<template>
    <div class="private-message-component">
        <h3 class="title" v-if="titleVisible">
            <template v-if="$t('lang') === 'en'">
                {{ $t('field.append') }}{{ letterText }}{{ $t('addReceiver.give') }}<strong>{{ toUser || 'Ta' }}</strong>
            </template>
            <template v-else-if="['zh','ru'].includes($t('lang'))">
                {{ $t('addReceiver.give') }}<strong>{{ toUser || 'Ta' }}</strong>{{ $t('field.append') }}{{ letterText }}
            </template>
        </h3>
        <el-input
            type="textarea"
            v-model="content"
            class="message-content"
            :placeholder="`${$t('addReceiver.saySomething')}...`"
            :rows="4"
            :disabled="!isCanModify || disabled"
            :maxlength="maxContentLength"
        >
        </el-input>

        <!--添加文档-->
        <div class="img-wrap" v-if="isShowAddFile">
            <div class="header">
                <span @click="uploadFn(false)" class="fl add-img" :class="{disabled}">
                    <span class="add"><b class="el-icon-ssq-jia"></b> {{ $t('addReceiver.addImage') }}</span>
                </span>
                <ConfigItem v-if="['edit','use','complete','update'].includes(type)" :type="type" :error="getErr('signInstructionDocumentInfo')" :item="communicateInfo.signInstructionDocumentInfo"></ConfigItem>
            </div>
            <div class="tip">{{ $t('addReceiver.addImageTips', { num: maxFileLength }) }}</div>
            <FileList
                :fileList="fileList"
                :isCanModify="isCanModify"
                :disabled="disabled"
                :type="type"
                @removeFile="removeFile"
                :canPreview="true"
                @changeConfig="changeConfig('signInstructionOriginDocumentInfo', $event)"
            >
            </FileList>
        </div>
        <!--添加源文档-->
        <div class="img-wrap" v-if="isShowAddSourceFile">
            <div class="header">
                <span @click="uploadFn(true)" class="fl add-img" :class="{disabled}">
                    <span class="add"><b class="el-icon-ssq-jia"></b> {{ $t('addReceiver.addSourceFile') }}</span>
                </span>
                <ConfigItem v-if="['edit','use','complete','update'].includes(type)" :type="type" :error="getErr('signInstructionOriginDocumentInfo')" :item="communicateInfo.signInstructionOriginDocumentInfo"></ConfigItem>
            </div>
            <div class="tip">{{ $t('addReceiver.addSourceFileTips', { num: maxSourceFileLength }) }}</div>
            <FileList
                :fileList="sourceFileList"
                :isCanModify="isCanModify"
                :disabled="disabled"
                :type="type"
                @removeFile="removeFile"
                @changeConfig="changeConfig('signInstructionOriginDocumentInfo', $event)"
            >
            </FileList>
        </div>
        <!-- 添加压缩文件 -->
        <div class="img-wrap" v-if="isShowAddCompressedFile">
            <div class="header">
                <div class="fl add-img" :class="{disabled}">
                    <el-upload
                        v-if="['approval','complete','update'].includes(type)"
                        ref="fileUploadApprove"
                        class="add"
                        :action="urlObject.compressedFileUploadUrl"
                        :headers="uploadHeaders"
                        :limit="1"
                        accept=".zip"
                        :show-file-list="false"
                        :before-upload="beforeFileUpload"
                        :on-success="onFileUploadSuccess"
                        :on-error="onFileUploadError"
                        :on-progress="onFileProgress"
                    >
                        <span @click="clickCompressedFile">
                            <b class="el-icon-ssq-jia"></b> {{ $t('addReceiver.addFile') }}</span>
                    </el-upload>
                    <span v-else class="add" @click="addCompressedFile"><b class="el-icon-ssq-jia"></b> {{ $t('addReceiver.addFile') }}</span>
                </div>
                <ConfigItem v-if="['edit','use','complete','update'].includes(type)" :type="type" :error="getErr('signInstructionZipInfo')" :item="communicateInfo.signInstructionZipInfo"></ConfigItem>
            </div>
            <div class="tip">{{ $t(type==='approval'?'addReceiver.addFileTipsApproval': 'addReceiver.addFileTips',{size:compressedFileMaxSize}) }}</div>
            <!-- 压缩文件 -->
            <FileList v-if="compressedFile.fileId"
                :fileList="[{...compressedFile,percentage:uploadFilePercentage}]"
                :isCanModify="isCanModify"
                :disabled="disabled"
                @removeFile="removeCompressedFile"
                @changeConfig="changeConfig('signInstructionZipInfo', $event)"
                :type="type"
            ></FileList>
        </div>
        <el-dialog
            :title="$t('addReceiver.selectFile')"
            :visible.sync="fileListVisible"
        >
            <div v-if="uploadFileList.length" class="file-list">
                <div class="li-item" v-for="(file) in uploadFileList" :key="file.fileId">
                    <el-radio v-model="selectedFileId" :label="file.fileId">
                        <span>{{ file.fileName }}</span>
                    </el-radio>
                </div>
            </div>
            <div v-else class="file-list-empty">{{ $t('addReceiver.emptyFile') }}</div>
            <span slot="footer" class="dialog-footer">
                <el-upload
                    style="display: inline-block;margin-right: 20px;"
                    ref="fileUpload"
                    :action="urlObject.compressedFileUploadUrl"
                    :headers="uploadHeaders"
                    :before-upload="beforeFileUpload"
                    :on-success="onFileUploadSuccess"
                    :on-error="onFileUploadError"
                    :on-progress="onFileProgress"
                    multiple
                    accept=".zip"
                    :show-file-list="false"
                >
                    <el-progress
                        class="file-list-upload"
                        v-if="fileUploadLoading"
                        :width="40"
                        :stroke-width="3"
                        type="circle"
                        :percentage="uploadFilePercentage"
                        :show-text="true"
                    ></el-progress>
                    <el-button>{{ $t('addReceiver.uploadFile') }}</el-button>
                </el-upload>
                <el-button type="primary" @click="confirmSelectedCompressedFile">{{ $t('addReceiver.confirm') }}</el-button>
            </span>
        </el-dialog>

        <el-upload
            ref="uploadFile"
            :action="urlObject.uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="onUploadSuccess"
            :on-error="onUploadError"
            :on-progress="onProgress"
            multiple
            accept=".doc,.docx,image/png, image/jpeg, image/jpeg, application/pdf, application/docx, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            :show-file-list="false"
        >
        </el-upload>
    </div>
</template>

<script>

import { mapGetters, mapMutations, mapState } from 'vuex';
import { getCompressedFileList, getCompressedFileMaxSize, getSenderCompressedFileMaxSize } from 'src/api/send/index';
import ConfigItem from './ConfigItem/index.vue';
import FileList from './FileList/index.vue';

export default {
    components: {
        ConfigItem,
        FileList,
    },
    props: {
        titleVisible: {
            type: Boolean,
            default: false,
        },
        saveBtnVisible: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: 'edit', // 编辑edit，使用use 审批approval，合同详情页补充 complete
        },
        toUser: String,
        maxContentLength: {
            type: Number,
            default: 255,
        },
        maxFileLength: {
            type: Number,
            default: 3,
        },
        maxSourceFileLength: {
            type: Number,
            default: 3,
        },
        disabled: Boolean,
        communicateInfo: {
            type: Object,
            default: () => {
            },
        },
        errors: {
            type: Array,
            default: () => [],
        },
        receiverId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            content: '',
            uploadHeaders: {
                Authorization: `bearer ${this.$cookie.get('access_token')}`,
                // 'x-authenticated-userid': '{"userId":2667417019430782979,"chosenEntId":2674414435103197187,"empId":2674414435187083265,"developerId":1}',
            },
            fileList: [],
            sourceFileList: [],
            compressedFile: {}, // 压缩文件

            letterText: ['edit', 'use', 'complete'].includes(this.type) ? this.$t('field.privateLetter') : this.$t('field.signNeedKnow'), // 审批的时候叫私信，签署的都叫签约须知
            keepSource: false, // 保持源文件
            fileListVisible: false, // 压缩文件上传列表弹窗显示与否
            uploadFileList: [], //  压缩文件上传列表
            selectedFileId: '', // 压缩文件上传列表选中的fileId
            fileUploadLoading: false,
            uploadFilePercentage: 0, // 上传进度
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
            currentEntId: state => state.commonHeaderInfo.currentEntId,
            compressedFileMaxSize: state => state.template.signRequirementFileMaxSize,
            ifCrossPlatform: state => state.template.ifCrossPlatform,
        }),
        // 是否是混3
        hybridVersionIsGama() {
            return !!this.hybridServer && this.$hybrid.isGamma(); // 是否是混3
        },
        // 乐高城开启开关,压缩文件使用时和补全时可以隐藏
        isShowAddCompressedFile() {
            return this.checkFeat.signNoticeAttachment && !(['complete', 'use'].includes(this.type) && !this.communicateInfo.signInstructionZipInfo.ifDisplayConfig) && !this.ifCrossPlatform;
        },
        // 文档使用时和补全时可以隐藏
        isShowAddFile() {
            return !(['complete', 'use'].includes(this.type) && !this.communicateInfo.signInstructionDocumentInfo.ifDisplayConfig) && !this.ifCrossPlatform;
        },
        // 源文档使用时和补全时可以隐藏
        isShowAddSourceFile() {
            return !(['complete', 'use'].includes(this.type) && !this.communicateInfo.signInstructionOriginDocumentInfo.ifDisplayConfig) && !this.ifCrossPlatform;
        },
        // 编辑、使用阶段签约须知，审批阶段审批须知3个接口
        urlObject() {
            const templateId = this.$route.params.templateId;
            const draftId = this.$route.params.draftId;
            const contractId = this.$route.params.contractId;
            const mapUrl = {
                edit: {
                    uploadUrl: `/template-api/v2/templates/${templateId}/private-letter/upload?keep=${this.keepSource}`,
                    compressedFileUploadUrl: `/template-api/v2/templates/${templateId}/instructions_appendix/upload`,
                },
                use: {
                    uploadUrl: `/template-api/v2/draft/${draftId}/private-letter/upload?keep=${this.keepSource}`,
                    compressedFileUploadUrl: `/template-api/v2/draft/${draftId}/instructions_appendix/upload`,
                },
                approval: {
                    uploadUrl: `/template-api/v2/draft/${draftId}/private-letter/upload?keep=${this.keepSource}`,
                    compressedFileUploadUrl: `/template-api/v2/draft/${draftId}/private-letter-zip/upload`,
                },
                complete: {
                    uploadUrl: `/contract-api/signer-instruction/${contractId}/${this.receiverId}/upload?fileType=${this.keepSource ? 'NOTICE_ORIGIN_DOCUMENT' : 'NOTICE_DOCUMENT'}`,
                    compressedFileUploadUrl: `/contract-api/signer-instruction/${contractId}/${this.receiverId}/upload?fileType=NOTICE_ZIP_FILE`,
                },
                update: {
                    uploadUrl: `/contract-api/signer-instruction/${contractId}/${this.receiverId}/upload?fileType=${this.keepSource ? 'NOTICE_ORIGIN_DOCUMENT' : 'NOTICE_DOCUMENT'}`,
                    compressedFileUploadUrl: `/contract-api/signer-instruction/${contractId}/${this.receiverId}/upload?fileType=NOTICE_ZIP_FILE`,
                },
            };
            return mapUrl[this.type];
        },
        ...mapState('template', ['canModifyWhenUsed', 'templateStatus']),
        // 是否可以修改私信内容
        isCanModify() {
            // todo 暂时返回true
            return true;
        },
        currentFileList: {
            get() {
                return this.keepSource ? this.sourceFileList : this.fileList;
            },
            set(value) {
                this[this.keepSource ? 'sourceFileList' : 'fileList'] = value;
            },
        },
    },
    watch: {
        communicateInfo: {
            deep: true,
            immediate: true,
            handler(newValue) {
                if (this.type !== 'approval') { // 审批须知每次进来都是清空的
                    this.content = newValue.privateLetter;
                    this.fileList = newValue.signInstructionDocumentInfo.privateLetterFileInfos || [];
                    this.sourceFileList = newValue.signInstructionOriginDocumentInfo.privateLetterFileInfos || [];
                    this.compressedFile = {
                        fileId: newValue.signInstructionZipInfo.instructionsAppendixId,
                        fileName: newValue.signInstructionZipInfo.instructionsAppendixName,
                    };
                }
            },
        },
        content() {
            this.emitData();
        },
    },
    methods: {
        ...mapMutations('template', ['setSignRequirementFileSize']),
        getErr(key) {
            let res = '';
            this.errors.every(item => {
                if (item.fieldName === key) {
                    res = item.errorInfo;
                    return false;
                }
                return true;
            });
            return res;
        },
        spliceErr(key) {
            this.$emit('spliceErr', key);
        },
        // 点击提交文档活源文档
        uploadFn(keepSource) {
            if (this.type === 'approval') {
                const isBatchSend = this.$store.state.template.isBatchSend;
                this.$sensors.track({
                    eventName: 'Ent_ContractSendDetailWindow_BtnClick',
                    eventProperty: {
                        page_name: '指定签署位置',
                        ...(!isBatchSend && { template_type: this.$route.query.isDynamic === 'true' ? '动态模板' : '静态模板' }),
                        is_batch_send: isBatchSend,
                        window_name: '设置审批流',
                        icon_name: keepSource ? '添加源文档' : '添加文档',
                    },
                });
            }
            this.keepSource = keepSource;
            this.spliceErr(this.keepSource ? 'signInstructionOriginDocumentInfo' : 'signInstructionDocumentInfo');
            this.$refs.uploadFile.$refs['upload-inner'].handleClick();
        },
        // 点击压缩文档
        clickCompressedFile() {
            if (this.type === 'approval') {
                const isBatchSend = this.$store.state.template.isBatchSend;
                this.$sensors.track({
                    eventName: 'Ent_ContractSendDetailWindow_BtnClick',
                    eventProperty: {
                        page_name: '指定签署位置',
                        ...(!isBatchSend && { template_type: this.$route.query.isDynamic === 'true' ? '动态模板' : '静态模板' }),
                        is_batch_send: isBatchSend,
                        window_name: '设置审批流',
                        icon_name: '添加压缩文档',
                    },
                });
            }
        },
        // 获取乐高城配置的签约须知附件大小
        getConfigCompressedFileMaxSize() {
            if (this.isShowAddCompressedFile) {
                (this.type === 'complete' ? getSenderCompressedFileMaxSize(this.$route.params.contractId) : getCompressedFileMaxSize()).then(res => {
                    this.setSignRequirementFileSize(+res.data);
                }).catch(() => {
                    // 发生异常时按照 200M 默认值处理
                    this.setSignRequirementFileSize(200);
                });
            }
        },
        setCompressedFileList() {
            const templateId = this.$route.params.templateId;
            let optType = 'draft';
            let id = this.$route.params.draftId;
            if (this.type === 'edit') {
                optType = 'template';
                id = templateId;
            }
            getCompressedFileList(optType, id).then((res) => {
                this.uploadFileList = res.data;
                this.selectedFileId = res.data[0] && res.data[0].fileId || '';
            });
        },
        // 添加压缩文件
        addCompressedFile() {
            this.$refs['fileUpload'].clearFiles();
            this.fileListVisible = true;
            this.setCompressedFileList();
        },
        beforeFileUpload(file) {
            this.spliceErr('signInstructionZipInfo');
            if (file.size / 1024 / 1024 > this.compressedFileMaxSize) {
                this.$MessageToast.error(this.$t('field.maximumSize', { size: this.compressedFileMaxSize }));
                return false;
            }
            if (['edit', 'use'].includes(this.type)) {
                const uploadedFileListNames = this.uploadFileList.map(file => file.fileName);
                if (uploadedFileListNames.includes(file.name)) {
                    this.$MessageToast.error(this.$t('field.uploadRepeatFileTip'));
                    return false;
                }
            }
            this.fileUploadLoading = true;
            this.uploadFilePercentage = 0;
        },
        onFileUploadSuccess(res) {
            if (!res) {
                this.$MessageToast.error(this.$t('field.uploadServerFailure'));
                this.compressedFile = {};
                return;
            }
            if (['approval', 'complete', 'update'].includes(this.type)) {
                this.compressedFile = res;
                this.emitData();
            } else {
                this.setCompressedFileList();
                this.$refs['fileUpload'].clearFiles();
            }
            this.fileUploadLoading = false;
            this.uploadFilePercentage = 100;
        },
        onFileProgress(event, file) {
            this.uploadFilePercentage = parseInt(file.percentage);
        },
        onFileUploadError(err) {
            let message = '';
            try {
                const messageObj = JSON.parse(err.message);
                message = messageObj.message;
            } catch (e) {
                message = '';
            }
            this.fileUploadLoading = false;
            this.$MessageToast.error(`${this.$t('field.uploadFailure')}:${message}`);
            this.compressedFile = {};
            this.$refs['fileUpload'].clearFiles();
        },
        removeCompressedFile() {
            this.compressedFile = {};
            this.emitData();
            if (['approval', 'complete'].includes(this.type)) {
                return this.$refs['fileUploadApprove'].clearFiles();
            }
            this.$refs['fileUpload'].clearFiles();
        },
        confirmSelectedCompressedFile() {
            if (!this.selectedFileId) {
                return this.$MessageToast.error(this.$t('addReceiver.pleaseSelectOne'));
            }
            this.compressedFile = (this.uploadFileList || []).filter(item => item.fileId === this.selectedFileId)[0];
            this.emitData();
            this.fileListVisible = false;
        },
        beforeUpload(file) {
            const maxFileLen = this.keepSource ? this.maxSourceFileLength : this.maxFileLength;
            if (this.currentFileList.length >= maxFileLen) {
                this.$MessageToast.error(this.$t('addReceiver.fileMax'));
                return false;
            }
            if (file.size / 1024 / 1024 > 5) {
                this.$MessageToast.error(this.$t('field.maximum5M'));
                return false;
            }
            // beforeUpload 存放数据，for 上传loading，在上传成功之前 以uid为标识符去判断文档的唯一性
            this.currentFileList.push({
                name: file.name,
                uid: file.uid,
                percentage: 0,
            });
        },
        onUploadSuccess(res, file) {
            if (!res) {
                this.$MessageToast.error(this.$t('field.uploadServerFailure'));
                const index = this.currentFileList.findIndex(f => f.uid === file.uid);
                this.removeFile(this.currentFileList, index);
                return;
            }
            (typeof this.currentFileList === 'undefined') && (this.currentFileList = []);
            this.currentFileList = this.currentFileList.map(f => {
                if (file.uid === f.uid) {
                    f.percentage = 100;
                    f = {
                        ...f,
                        ...res,
                    };
                }
                return f;
            });
            this.emitData();
        },
        onUploadError(err, file) {
            const errorMsg = err.message;
            let errorObj = {
                message: this.$t('field.uploadFailure'),
            };

            try {
                errorObj = JSON.parse(errorMsg);
            } catch (e) {
                console.log(e);
            }

            this.$MessageToast.error(errorObj.message);
            const index = this.currentFileList.findIndex(f => f.uid === file.uid);
            this.removeFile(this.currentFileList, index);
        },
        onProgress(event, file) {
            const index = this.currentFileList.findIndex(f => f.uid === file.uid);
            if (index === -1) {
                this.currentFileList.push({
                    name: file.name,
                    uid: file.uid,
                    percentage: 0,
                });
            } else {
                this.$set(this.currentFileList, index, {
                    ...this.currentFileList[index],
                    percentage: file.percentage,
                });
            }
        },
        removeFile(fileList, index) {
            fileList.splice(index, 1);
            this.emitData();
        },
        emitData() {
            if (['edit', 'use', 'complete', 'update'].includes(this.type)) {
                this.$emit('change', {
                    privateLetter: this.content,
                    signInstructionDocumentInfo: {
                        ...this.communicateInfo.signInstructionDocumentInfo,
                        privateLetterFileInfos: this.fileList,
                        privateLetterFileList: this.fileList.map(i => i.fileId),
                    },
                    signInstructionOriginDocumentInfo: {
                        ...this.communicateInfo.signInstructionOriginDocumentInfo,
                        privateLetterFileInfos: this.sourceFileList,
                        privateLetterFileList: this.sourceFileList.map(i => i.fileId),
                    },
                    signInstructionZipInfo: {
                        ...this.communicateInfo.signInstructionZipInfo,
                        instructionsAppendixId: this.compressedFile.fileId || null,
                        instructionsAppendixName: this.compressedFile.fileName || null,
                    },
                });
            } else {
                this.$emit('change', {
                    privateLetter: this.content,
                    privateLetterFileList: this.fileList,
                    privateLetterOriginFileList: [...this.sourceFileList],
                    instructionsAppendixId: this.compressedFile.fileId,
                });
            }
        },
        changeConfig(type, newValue) {
            const changeContent = {};
            changeContent[`communicateInfo.${type}`] = newValue;
            this.$emit('changeConfig', changeContent);
        },
    },
    mounted() {
        this.getConfigCompressedFileMaxSize();
    },
    beforeDestroy() {
        this.keepSource = false;
    },
};
</script>

<style lang="scss" scoped>
.private-message-component {
  font-size: 12px;
  .title {
    color: $--color-text-primary;
  }
  .message-content {
    margin: 10px 0;
    box-sizing: border-box;
  }
  .privateLetter-footer {
    color: $--color-text-secondary;
    padding: 0 0 10px;
  }
  .file-list-upload{
    position: relative;
    top: 13px;
    right: 10px;
  }
  .is-approval{
    margin-left: 20px;
  }
  .img-wrap {
    .header{
      height: 24px;
      line-height: 24px;
    }
    .add-img {
      &.disabled{
          pointer-events: none;
        .add{
          cursor: auto;
          color: $--color-info
        }
      }
      display: flex;
      justify-content: space-between;
      align-items: center;
      .add {
        color: $--color-primary-light-2;
        display: inline-block;
        cursor: pointer;
        b {
          font-weight: bold;
        }
      }

    }
    .tip {
      color: $--color-text-secondary;
    }
    .file-list {
      width: 305px;
      .li-item {
        width: 365px;
        margin-bottom: 10px;
        position: relative;
        display: flex;
        align-items: center;
        .close {
          color: $--color-text-placeholder;
          margin-left: 16px;
          cursor: pointer;
          position: absolute;
          right: 0;
          &:hover {
            color: $--color-text-secondary;
          }
        }
        span {
          display: inline-block;
          width: 300px;
          overflow : hidden;
          text-overflow: ellipsis;
          margin-left:  4px;
          word-break: break-all;
          cursor: pointer;
          &:hover {
            color: $theme-color;
          }
        }
        .el-progress {
          margin-left:  4px;
          width: 240px;
        }
        .el-progress-bar__inner {
          background: $theme-color;
        }
      }
    }
  }
  .save-btn {
    margin-top: 5px;
    float: none;
  }
}
</style>
