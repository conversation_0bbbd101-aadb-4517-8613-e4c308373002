<template>
    <section class="add-file" :class="{'mini-height': isUseStatus}">
        <div class="add-file-content">
            <CommonTip
                type="popover"
                v-if="!isUseStatus"
                v-model="popoverVisible"
                placement="top"
                width="600"
                trigger="click"
                :popper-class="`add-file-popper ${isEn}? en-popper`"
                :visible-arrow="false"
            >
                <div slot="content" class="add-file-selectbtn">
                    <p @click="clickAddFile('addBlank')" v-if="!isDynamic" class="add-file-blank">
                        <!-- 添加空白合同 -->
                        {{ $t('upload.addBlank') }}
                        <CommonTip
                            :content="$t('upload.blankTips')"
                            effect="light"
                            :placement="toolTipPlacement"
                        />
                    </p>
                    <!--动态模板、混3不支持单据合同-->
                    <p v-if="!isDynamic && !hybridVersionIsGama && isSupportBill">
                        <Upload
                            :requestUrl="uploadBillFileRequestUrl"
                            :uploadData="uploadData"
                            :hybridTarget="hybridTarget"
                            :multiple="true"
                            :upload-start="beforeUpload"
                            :upload-success="($event)=>handleUploadSuccess($event,'addBill')"
                            :upload-error="($event)=>handleUploadFail($event,'addBill')"
                            :isDynamic="isDynamic"
                            :isBill="true"
                        >

                            <span class="inline-block" @click="clickAddFile('addBill')">
                                <!-- 添加单据合同 -->
                                {{ $t('upload.addBill') }}
                                <CommonTip effect="light" :placement="toolTipPlacement" popper-class="add-file-bill-tooltip">
                                    <template #content>
                                        <ul class="add-file-bill-tip">
                                            <li>{{ $tc('upload.billTip1', 1) }}</li>
                                            <li>{{ $tc('upload.billTip1', 2) }}</li>
                                        </ul>
                                        <ul class="add-file-bill-tip tip2">field1
                                            <li>{{ $tc('upload.billTip2', 0, {text: $t('upload.field1')}) }}</li>
                                            <li>{{ $tc('upload.billTip2', 1, {text: $t('upload.field2')}) }}</li>
                                            <li>{{ $tc('upload.billTip2', 2) }}</li>
                                        </ul>
                                        <img style="width:300px" src="~img/template/bill-case.png" alt="出货单示例图">
                                    </template>
                                </CommonTip>
                            </span>
                        </Upload>
                    </p>
                    <p v-if="checkFeat.wordBill">
                        <Upload
                            :requestUrl="uploadBillFileRequestUrl"
                            :uploadData="uploadData"
                            :hybridTarget="hybridTarget"
                            :multiple="true"
                            :upload-start="beforeUpload"
                            :upload-success="($event)=>handleUploadSuccess($event,'addWordBill')"
                            :upload-error="($event)=>handleUploadFail($event,'addWordBill')"
                            :isDynamic="true"
                        >
                            <span class="inline-block" @click="clickAddFile('addWordBill')">
                                <!-- 上传word单据模板 -->
                                {{ $t('upload.addWordBill') }}
                                <CommonTip
                                    effect="light"
                                    :placement="toolTipPlacement"
                                    popper-class="add-file-bill-tooltip"
                                >
                                    <template #content>
                                        <ul class="add-file-bill-tip">
                                            <li>{{ $tc('upload.wordBillTip1', 1) }}</li>
                                            <li>{{ $tc('upload.wordBillTip1', 2) }}</li>
                                            <li>{{ $tc('upload.wordBillTip2', 1) }}</li>
                                        </ul>
                                        <ul class="add-file-bill-tip tip2">
                                            <li>{{ $tc('upload.newBillTip1') }}</li>
                                            <li>{{ $tc('upload.newBillTip2') }}</li>
                                            <li>{{ $t('upload.newBillTip3') }}</li>
                                            <li>{{ $t('upload.newBillTip4') }}</li>
                                            <li>3. {{ $t('upload.exampleAsFollows') }}</li>
                                        </ul>
                                        <img style="width:400px" src="~img/template/word-bill.png" alt="出货单示例图">
                                    </template>
                                </CommonTip>
                            </span>
                        </Upload>
                    </p>
                    <p>
                        <Upload
                            :requestUrl="uploadLocalFileRequestUrl"
                            :uploadData="uploadData"
                            :hybridTarget="hybridTarget"
                            :multiple="true"
                            :upload-start="beforeUpload"
                            :upload-success="($event)=>handleUploadSuccess($event,'addLocal')"
                            :upload-error="($event)=>handleUploadFail($event,'addLocal')"
                            :isDynamic="isDynamic"
                            :isOfdTemplate="isOfdTemplate"
                        >
                            <span class="inline-block" @click="clickAddFile('addLocal')">
                                <!-- 上传本地文档 -->
                                {{ $t('upload.addLocal') }}
                            </span>
                        </Upload>
                    </p>
                </div>
                <el-button slot="reference" type="primary" class="add-file-btn">
                    <!-- 上传文档 -->
                    {{ $t('upload.uploadDoc') }}
                </el-button>
            </CommonTip>

            <Upload
                v-else
                :requestUrl="uploadLocalFileRequestUrl"
                :uploadData="uploadData"
                :hybridTarget="hybridTarget"
                :multiple="true"
                :upload-start="beforeUpload"
                :upload-success="($event)=>handleUploadSuccess($event,'addFile')"
                :upload-error="($event)=>handleUploadFail($event,'addFile')"
                :isOfdTemplate="isOfdTemplate"
            >
                <el-button type="primary" class="add-file-btn" @click="clickAddFile('addFile')">
                    <!-- 上传文档 -->
                    {{ useStatusUploadName }}
                </el-button>
            </Upload>
            <p>
                <!-- 文件支持jpg、png、doc、docx、pdf 等格式 -->
                {{ uploadTip }}
            </p>
        </div>
        <AddBlankDialog
            v-if="dialogVisible"
            :requestUrl="addBlankRequestUrl"
            @add-blank-doc-suc="addBlankDocSuc"
            @close="dialogVisible=false"
        >
        </AddBlankDialog>
    </section>
</template>

<script>
import Upload from 'components/upload';
import AddBlankDialog from 'components/addBlankDialog';
import { mapGetters } from 'vuex';
export default {
    name: 'AddFileBtn',
    components: {
        Upload,
        AddBlankDialog,
    },
    props: {
        // 空白文档请求接口
        addBlankRequestUrl: {
            type: String,
            default: '',
        },
        // 本地文档请求接口
        uploadLocalFileRequestUrl: {
            type: String,
            default: '',
        },
        // 单据合同文档请求接口
        uploadBillFileRequestUrl: {
            type: String,
            default: '',
        },
        // 乐高成是否开通了单据合同
        isSupportBill: {
            type: Boolean,
            default: false,
        },
        // 是否是混3
        hybridVersionIsGama: {
            type: Boolean,
            default: false,
        },
        // 本地文档混合云3.0请求接口的target
        hybridTarget: {
            type: String,
            default: '',
        },
        // 本地文档请求接口需要上传的参数
        uploadData: {
            type: Object,
            default: () => {},
        },
        isUseStatus: {
            type: Boolean,
            default: false,
        },
        isDynamic: {
            type: Boolean,
            default: false,
        },
        isOfdTemplate: {
            type: Boolean,
            default: false,
        },
        // 模板使用时上传按钮的文案
        useStatusUploadName: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            contractTitle: '',
            popoverVisible: false,
            dialogVisible: false,
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
        isEn() {
            return this.$i18n.locale === 'en';
        },
        toolTipPlacement() {
            return this.$i18n.locale === 'ar' ? 'left' : 'right';
        },
        uploadTip() {
            if (this.isDynamic) {
                return this.$t('upload.dynamicTypeTips');
            } else if (this.isOfdTemplate) {
                return this.$t('upload.ofdTypeTips');
            } else if (this.hybridVersionIsGama) {
                return this.$t('upload.gamaTypeTips');
            } else {
                return this.$t('upload.typeTips');
            }
        },
    },
    methods: {
        clickAddFile(type) {
            if (type === 'addBlank') {
                this.dialogVisible = true;
            } else if (type !== 'addFile') {
                this.popoverVisible = false;
            }
            this.$sensors.track({
                eventName: this.isUseStatus ? 'Ent_ContractSendDetail_BtnClick' : 'Ent_TemplateCreate_BtnClick',
                eventProperty: {
                    page_name: this.isUseStatus ? '设置文档' : '上传文档',
                    template_type: this.isDynamic ? '动态模板' : '静态模板',
                    first_category: '添加合同',
                    ...(this.isUseStatus && { is_batch_send: false }),
                    icon_name:
                        ({ addBlank: '添加待上传文档', addBill: '添加单据合同', addLocal: '上传本地文档', addFile: '继续上传文档', addWordBill: '添加word单据模板' })[type],
                },
            });
        },
        beforeUpload() {
            this.$emit('before-upload');
        },
        handleUploadSuccess(res, type) {
            this.$emit('add-local-doc-suc', res);
            this.$sensors.track({
                eventName: this.isUseStatus ? 'Ent_ContractSendDetail_Result' : 'Ent_TemplateCreate_Result',
                eventProperty: {
                    page_name: this.isUseStatus ? '设置文档' : '上传文档',
                    template_type: this.isDynamic ? '动态模板' : '静态模板',
                    first_category: '添加合同',
                    ...(this.isUseStatus && { is_batch_send: false }),
                    is_success: true,
                    icon_name: ({ addBill: '添加单据合同', addLocal: '上传本地文档', addFile: '继续上传文档', addWordBill: '添加word单据模板' })[type],
                },
            });
        },
        handleUploadFail(res, type) {
            this.$emit('add-doc-fail', res);
            this.$sensors.track({
                eventName: this.isUseStatus ? 'Ent_ContractSendDetail_Result' : 'Ent_TemplateCreate_Result',
                eventProperty: {
                    page_name: this.isUseStatus ? '设置文档' : '上传文档',
                    template_type: this.isDynamic ? '动态模板' : '静态模板',
                    first_category: '添加合同',
                    ...(this.isUseStatus && { is_batch_send: false }),
                    is_success: false,
                    icon_name:
                        ({ addBill: '添加单据合同', addLocal: '上传本地文档', addFile: '继续上传文档', addWordBill: '添加word单据模板' })[type],
                    request_url: res.config.url,
                    fail_reason: res.response?.data?.message || res.message,
                    fail_error_code: res.response?.data?.code,
                    fail_http_code: res.response?.status,
                },
            });
        },
        addBlankDocSuc(event) {
            this.$emit('add-blank-doc-suc', event);
        },
    },
};
</script>

<style lang="scss">
.en-popper.add-file-popper{
     width: 250px !important;
}
.add-file {
    border: 1px dashed $--color-info;
    text-align: center;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items:center;
    &.mini-height {
        padding: 20px 0;
    }
    &-btn {
        font-size: 16px;
        height: 42px;
    }
    p {
        font-size: 12px;
        color: $--color-info;
        margin-top: 12px;
    }
    .el-dialog .el-dialog__header {
        text-align: left;
    }
    .add-blank-input {
        .el-input {
            display: inline-block;
            margin-left: 10px;
            width: 360px;
        }
    }
    .add-blank-checkbox {
            word-break: break-word;
    white-space: normal;
        margin-top: 30px;
        text-align: left;
    }
    .add-blank-tip {
        text-align: left;
        line-height: 24px;
    }
}
.add-file-selectbtn {
    .tips {
        position: absolute;
        top: 55px;
        // left: 145px;
        margin-left: 3px;
    }
}
.add-file-blank {
    position: relative;
    .tips {
        top: 10px;
    }
}
.add-file-bill-tooltip {
    width: 460px;
    .add-file-bill-tip {
        line-height: 20px;
        margin-bottom: 10px;
        color: #333;
    }
    .tip2 {
        color: #999;
    }
}
.add-file-popper {
    border: 1px solid $--border-color-lighter;
    box-shadow: 0 2px 4px 0 $--border-color-lighter;
    border-radius: 4px;
    width: 90px !important;
    padding: 10px 0 !important;
    p {
        text-align: center;
        font-size: 14px;
        color: $--color-text-regular;
        cursor: pointer;
        line-height: 34px;
        &:hover {
            background: $--color-primary-light-9;
        }
    }
}
</style>
