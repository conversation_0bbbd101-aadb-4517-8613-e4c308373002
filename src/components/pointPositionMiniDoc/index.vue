<template>
    <div class="doc-mini-documents">
        <div class="contract-swtich" v-if="templateStatus=== 'use' && contractNums > 1">
            <!-- excel导入的数据大于500条时，显示 -->
            <p v-if="contractNums > 500" class="contract-swtich__counts">{{ $t('pointPositionMiniDoc.superTotalContract', {num: contractNums}) }}</p>
            <p v-else class="contract-swtich__counts">{{ $t('pointPositionMiniDoc.totalContract', {num: contractNums}) }}</p>
            <p class="contract-swtich__select">
                <el-select v-model="currentContractIndex" @change="onChangeContractIndex">
                    <el-option v-for="n in selectNums" :label="n" :value="n" :key="`i${n}`"></el-option>
                </el-select>
                {{ $t('pointPositionMiniDoc.totalContractAfter') }}
                <CommonTip class="item"
                    effect="dark"
                    :content="$t('pointPositionMiniDoc.contractSwitchTip')"
                    placement="top"
                />
            </p>
        </div>
        <div class="title">
            {{ $t('pointPositionMiniDoc.document') }}
            <!-- 共xx份 -->
            <span class="fr">{{ $t('pointPositionMiniDoc.documentsLength', {documentsLength: documents.length}) }}</span>
        </div>
        <ul>
            <li class="doc" :class="{active:currentDocIndex === docIndex}" v-for="(doc, docIndex) in documents" :key="docIndex">
                <div class="doc-title">{{ doc.documentName }}（{{ doc.pageSize }}{{ $t('pointPositionMiniDoc.page') }}）</div>
                <div class="doc-thumbnail-container">
                    <div
                        @click="handleClickMiniImage(docIndex)"
                        class="doc-thumbnail"
                        :style="{ backgroundImage: `url(${doc.image})`}"
                    >
                    </div>
                    <i v-if="isApproval && doc.approvalStatus !== 'NONE'"
                        class="status-icon"
                        :class="doc.approvalStatus === 'APPROVED' ? 'el-icon-ssq-hetongxiangqing-wancheng1' :
                            'el-icon-ssq-hetongxiangqing-shenpizhong1'"
                    ></i>
                </div>
                <div class="doc-totalPage" :class="{ ['lang_' + $t('lang')]: true }">
                    <template v-if="doc.documentPages.length > 1">
                        <el-input
                            v-if="currentDocIndex === docIndex"
                            v-model="page"
                            :placeholder="$t('pointPositionMiniDoc.pager')"
                            @blur="onBlur()"
                            @keyup.enter.native="onBlur()"
                        >
                        </el-input>
                        <el-input v-else :disabled="true" :placeholder="$t('pointPositionMiniDoc.pager')">
                        </el-input>
                        / </template>{{ doc.documentPages.length }}{{ $t('pointPositionMiniDoc.page') }}
                </div>
                <div v-if="doc.attachments && doc.attachments.length" class="doc-attachments">
                    <p class="doc-attachments-title">附属文件：</p>
                    <div class="doc-attachments-line" v-for="attachment in doc.attachments" :key="attachment.attachmentId">
                        {{ attachment.attachmentName }}<span>（{{ attachment.pageSize }}页）</span>
                    </div>
                </div>
                <div class="next-sign-position" v-if="currentDocIndex === docIndex" @click="$emit('next-sign-position')">
                    {{ $t('pointPositionMiniDoc.findNextSignPosition') }}
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
import { mapState, mapMutations, mapGetters } from 'vuex';
import { getContractSendCount } from '@/api/template/pointPosition';
let timer;

export default {
    props: {
        documents: {
            type: Array,
            default() {
                return [];
            },
        },
        isApproval: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            page: 1,
            contractNums: 1,
            distinctEntSignerCount: 0,
            selectNums: 1,
            currentContractIndex: 1,
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
        ...mapState('template', ['currentDocIndex', 'currentPageIndex', 'templateStatus']),
    },
    watch: {
        currentDocIndex() {
            this.setCurrentPageIndex(0);
        },
        currentPageIndex(v) {
            this.page = v + 1;
        },
    },
    methods: {
        ...mapMutations('template', ['setCurrentPageIndex', 'setExcelRowCount', 'setContractNum', 'setDistinctEntSignerCount']),
        handleClickMiniImage(docIndex) {
            clearTimeout(timer);
            this.page = 1;
            this.$emit('change-doc', docIndex);
        },
        onChangeContractIndex(index) {
            this.$emit('change-contract', index - 1);
        },
        onBlur() {
            const _this = this;
            // 防止onblur与handleClickMiniImage同时执行
            timer = setTimeout(function() {
                _this.page = parseInt(_this.page) || 1; // 取整数
                _this.page = _this.page < 1 ? 1 : _this.page;
                const maxPage = _this.documents[_this.currentDocIndex].documentPages.length;
                _this.page = _this.page > maxPage ? maxPage : _this.page;
                _this.$emit('change-page', _this.page - 1);
            }, 120);
        },
        handleContractNum() {
            this.setContractNum(this.contractNums);
            this.setDistinctEntSignerCount(this.distinctEntSignerCount);
            const needVerify = this.checkFeat.annualVerify || this.checkFeat.customInfoVerify;
            // 需要校验企业签署人的数量超过500才提示
            if (this.distinctEntSignerCount > 500 && needVerify) {
                this.$confirm(this.$t('pointPositionMiniDoc.skipRiskTip'), this.$t('templateCommon.tip'), {
                    showCancelButton: false,
                    confirmButtonText: this.$t('templateCommon.understand'),
                });
            }
        },
    },
    created() {
        // 审批时不需要获取该数据
        if (this.templateStatus === 'use' && !this.isApproval) {
            getContractSendCount().then((res) => {
                this.contractNums = res.data.excelRowCount;
                this.distinctEntSignerCount = res.data.distinctEntSignerCount;
                this.selectNums = res.data.excelRowCount > 500 ? 500 : res.data.excelRowCount;
                this.setExcelRowCount(this.selectNums);
                this.handleContractNum();
            });
        }
    },
};
</script>

<style lang="scss">
    .doc-mini-documents {
        width: 210px;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        overflow: auto;
        font-size: 12px;
        box-shadow: 6px 0px 6px -6px $--border-color-light;
        border-left: 1px solid $border-color;
        [dir="rtl"] & {
            right: auto;
            left: 0;
            border-left: 0;
            border-right: 1px solid $border-color;
        }
        .contract-swtich {
            padding: 10px 15px 15px;
            border-bottom: 1px solid $border-color;
            font-size: 14px;
            color: $--color-text-primary;
            &__counts {
                line-height: 24px;
            }
            &__select .el-select{
                width: 80px;
            }
        }
        .title{
            line-height: 38px;
			padding: 0 15px;
			font-size: 14px;
			color: $--color-text-primary;
            border-bottom: 1px solid $border-color;
            .fr{
                font-size: 12px;
                color: $--color-info;
                [dir="rtl"] & {
                    float: left;
                }
            }
        }

        .doc {
            padding-top: 6px;
            padding-bottom: 10px;
            border-bottom: 1px solid $border-color;
            &.active {
                background: $--color-white;
                border:2px solid $theme-color;
                .doc-thumbnail-container{
                    background-image: url(~img/doc-active.png);
                }
            }
            .doc-title {
                position: relative;
                height: 18px;
                line-height: 18px;
                color: $--color-text-primary;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                padding: 6px 40px 6px 19px;
                // border-top: 1px solid $border-color;
            }
            .doc-totalPage {
                color: $--color-text-secondary;
                // margin-bottom: 10px;
                line-height: 30px;
                text-align: center;
                &.lang_ru {
                    line-height: 10px;
                }
                .el-input{
                    width: 52px;
                    .el-input__inner{
                        box-sizing: border-box;
                        height: 24px;
                        border-radius: 2px;
                        padding: 3px 5px;
                    }
                }
            }
            .doc-thumbnail-container {
                position: relative;
                margin: 5px auto;
                width: 132px;
                height: 161px;
                background-image: url(~img/doc.png);
                background-position: top left;
                background-repeat: no-repeat;
                background-size: 132px 161px;
                cursor: pointer;
                .doc-thumbnail {
                    width: 115px;
                    height: 144px;
                    background-repeat: no-repeat;
                    background-size: 115px 144px;
                    background-position: 4px 4px;
                    [dir=rtl] & {
                        background-position: -16px 4px;
                    }
                }
                .status-icon {
                    position: absolute;
                    top: 5px;
                    left: 0px;
                    font-size: 20px;
                }
                .el-icon-ssq-hetongxiangqing-wancheng1 {
                    color: $--color-success;
                }
                .el-icon-ssq-hetongxiangqing-shenpizhong1 {
                    color: $--color-danger;
                }
            }
            .doc-attachments{
                padding: 6px 0 6px 19px;
                line-height: 18px;
                font-weight: 400;
                .doc-attachments-title{
                    color: $--color-text-secondary;
                }
                .doc-attachments-line{
                    color: $--color-text-primary;
                    line-height: 26px;
                    span{
                        color: $--color-text-secondary;
                    }
                }
            }
            .next-sign-position {
                text-align: center;
                height: 24px;
                line-height: 24px;
                color: $--color-primary;
                font-size: 12px;
                cursor: pointer;
            }
        }
    }
</style>
