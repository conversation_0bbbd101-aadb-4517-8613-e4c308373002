
<template>
    <div class="cus-label-edit-cpn">
        <el-form ref="form" :model="form">
            <!-- 名称 -->
            <el-form-item :label="$t('customLabelEdit.labelName')" class="form-name">
                <!-- 必填 -->
                <el-input :placeholder="$t('customLabelEdit.require')"
                    v-model="form.labelName"
                    :minlength="1"
                    :maxlength="30"
                    :disabled="isDisabledName || isWordBillLabel"
                    @input="onValueChangeImmediate('labelName')"
                >
                </el-input>
            </el-form-item>
            <template v-if="labelType != 'CONFIRMATION_REQUEST_REMARK'">
                <template v-if="labelType != 'DATE'">
                    <!-- 内容填写人 -->
                    <el-form-item :label="$t('customLabelEdit.contentFiller')">
                        <el-radio-group
                            v-model="form.labelExtends.receiverFill"
                            @change="onChange('labelExtends','receiverFill')"
                            :disabled="fillerUnchangable && !isAppendedForDraft"
                        >
                            <div class="sender-fill-part fill-select">
                                <el-radio :label="false" v-show="!isAppendedForDraft">
                                    <!-- 发件人 -->
                                    {{ $t('customLabelEdit.sender') }}
                                    <CommonTip
                                        class="item"
                                        effect="dark"
                                        :content="$t('customLabelEdit.senderTip')"
                                        placement="top"
                                    />
                                </el-radio>
                                <!-- 发件人：存在补全角色时添加身份选择-->
                                <el-radio-group v-if="showEditorRadios"
                                    v-model="senderFillRoleId"
                                >
                                    <div>
                                        <el-radio label="1">
                                            {{ $t('sendedEdit.senderFill') }}
                                        </el-radio>
                                    </div>
                                    <div v-for="item in editors"
                                        :key="item.receiverId"
                                    >
                                        <el-radio :label="item.receiverId" :disabled="item.completerType === 2">
                                            <span>{{ $t('sendedEdit.fillByRole', {role: item.showName}) }}</span>
                                        </el-radio>
                                    </div>
                                </el-radio-group>
                            </div>
                            <div class="receiver-fill-part fill-select" v-if="!getIsUae">
                                <el-radio :label="true">
                                    <!-- 签署人 -->
                                    {{ $t('customLabelEdit.signer') }}
                                    <CommonTip
                                        class="item"
                                        effect="dark"
                                        :content="$t('customLabelEdit.signerTip')"
                                        placement="top"
                                    />
                                </el-radio>
                                <!-- 签署人多个变量时添加身份选择 -->
                                <el-radio-group
                                    v-if="form.labelExtends.receiverFill && receivers.length > 1"
                                    v-model="signerFillRoleId"
                                    :disabled="(!canChangeReceiver || form.labelExtends.subType ==='innerSign') && !isAppendedForDraft "
                                >
                                    <div v-for="item in receivers"
                                        :key="item.receiverId"
                                    >
                                        <el-radio :label="item.receiverId">
                                            <span>{{ item.showName }}</span>
                                        </el-radio>
                                    </div>
                                </el-radio-group>
                            </div>
                        </el-radio-group>
                    </el-form-item>

                    <!-- 临时字段-图片类型 设置宽高 -->
                    <el-form-item v-if="labelType === 'PICTURE'" :label="$t('customLabelEdit.labelSize')">
                        <div>
                            <!-- 宽度 -->
                            <span class="image-tips-1">{{ $t('customLabelEdit.labelWidth') }}</span><el-input v-model="form.labelPosition.width" type="Number" :placeholder="$t('customLabelEdit.labelWidthPlaceHolder')" class="image-tips-input" @blur="onChange('labelPosition', 'width')" /><span class="image-tips-1">px</span>
                        </div>
                        <div>
                            <!-- 高度 -->
                            <span class="image-tips-1">{{ $t('customLabelEdit.labelHeight') }}</span><el-input v-model="form.labelPosition.height" type="Number" :placeholder="$t('customLabelEdit.labelHeightPlaceHolder')" class="image-tips-input" @blur="onChange('labelPosition', 'height')" /><span class="image-tips-1">px</span>
                        </div>
                    </el-form-item>
                </template>
                <div v-else class="cus-label-edit-cpn--date-content-writer-con">
                    <!-- 内容填写人 -->
                    <p class="cus-label-edit-cpn--date-content-writer-con-title">{{ $t('customLabelEdit.contentFiller') }}</p>
                    <p>{{ $t('customLabelEdit.autoSystemFill') }}</p>
                </div>
            </template>

            <!-- 日期样式 -->
            <el-form-item :label="$t('customLabelEdit.dateFormat')"
                v-if="['BIZ_DATE', 'DATE', 'DATE_TIME'].includes(labelType)"
            >
                <el-radio-group v-model="form.labelExtends.dateFieldFormat"
                    @change="onChange('labelExtends','dateFieldFormat')"
                >
                    <el-radio class="date-format-radio"
                        v-for="opt in formatTypes"
                        :label="opt.value"
                        :key="opt.text"
                    >
                        {{ opt.text }}
                        <CommonTip
                            class="item"
                            effect="dark"
                            :content="getTooltipDateText(opt.value)"
                            placement="top"
                        />
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- 备选项 -->
            <template v-if="['SINGLE_BOX','MULTIPLE_BOX', 'COMBO_BOX'].includes(labelType)">
                <template v-if="'SINGLE_BOX' === labelType">
                    <!-- 备选项 -->
                    <el-form-item :label="$t('customLabelEdit.alternativeItem')">
                        <div class="buttons radio-buttons">
                            <div class="buttons-default-tip">{{ $t('customLabelEdit.defaultValueTip') }}</div>
                            <el-radio-group v-model="form.labelExtends.defaultValue">
                                <div v-for="(item,index) in form.labelExtends.items" :key="index">
                                    <el-radio
                                        :label="item.itemValue.trim()"
                                        @click.native.prevent="onClick('labelExtends', 'defaultValue', item.itemValue.trim())"
                                        :disabled="!canSetDefaultValue"
                                    >
                                        <span></span>
                                    </el-radio>
                                    <el-input :maxlength="100"
                                        @focus="changeButton(index,item)"
                                        @input="onValueChangeImmediate('labelExtends', 'items', index)"
                                        :class="index== labelButtonInd? 'selected':''"
                                        type="text"
                                        v-model.trim="item.itemValue"
                                    >
                                        <ElIDelete class="option-delete" v-if="index== labelButtonInd" slot="icon" :selfClick="true" @click="clearButtons($event,item)"></ElIDelete>
                                    </el-input>
                                </div>
                            </el-radio-group>
                        </div>
                    </el-form-item>
                </template>
                <!-- 复选框 -->
                <template v-if="'MULTIPLE_BOX' === labelType">
                    <!-- 备选项 -->
                    <el-form-item :label="$t('customLabelEdit.alternativeItem')">
                        <div class="buttons">
                            <div class="buttons-default-tip">{{ $t('customLabelEdit.defaultValueTip') }}</div>
                            <el-checkbox-group v-model="form.labelExtends.defaultValue" @change="onChange('labelExtends', 'defaultValue')" :disabled="!canSetDefaultValue">
                                <div v-for="(item,index) in form.labelExtends.items" :key="index">
                                    <el-checkbox :label="item.itemValue.trim()">
                                        <el-input :maxlength="100"
                                            @focus="changeButton(index,item,'MULTIPLE_BOX')"
                                            @input="onValueChangeImmediate('labelExtends', 'items', index)"
                                            :class="index== labelButtonInd? 'selected':''"
                                            type="text"
                                            v-model.trim="item.itemValue"
                                        >
                                            <ElIDelete class="option-delete" v-if="index== labelButtonInd" slot="icon" :selfClick="true" @click="clearButtons($event,item)"></ElIDelete>
                                        </el-input>
                                    </el-checkbox>
                                </div>
                            </el-checkbox-group>
                        </div>
                    </el-form-item>
                </template>
                <!-- 下拉框 -->
                <template v-if="'COMBO_BOX' === labelType">
                    <el-form-item :label="$t('customLabelEdit.alternativeItem')">
                        <div class="buttons">
                            <div class="buttons-default-tip">{{ $t('customLabelEdit.defaultValueTip') }}</div>
                            <el-radio-group v-model="form.labelExtends.defaultValue" :disabled="!canSetDefaultValue">
                                <div v-for="(item,index) in form.labelExtends.items" :key="index" class="combo-list">
                                    <el-radio :label="item.itemValue.trim()" @click.native.prevent="onClick('labelExtends', 'defaultValue', item.itemValue.trim())">
                                        <span class="combo-label-name">{{ $t('pointPositionDoc.checkboxName', { count: (index + 1) }) }}
                                            <i class="el-icon-ssq-delete delete-item-icon" @click.stop="handleDeleteOption('labelExtends', 'items', index)" v-if="form.labelExtends.items.length > 2"></i>
                                        </span>
                                        <div>
                                            <el-input :maxlength="100"
                                                @focus="changeButton(index,item)"
                                                @input="onValueChangeImmediate('labelExtends', 'items', index)"
                                                :class="index== labelButtonInd? 'selected':''"
                                                type="textarea"
                                                v-model.trim="item.itemValue"
                                            >
                                            </el-input>
                                        </div>
                                    </el-radio>
                                </div>
                            </el-radio-group>
                            <el-button type="text" class="combo-add-button" @click.stop="handleAddOption('labelExtends', 'items', [])">+ {{ $t('customLabelEdit.addOption') }}</el-button>
                            <span class="splitInline"></span>
                            <el-button type="text" class="combo-add-button" @click.stop="handleBatchAdd">+ {{ $t('customLabelEdit.batchAddOption') }}</el-button>
                        </div>
                    </el-form-item>
                </template>
            </template>

            <!-- 数字格式 -->
            <el-form-item :label="$t('customLabelEdit.numberFormat')" v-if="['NUMERIC_VALUE'].includes(labelType)">
                <el-radio-group v-model="decimalPlace" class="cus-field-number-select" @change="onChange('labelExtends', 'decimalPlace')">
                    <el-radio :label="0">{{ $t('customLabelEdit.integer') }}</el-radio>
                    <el-radio class="number-style-item" :label="-1">
                        {{ $t('customLabelEdit.decimalLimit') }}
                        <el-select v-model="numberDecimalFieldFormat" @change="onChange('labelExtends', 'decimalPlace')">
                            <el-option v-for="item in numberDecimalOptions" :key="item" :label="item" :value="item"></el-option>
                        </el-select>
                        {{ $t('customLabelEdit.decimal') }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 字号 -->
            <el-form-item :label="$t('customLabelEdit.labelFontSize')" v-if="!['SINGLE_BOX','MULTIPLE_BOX', 'PICTURE'].includes(labelType)">
                <el-select popper-class="cus-field-editor-select"
                    v-model="form.labelExtends.pxFontSize"
                    :placeholder="$t('customLabelEdit.labelFontSizePlaceHolder')"
                    :disabled="fontSizeSelectDisable"
                    @change="onChange('labelExtends', 'pxFontSize')"
                >
                    <el-option
                        v-for="(item, index) in fontSizeRange"
                        :key="index"
                        :label="isEn ? item.enLabel : item.label"
                        :value="convertPtToPx(item.pt, dpi)"
                    ></el-option>
                </el-select>
            </el-form-item>
            <!-- 设置文字对齐方式 -->
            <el-form-item :label="$t('customLabelEdit.labelAlign')" v-if="(!hybridServer || $hybrid.isGamma()) && ['TEXT', 'NUMERIC_VALUE'].indexOf(labelType) > -1">
                <el-radio-group v-model="form.labelExtends.alignment" @change="onChange('labelExtends', 'alignment')" class="alignment-group">
                    <!-- private Integer alignment;0（居左）,1（居中）,2（居右） -->
                    <el-radio v-for="(item, i) in alignmentArr" :key="i" :label="item.label" :class="{'active': form.labelExtends.alignment==item.label}">
                        <i :class="item.class"></i>
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- 设置文本格式 -->
            <el-form-item v-if="!getIsForeignVersion && labelType === 'TEXT' && form.labelExtends.receiverFill"
                class="text-format-select"
            >
                <span class="text-format-select__title">
                    {{ $t('customLabelEdit.formatValid') }}
                    <CommonTip
                        class="item"
                        effect="dark"
                        :content="$t('customLabelEdit.formatValidTip')"
                        placement="top"
                    >
                    </CommonTip>
                </span>
                <el-radio-group
                    v-model="form.labelExtends.validationType"
                    class="text-format-select__options"
                    @change="onChange('labelExtends', 'validationType')"
                >
                    <el-radio v-for="formatType in textFormatTypes"
                        :label="formatType.value"
                        :key="formatType.text"
                    >
                        <span>{{ formatType.text }}</span>
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- 设置文本字段填写说明 -->
            <el-form-item v-if="form.labelExtends.receiverFill && ['TEXT','TEXT_NUMERIC', 'CONFIRMATION_REQUEST_REMARK', 'NUMERIC_VALUE'].includes(labelType)">
                <span>
                    {{ $t('customLabelEdit.labelDescribe') }}
                    <CommonTip
                        type="popover"
                        trigger="click"
                        effect="light"
                        placement="bottom"
                        popper-class="label-describe-tooltip"
                    >
                        <template #content>
                            <p class="text">{{ $t('customLabelEdit.labelDescribeTooltip') }}</p>
                            <img v-if="$i18n.locale !== 'ja'"
                                class="image"
                                src="~img/template/label-describe.png"
                                alt="填写说明示意图"
                            >
                        </template>
                    </CommonTip>
                </span>
                <el-input
                    type="textarea"
                    :placeholder="$t('customLabelEdit.labelDescribeTip')"
                    v-model="form.description"
                    :maxlength="20"
                    @input="onValueChangeImmediate('description')"
                >
                </el-input>
            </el-form-item>
            <!-- 设置默认值 -->
            <el-form-item v-if="canSetDefaultValue &&
                              ['TEXT', 'BIZ_DATE','DATE_TIME', 'NUMERIC_VALUE'].includes(labelType)"
                :label="$t('customLabelEdit.defaultValue')"
            >
                <el-input
                    v-if="['TEXT', 'NUMERIC_VALUE'].includes(labelType)"
                    type="textarea"
                    v-model="form.labelExtends.defaultValue"
                    @input="onValueChangeImmediate('labelExtends', 'defaultValue')"
                >
                </el-input>
                <el-date-picker
                    v-else-if="['BIZ_DATE'].includes(labelType)"
                    v-model="form.labelExtends.defaultValue"
                    type="date"
                    :placeholder="$t('customLabelEdit.selectDateDefaultValue')"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    @change="onChange('labelExtends', 'defaultValue')"
                >
                </el-date-picker>
                <el-time-picker
                    v-else-if="labelType === 'DATE_TIME'"
                    v-model="form.labelExtends.defaultValue"
                    :placeholder="$t('customLabelEdit.defaultValue')"
                    value-format="HH:mm"
                    format="HH:mm"
                    @change="onChangeTimeDefault"
                >
                </el-time-picker>
            </el-form-item>
            <!-- 设置坐标位置 -->
            <el-form-item :label="$t('customLabelEdit.location')" v-if="!isBlankDocument && !isWordBillLabel">
                <div class="cus-field-editor-location">
                    <!-- x -->
                    <span class="image-tips-1">{{ $t('customLabelEdit.xLocation') }}</span>
                    <el-input
                        type="number"
                        v-model="form.labelPosition.vX"
                        :placeholder="$t('customLabelEdit.labelWidthPlaceHolder')"
                        class="image-tips-input"
                        :min="0"
                        @input="onValueChangeImmediate('labelPosition', 'x')"
                    />
                    <span class="image-tips-1">px</span>
                </div>
                <div class="cus-field-editor-location">
                    <!-- y -->
                    <span class="image-tips-1">{{ $t('customLabelEdit.yLocation') }}</span>
                    <el-input
                        type="number"
                        v-model="form.labelPosition.vY"
                        :placeholder="$t('customLabelEdit.labelHeightPlaceHolder')"
                        class="image-tips-input"
                        :min="0"
                        @input="onValueChangeImmediate('labelPosition', 'y')"
                    />
                    <span class="image-tips-1">px</span>
                </div>
                <p v-if="form.pageType === 'ALL' && templateStatus === 'use'" class="location-tip">
                    {{ $t('customLabelEdit.labelChangeAllPageTip') }}
                </p>
            </el-form-item>
            <!-- 填写要求 -->
            <el-form-item v-if="!['DATE', 'CONFIRMATION_REQUEST_REMARK'].includes(labelType)"
                :label="$t('customLabelEdit.labelRequire')"
            >
                <el-checkbox-group
                    v-model="form.labelExtends.required"
                    @change="onChange('labelExtends', 'required')"
                >
                    <el-checkbox :label="$t('customLabelEdit.labelRequireTip')" name="type"></el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <!-- 填写要求 -->
            <el-form-item v-if="['PICTURE'].includes(labelType)">
                <el-checkbox-group
                    v-model="picFillOption"
                >
                    <el-checkbox :label="$t('customLabelEdit.labelFillAllRegion')" name="type">
                        {{ $t('customLabelEdit.labelFillAllRegion') }}
                        <CommonTip
                            class="item"
                            effect="dark"
                            :content="$t('customLabelEdit.labelFillAllRegionTip')"
                            placement="top"
                        />
                    </el-checkbox>

                </el-checkbox-group>
            </el-form-item>
        </el-form>

        <div class="btns clear"
            v-if="!immediateSave"
        >
            <div class="ssq-btn-cancel"
                @click="clickCancelBtn"
            >{{ $t('customLabelEdit.cancel') }}</div>
            <div class="ssq-btn-confirm"
                @click="clickConfirmBtn"
            >{{ $t('customLabelEdit.confirm') }}</div>
        </div>
        <batchAddOptionsDialog :visible="showBatchAddOptionsDialog" :appendToBody="false" @close="showBatchAddOptionsDialog=false" @addComboBoxItems="handleBatchAddOption"></batchAddOptionsDialog>
        <LabelNameConflictDialog
            :visible.sync="labelNameConflictDialog.visible"
            :targetLabel="labelNameConflictDialog.targetLabel"
            :allLabels="allDocInputLabels"
            @confirm="handleConfirmLabelNameConflict"
            @close="handleCloseLabelNameConflict"
        />
    </div>
</template>
<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import { convertPtToPx, fontSizeRange } from 'pub-utils/fontSize.js';
import ElIDelete from 'pub-components/elIconfont/elIDelete/index.vue';
import { find } from 'pub-utils/dom.js';
import regRules from 'src/utils/regs.js';
import { DATE_FORMATS, TIME_FORMATS, TIME_BASE_DATE } from 'pub-consts/const';
import batchAddOptionsDialog from 'pub-businessComponents/batchAddOptionsDialog/index.vue';
import LabelNameConflictDialog from 'components/labelNameConflictDialog';
import { debounce } from 'pub-utils';
import dayjs from 'dayjs';
import { INPUT_TYPES } from 'utils/labelStyle';
export default {
    components: {
        ElIDelete,
        batchAddOptionsDialog,
        LabelNameConflictDialog,
    },
    props: {
        immediateSave: {
            type: Boolean,
            default: false,
        },
        isWordBillLabel: {
            type: Boolean,
            default: false,
        },
        currentPage: {
            type: Object,
        },
        editorMetaData: {
            type: Object,
            default: () => {
                return {
                    name: '',
                    receiverFill: '',
                    fontSize: 14,
                    necessary: true,
                    fieldType: '',
                    roleId: '', // 角色Id
                    // buttonDirection: 'V',
                    type: '',
                    buttons: [],
                    width: 0,
                    height: 0,
                    labelId: null,
                    alignment: 'left',
                    pageNumber: 1,
                    fillSender: '',
                    pageType: '',
                };
            },
        },
        canChangeReceiver: {
            type: Boolean,
            default: false,
        },
        isBlankDocument: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            showBatchAddOptionsDialog: false,
            form: JSON.parse(JSON.stringify(this.editorMetaData)),
            cacheOldData: JSON.parse(JSON.stringify(this.editorMetaData)),
            dpi: 96, // 目前写死
            convertPtToPx: convertPtToPx,
            fontSizeRange: fontSizeRange,
            input: '',
            alignmentArr: [{
                label: 'left',
                class: 'el-icon-ssq-juzuoduiqi',
            }, {
                label: 'center',
                class: 'el-icon-ssq-juzhongduiqi',
            }, {
                label: 'right',
                class: 'el-icon-ssq-juyouduiqi',
            }],
            textFormatTypes: [{
                text: this.$t('customLabelEdit.noFormat'),
                value: null,
            }, {
                text: this.$t('customLabelEdit.idCard'),
                value: 'CN_ID_FE',
            }, {
                text: this.$t('customLabelEdit.phoneNumber'),
                value: 'CN_MOBILE_FE',
            }],
            numberDecimalOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            numberDecimalFieldFormat: this.editorMetaData.labelExtends.decimalPlace === 0 ? 1 : this.editorMetaData.labelExtends.decimalPlace,
            decimalPlace: this.editorMetaData.labelExtends.decimalPlace === 0 ? 0 : -1,
            labelNameConflictDialog: { // 字段名称相同提示弹窗
                visible: false,
                targetLabel: {},
            },
        };
    },
    computed: {
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
        }),
        ...mapGetters(['getIsForeignVersion', 'getIsUae']),
        ...mapGetters('template', ['currentDoc']),
        ...mapState('template', ['receivers', 'editors', 'focusLabelOpt', 'templatePermissions', 'templateStatus',
                                 'isManageField', 'dateFormatTypes', 'docList']),
        labelType() {
            return this.editorMetaData.labelType;
        },
        isAppendedForDraft() {
            return this.editorMetaData.labelExtends?.appendedForDraft;
        },
        formatTypes() {
            return this.labelType === 'DATE_TIME' ? TIME_FORMATS : this.dateFormatTypes;
        },
        fillerUnchangable() {
            // 无调整权限、内部签字、时刻类型字段，单据模板不允许修改填写人，新单据模板字段不允许切换
            return !this.canChangeReceiver || this.form.labelExtends.subType === 'innerSign' || this.labelType ===
                'DATE_TIME' || this.isWordBillLabel;
        },
        fontSizeSelectDisable() {
            // 混合云用户禁止修改签署日期的字号
            return !!this.hybridServer && this.labelType === 'DATE';
        },
        focusLabel() {
            return this.currentDoc.labels.find(label => label.labelId === this.focusLabelOpt.labelId);
        },
        labelButtonInd() {
            return this.focusLabelOpt.labelButtonInd;
        },
        isEn() {
            return this.$i18n.locale === 'en';
        },
        canSetDefaultValue() {
            return this.form.labelExtends.receiverFill;
        },
        showEditorRadios() {
            return !this.form.labelExtends.receiverFill && this.editors.length &&
                this.form.labelType !== 'PICTURE';
        },
        dateFormat() {
            return (this.form.labelExtends.dateFieldFormat || DATE_FORMATS[1].value).toUpperCase();
        },
        isDisabledName() {
            // 日期标签 或者设置了管理业务的业务字段 或者批注标签
            return this.labelType === 'DATE' || (this.isManageField && this.labelType !== 'PICTURE') || (this.labelType === 'TEXT' && this.form.labelExtends.subType === 'innerSign');
        },
        getTooltipDateText() {
            return (formatType) => {
                if (this.labelType === 'DATE_TIME') {
                    return TIME_FORMATS.find(a => a.value === formatType).tooltip;
                }
                return dayjs('2022-09-10').format(formatType.toUpperCase());
            };
        },
        picFillOption: {
            get() {
                return this.form.labelExtends?.autoAdjust || false;
            },
            set(v) {
                this.form.labelExtends.autoAdjust = v;
                this.onChange('labelExtends', 'autoAdjust');
            },
        },
        senderFillRoleId: {
            get() {
                // completerType：2 预置签约方页面勾选了“补全附件”，这里就不能补全字段了
                if (this.editors.find(a => a.receiverId === this.form.receiverId) && this.form.completerType !== 2) {
                    return this.form.receiverId;
                }
                // '1'标记发件时填写
                return '1';
            },
            set(v) {
                this.form.receiverId = v === '1' ? '' : v;
                this.onChange('receiverId');
            },
        },
        signerFillRoleId: {
            get() {
                if (this.receivers.find(a => a.receiverId === this.form.receiverId)) {
                    return this.form.receiverId;
                }
                // '1'标记发件时填写
                return this.receivers[0].receiverId;
            },
            set(v) {
                this.form.receiverId = v;
                this.onChange('receiverId');
            },
        },
        allDocInputLabels() { // 全部的具名业务字段，插入字段时同名比较要用
            return this.docList.reduce((total, curDoc) => {
                total = total.concat(curDoc.labels.filter(label => INPUT_TYPES.includes(label.labelType)).map(a => ({
                    labelName: a.labelName,
                    labelId: a.labelId,
                    labelType: a.labelType,
                })));
                return total;
            }, []);
        },
    },
    watch: {
        editorMetaData: {
            handler(v) {
                this.cacheOldData = JSON.parse(JSON.stringify(v)); // 缓存form数据
                this.form = JSON.parse(JSON.stringify(v)); // 缓存form数据
                if (v.labelType === 'MULTIPLE_BOX') {
                    const defaultValue = v.labelExtends.defaultValue || [];
                    this.form.labelExtends.defaultValue = typeof defaultValue === 'string' ?  defaultValue.split(',') : defaultValue;
                } else if (['DATE', 'BIZ_DATE'].includes(v.labelType) && !this.form.labelExtends.dateFieldFormat) {
                    this.form.labelExtends.dateFieldFormat = DATE_FORMATS[1].value;
                } else if (v.labelType === 'DATE_TIME' && this.form.labelExtends.defaultValue) {
                    this.form.labelExtends.defaultValue =
                        dayjs(Number(this.form.labelExtends.defaultValue)).format(this.form.labelExtends.dateFieldFormat);
                } else if (v.labelType === 'NUMERIC_VALUE') {
                    this.decimalPlace = v.labelExtends.decimalPlace === 0 ? 0 : -1;
                    this.numberDecimalFieldFormat = v.labelExtends.decimalPlace === 0 ? 1 : v.labelExtends.decimalPlace;
                } else if (v.labelType === 'TEXT') {
                    this.form.labelExtends.validationType = v.labelExtends.validationType || null;
                }
                if (!v.fillSender) {
                    this.form.fillSender = 'SENDER';
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        ...mapMutations({
            setFocusLabelOpt: 'template/setFocusLabelOpt',
        }),
        handleConfirmLabelNameConflict(name) {
            this.form.labelName = name;
            this.onChange('labelName');
        },
        handleCloseLabelNameConflict() {
            this.labelNameConflictDialog = {
                visible: false,
                targetLabel: {},
            };
            this.form.labelName = this.editorMetaData.labelName;
        },
        // 防抖提交
        onValueChangeImmediate: debounce(function(k, nextK, index) {
            if (k === 'labelPosition') {
                this.onLocationInput(k, nextK);
            }
            if (k === 'labelName' && this.allDocInputLabels.find(a => a.labelName === this.form.labelName)) {
                this.labelNameConflictDialog = {
                    visible: true,
                    targetLabel: { ...this.form },
                };
                return;
            }
            this.onChange(k, nextK, index);
        }, 400),
        changeButton(ind, item, labelType) {
            if (ind !== this.labelButtonInd) {
                this.setFocusLabelOpt({
                    labelId: this.editorMetaData.labelId,
                    labelButtonInd: ind,
                });
            }
            if (labelType === 'MULTIPLE_BOX' && item.itemValue.indexOf(',') !== -1) {
                this.$MessageToast.error(this.$t('customLabelEdit.messageTip.hasEnComma'));
            }
        },
        onClick(k, nextK, index) {
            if (nextK !== 'defaultValue' || !this.canSetDefaultValue) {
                return;
            }
            if (this.form.labelExtends.defaultValue === index) {
                this.form.labelExtends.defaultValue = '';
            } else {
                this.form.labelExtends.defaultValue = index;
            }
            this.onChange(k, nextK);
        },
        onLocationInput: function(k, nextK) {
            const { width, height } = this.currentPage;
            const position = {};
            if (nextK === 'x') {
                let x = this.form[k].vX / width;
                if (x < 0) {
                    x = 0;
                } else {
                    x = Math.min(x, 1 - this.form[k].vWidth);
                }
                position.x = x;
            } else {
                const y = 1 - Math.abs(this.form[k].vY) / height;
                position.y = y;
            }
            this.$emit('change-location', position);
        },
        onChangeTimeDefault(val) {
            const defaultValue = dayjs(new Date(`${TIME_BASE_DATE} ${val}`)).valueOf();
            this.$emit('change-value', {
                labelExtends: {
                    ...this.form.labelExtends,
                    defaultValue,
                },
            }, 'labelExtends', 'defaultValue');
        },
        onChange(k, nextK, index) {
            if (!this.immediateSave) {
                return;
            }
            if (k === 'labelName' && this.form[k].trim().length === 0) {
                return this.$MessageToast.error(this.$t('customLabelEdit.messageTip.nameError')); // 请输入名称
            }
            // 切换填写人时，重置格式类型
            if (nextK === 'receiverFill' && this.form.labelType === 'TEXT' && !this.form.labelExtends.receiverFill) {
                this.form.labelExtends.validationType = null;
            }
            // 设置默认值和数字格式
            if (this.labelType === 'NUMERIC_VALUE' && ['decimalPlace', 'defaultValue'].includes(nextK)) {
                if (nextK === 'decimalPlace') {
                    this.form[k].decimalPlace = this.decimalPlace === 0 ? 0 : this.numberDecimalFieldFormat;
                }
                const { decimalPlace, defaultValue } = this.form[k];
                const numberRegex = regRules.numberDefault(decimalPlace);
                if (defaultValue && !numberRegex.test(defaultValue)) {
                    return this.$MessageToast.error(this.$t(decimalPlace > 0 ? 'customLabelEdit.messageTip.numberDefaultError' : 'customLabelEdit.messageTip.numberDefaultInteger', { decimalPlace }));
                }
            }

            if (nextK === 'items') {
                const now = this.form[k][nextK][index].itemValue;
                const old = this.cacheOldData[k][nextK][index].itemValue;
                const sameItems = this.form[k][nextK].filter(item => item.itemValue === now);
                if (!now.trim()) {
                    this.$MessageToast.error(this.$t('customLabelEdit.messageTip.itemError')); // '请输入备选项'
                    return;
                } else if (old === now) {
                    return;
                } else if (sameItems.length > 1) {
                    this.$MessageToast.error(this.$t('customLabelEdit.messageTip.itemSameError')); // 备选项不能相同
                    return;
                }
                // 备选项名字需要做格式校验，不能包括特殊字符, CFD-6264
                if (regRules.fieldValueReg.test(now)) {
                    this.$MessageToast.error(this.$t('customLabelEdit.messageTip.markOptionValueTip'));
                    return;
                }
                // 备选项变更时，对应设置的默认值也要进行变更
                if (['SINGLE_BOX', 'COMBO_BOX'].includes(this.labelType)) {
                    if (this.form.labelExtends.defaultValue === old) {
                        this.form.labelExtends.defaultValue = now;
                    }
                } else if (this.labelType === 'MULTIPLE_BOX') {
                    if (now.indexOf(',') !== -1) {
                        this.$MessageToast.error(this.$t('customLabelEdit.messageTip.hasEnComma'));
                        return;
                    }
                    const index = this.form.labelExtends.defaultValue.findIndex(item => item === old);
                    if (index > -1) {
                        this.form.labelExtends.defaultValue.splice(index, 1, now);
                    }
                }
            }
            // x，y坐标变化实时响应，是一致的
            // console.log('x，y坐标 === ', nextK && !['x', 'y'].includes(nextK), this.cacheOldData[k][nextK],
            //             this.form[k][nextK], this.cacheOldData[k] === this.form[k]);
            if ((nextK && !['x', 'y'].includes(nextK) &&
                this.cacheOldData[k][nextK] === this.form[k][nextK]) || this.cacheOldData[k] === this.form[k]) { // 利用缓存数据，在名称未发生变化时，直接返回，不触发数据更新
                return;
            }

            const data = {};
            data[k] = JSON.parse(JSON.stringify(this.form[k]));
            if (nextK) {
                data[k][nextK] = this.form[k][nextK];
            }
            // 复选框的值类型需要用string，将数组转换为string
            if (this.labelType === 'MULTIPLE_BOX' && k !== 'labelName') {
                data.labelExtends = this.form.labelExtends;
                data.labelExtends.defaultValue = (this.form.labelExtends.defaultValue || []).join(',');
            }
            if (nextK !== 'pxFontSize' && this.form.labelExtends.pxFontSize) { // 避免后端设置字体默认值
                data.labelExtends = this.form.labelExtends;
                data.labelExtends.pxFontSize = this.form.labelExtends.pxFontSize;
            }
            if (nextK === 'items') { // 修改备选项
                data[k][nextK][index].itemValue = this.form[k][nextK][index].itemValue;
            }
            if (nextK === 'dateFieldFormat') {
                data.labelExtends.locale = this.dateFormatTypes.find(t => t.value ===
                    this.form.labelExtends.dateFieldFormat)?.locale;
            }

            // 图片类型，修改宽高 大小限制提示
            if (this.labelType === 'PICTURE' && (nextK === 'width' || nextK === 'height')) {
                const { width, height } = this.currentPage;
                let errMsg = null;
                if (nextK === 'width') {
                    if (this.form[k][nextK] < 28) {
                        errMsg = this.$t('customLabelEdit.messageTip.widthError'); // 宽度请输入大于等于28的值
                    }
                    if (this.form[k][nextK] >= width) {
                        errMsg =  this.$t('customLabelEdit.messageTip.widthMaxError', { width }); // `宽度请输入小于 width 的值`;
                    }
                } else {
                    if (this.form[k][nextK] < 20) {
                        errMsg =  this.$t('customLabelEdit.messageTip.heightError'); // '高度请输入大于等于20的值';
                    }
                    if (this.form[k][nextK] >= height) {
                        errMsg = this.$t('customLabelEdit.messageTip.heightMaxError', { height }); // `高度请输入小于 height 的值`;
                    }
                }
                if (errMsg) {
                    this.$set(this.form[k], nextK, this.cacheOldData[k][nextK]);
                    return this.$MessageToast.error(errMsg);
                }

                data[k].width =  this.form[k].width / this.currentPage.width;
                data[k].height = this.form[k].height / this.currentPage.height;
                // 高度变动导致坐标需要下移(增加的高度)
                if (nextK === 'height') {
                    data[k].y = data[k].y - (data[k].height - this.cacheOldData[k].height / this.currentPage.height);
                    // 如果下移超过底线，则紧贴底线
                    if (data[k].y < 0) {
                        data[k].y = 0;
                    }
                } else if (nextK === 'width') {
                    if (data[k].x + data[k].width > 1) {
                        data[k].x = 1 - data[k].width;
                    }
                }
            }

            // 修改坐标位置
            if (k === 'labelPosition' && (nextK === 'x' || nextK === 'y')) {
                const { width, height } = this.currentPage;
                if (nextK === 'x') {
                    let x = this.form[k].vX / width;
                    if (x < 0) {
                        x = 0;
                    } else {
                        x = Math.min(x, 1 - this.form[k].vWidth);
                    }
                    data[k].x = x;
                    data[k].y = this.form[k].y;
                } else {
                    let y = 1 - Math.abs(this.form[k].vY) / height;
                    if (y < 0)  {
                        y = 0;
                    } else {
                        y = Math.min(y, 1 - this.form[k].vHeight);
                    }
                    data[k].y = y;
                    data[k].x = this.form[k].x;
                }
                // 因为初始化时宽高被修改成绝对值，这里需要重新修正一下
                data[k].width = this.form[k].vWidth;
                data[k].height = this.form[k].vHeight;
            }
            this.$emit('change-value', data, k, nextK);
        },

        clickConfirmBtn() {
            this.$emit('confirm', this.form);
        },

        clickCancelBtn() {
            this.$emit('cancel');
        },
        clearButtons(e, item) {
            item.itemValue = '';
            find(e.$parent.$el, 'INPUT').focus();
        },
        getMarkCurrentPage() {
            const pageNumber = this.editorMetaData.pageNumber;
            return (this.currentDoc.documentPages && this.currentDoc.documentPages[pageNumber - 1]) || {};
        },
        handleDeleteOption(k, nextK, index) {
            const data = {};
            data[k] = JSON.parse(JSON.stringify(this.form[k]));
            if (nextK) {
                data[k][nextK] = this.form[k][nextK];
            }
            if (nextK === 'items') { // 删除备选项
                data[k][nextK].splice(index, 1);
            }
            this.$emit('change-value', data, k, nextK);
        },
        handleAddOption(k, nextK, inputOptions) {
            const data = {};
            data[k] = JSON.parse(JSON.stringify(this.form[k]));
            if (nextK) {
                data[k][nextK] = this.form[k][nextK];
            }
            if (nextK === 'items') {  // 新增备选项
                if (!inputOptions.length) {
                    if (data[k][nextK].length >= 500) {
                        this.$MessageToast.error(this.$t('customLabelEdit.messageTip.overCountLimit')); // 备选项个数大于500
                        return;
                    }
                    data[k][nextK].push({
                        itemValue: this.$t('pointPositionDoc.checkboxName', { count: data[k][nextK].length + 1 }),
                    });
                    this.$emit('change-value', data, k, nextK);
                } else {
                    const totalNum = data[k][nextK].length + inputOptions.length;
                    if (totalNum >= 500) {
                        this.$MessageToast.error(this.$t('customLabelEdit.messageTip.overCountLimit')); // 备选项个数大于500
                        return;
                    }
                    data[k][nextK] = data[k][nextK].concat(inputOptions);
                    this.$emit('change-value', data, k, nextK);
                }
            }
        },
        handleBatchAddOption(items) {
            const batchAddedOptions = [];
            const nowItems = this.form.labelExtends.items.map(a => a.itemValue);
            items.textValues.filter(text => !nowItems.includes(text)).forEach((item) => {
                batchAddedOptions.push({
                    itemValue: item,
                });
            });
            if (batchAddedOptions.length) {
                this.handleAddOption('labelExtends', 'items', batchAddedOptions);
            }
            this.showBatchAddOptionsDialog = false;
        },
        handleBatchAdd() {
            this.showBatchAddOptionsDialog = true;
        },
    },
};
</script>
<style lang="scss">
.label-describe-tooltip {
    width: 420px;
    .text {
        //line-height: 20px;
    }
    .image {
        width: 400px;
    }
}
    .en-page .cus-label-edit-cpn .el-radio {
        width: 69px;
    }
	.cus-label-edit-cpn {
		label {
			width: 100%;
			height: 20px;
			font-size: 14px;
			color: $--color-text-primary;
			text-align: left;
			padding-top: 0;
            padding-bottom: 0;
            line-height: 20px!important;
            [dir=rtl] & {
                text-align: right;
            }
		}
        .cus-field-editor-location {
            .image-tips-input {
                width: 112px !important;
            }
        }
        .fill-select {
            padding-top: 5px;
            .el-radio-group {
                display: block;
                padding: 5px 0 0 20px;
            }
        }
        .location-tip {
            color: $--color-text-secondary;
            font-size: 12px;
            line-height: 16px;
        }
		.form-name .el-form-item__label::after {
			content: "*";
            color: $--color-danger;
            margin-left: 4px;
            [dir=rtl] & {
                margin-right: 4px;
                margin-left: 0;
            }
		}
		.el-form-item__content .el-input {
			padding-top: 0;
			.el-input__inner {
				height: 28px;
				line-height: 28px;
				font-size: 12px;
			}
			.el-input__icon {
				color: $--color-text-primary;
			}
		}
        .el-date-editor.el-input {
            width: 100%;
        }
        .text-format-select {
            &__title {
                color: $--color-text-primary;
            }
            &__options {
                padding-left: 20px;
                line-height: 18px;
                [dir=rtl] & {
                    padding-right: 20px;
                    padding-left: 0;
                }
            }
        }
        .buttons .el-radio-group .el-radio {
            width: auto;
            height: 30px;
            margin-bottom: 5px;
            line-height: 30px !important;
        }
        .buttons .el-checkbox-group .el-checkbox {
            width: 16px;
        }
        .radio-buttons .el-radio-group .el-radio {
            width: 20px;
            float: left;
            margin-right: 0;
            [dir=rtl] & {
                float: right;
                margin-left: 0;
            }
        }
        .buttons .buttons-default-tip {
            font-size:12px;
            color:$--color-text-secondary
        }
		.el-radio {
			width: 72px;
			.el-radio__label {
				padding-left: 2px;
                [dir=rtl] & {
                    padding-right: 2px;
                    padding-left: 0;
                }
			}
		}
		.btns {
			.ssq-btn-confirm, .ssq-btn-cancel {
				float: right;
				line-height: 34px;
                [dir=rtl] & {
                    float: left;
                }
			}
			.ssq-btn-confirm {
				margin-right: 8px;
                [dir=rtl] & {
                    margin-left: 8px;
                    margin-right: 0;
                }
			}
		}
		.tips {
			margin-left: 1px;
			font-size: 12px;
			color: $--color-text-regular;
			cursor: pointer;
		}
		.cus-field-editor-identity {
			margin-top: -16px;
            padding-left: 10px;
            .el-radio-group {
                .el-radio {
					width: 100%;
					overflow: hidden;
					height: auto;
					margin-bottom: 3px;
                    .el-radio__input {
                        width: 20px;
                        float: left;
                    }
                    .el-radio__label {
                        display: block;
                        margin-left: 20px;
						white-space: normal;
						line-height: 16px;
                    }
                }
            }
            [dir=rtl] & {
                padding-left: 0;
                padding-right: 10px;
                .el-radio-group {
                    .el-radio {
                        .el-radio__input {
                            float: right;
                        }

                        .el-radio__label {
                            margin-left: 0;
                            margin-right: 20px;
                        }
                    }
                }
            }
        }
        .alignment-group.el-radio-group {
            background: $--background-color-regular;
            border: 1px solid $--border-color-light;
            border-radius: 2px;
            width: 100%;
            .el-radio {
                width: 32%;
                margin: 0;
                text-align: center;
                padding: 4px 0;
                i[class^="el-icon-ssq-"] {
                    font-size: 20px;
                }
                &.active {
                    background: $--color-white;
                    i[class^="el-icon-ssq-"] {
                        color: $theme-color;
                    }
                }
                &:nth-child(2) {
                    width: 34%;
                    border-left: 1px solid $--border-color-light;
                    border-right: 1px solid $--border-color-light;
                }
            }
            .el-radio .el-radio__input .el-radio__inner {
                display: none;
            }
        }
        .date-format-radio {
            display: block;
        }
        .el-form-item__content .el-date-editor .el-input__inner {
            padding-left: 30px !important;
        }
        .combo-list {
            margin-top: 10px;
            &:first-child {
                margin-top: 0;
            }
        }
        .combo-label-name {
            display: inline-block;
            margin-left: 4px;
        }
        .delete-item-icon {
            color: $--color-text-secondary;
            font-size: 12px;
            cursor: pointer;
            margin-left: 95px;
        }
        .splitInline {
            display: inline-block;
            width: 1px;
            height: 12px;
            margin: -2px 8px;
            background-color: #eee;
        }
        .combo-add-button {
            margin-top: 10px;
            font-size: 12px;
        }
	}
        .buttons{
            .option-checkbox{
                display: inline-block;
                background: $--color-white;
                border: 1px solid $--border-color-base;
                border-radius: 2px;
                width: 14px;
                height: 14px;
                vertical-align: middle;
            }
            .el-input{
                background: $--color-white;
                width: 136px;
                height: 30px;
                margin-left: 8px;
                border: 1px solid $--border-color-base;
                box-sizing: border-box;
                line-height: 100%;
                margin-bottom:5px;
                input,input:hover,input:focus{
                    padding-left: 6px!important;
                    box-shadow:none;
                    border:none;
                    height: 30px;
                    line-height: 30px;
                }
                &.selected{
                    border: 1px solid $theme-color;
                    box-shadow: 0 0 2px $--color-primary-light-1;
                }
            }
        }
        .buttons-direction{
            line-height: 22px;
            .el-radio .el-radio__label{
                padding-left: 10px;
            }
        }

	.cus-field-editor-select {
		li {
			height: 28px;
			line-height: 28px;
			padding: 0 10px;
		}
	}
    .cus-field-number-select {
        .el-radio {
            width: auto;
            margin-left: 10px;
        }
        .el-input__inner {
            width: 60px;
            display: inline-block;
            padding-left: 5px;
            padding-right: 10px;
        }
        .el-select {
            width: auto !important;
        }
    }
	.cus-field-editor-role {
		margin-top: -16px;
	}
    .cus-label-edit-cpn--date-content-writer-con {
        margin-bottom: 22px;
        color: $--color-text-primary;
        .reciver-write{
            color: $--color-text-secondary;
            font-size: 12px;
            i{
                padding-right: 3px;
            }
        }
    }
    .cus-label-edit-cpn--date-content-writer-con-title {
        margin-bottom: 8px;
        font-size: 14px;

    }
    .image-tips {
        font-size: 12px;
        color: $--color-info;
        .el-icon-ssq-tishi1{
            padding-right: 4px;
        }
        &-1 {
            color: $--color-text-primary;
            display: inline-block;
            font-size: 12px;
        }
        &-input {
            width: 125px;margin: 0 9px;
        }
    }
</style>
