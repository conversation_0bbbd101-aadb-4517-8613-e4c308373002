<template>
    <div class="extends-collapses" :class="{'en-page': lang.locale === 'en','ja-page':lang.locale === 'ja'}">
        <ExtendsItem v-if="getIsForeignVersion || ifCrossPlatform" :title="$t('receiverItem.setNoticelang')" name="notifyForeign" :hideOpera="true">
            <el-radio v-model="noticeLanguage" label="EN">{{ $t('receiverItemExtends.English') }}</el-radio>
            <el-radio v-if="getIsUae" v-model="noticeLanguage" label="AR">{{ $t('receiverItemExtends.Arabic') }}</el-radio>
            <el-radio v-model="noticeLanguage" label="ZH">{{ $t('receiverItemExtends.Chinese') }}</el-radio>
            <el-radio v-model="noticeLanguage" label="JA">{{ $t('receiverItemExtends.Japanese') }}</el-radio>
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('messageAndFaceVerify')" name="messageAndFaceVerify" @del="delHandle">
            <template slot="title">
                {{ $t('receiverItemExtends.messageAndFaceVerify') }}
                <span class="err" v-if="isLimitFaceConfig">({{ $t('receiverItem.limitFaceConfigTip') }})</span>
            </template>
            {{ $t('receiverItemExtends.messageAndFaceVerifyTips') }}
            <FaceTooltip />
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('faceFirst')" name="faceFirst" @del="delHandle">
            <template slot="title">
                {{ $t('receiverItemExtends.faceFirst') }}
                <span class="err" v-if="isLimitFaceConfig">({{ $t('receiverItem.limitFaceConfigTip') }})</span>
            </template>
            {{ $t('receiverItemExtends.faceFirstTips') }}
            <FaceTooltip />
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('faceMust')" name="faceMust" @del="delHandle">
            <template slot="title">
                {{ $t('receiverItemExtends.faceMust') }}
                <span class="err" v-if="isLimitFaceConfig">({{ $t('receiverItem.limitFaceConfigTip') }})</span>
            </template>
            <span>{{ $t('receiverItemExtends.faceMustTips') }}</span>
            <FaceTooltip />
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('noHand')" :title="$t('receiverItemExtends.noHand')" name="noHand" @del="delHandle">
            {{ $t('receiverItemExtends.noHandTips') }}
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('mustHand') && !internalSignInfoVO.isInternalSignTemplate" :title="$t('receiverItemExtends.mustHand')" name="mustHand" @del="delHandle">
            {{ $t('receiverItemExtends.mustHandTips') }}
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('notify') && isSignerRole" name="notify" @del="delHandle">
            <template slot="title">
                {{ $t('receiverItemExtends.notifyLabel.1') }}
                <span class="extends-title-gray">{{ $t('receiverItemExtends.notifyLabel.2') }}</span>
            </template>
            <el-checkbox class="notify-checkbox" v-if="!ifCrossPlatform" v-model="recipient.communicateInfo.ifEditorComplement" @change="changeEditorComplement">{{ $t('receiverItemExtends.notifyLabel.3') }}</el-checkbox>
            <PrivateMessage
                :disabled="disabled"
                :type="templateStatus"
                :communicateInfo="recipient.communicateInfo"
                :errors="vErrors"
                @spliceErr="spliceErr"
                @changeConfig="changePrivateMessageConfig"
                @change="updatePrivateMessage"
            />
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('handwritingRec')" :title="$t('receiverItemExtends.handwritingRec')" name="handwritingRec" @del="delHandle">
            {{ $t('receiverItemExtends.handwritingRecTips') }}
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('readAll')" :title="$t('receiverItemExtends.readAll')" name="readAll" remove @del="delHandle">
            {{ $t('receiverItemExtends.readAllTips') }}
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('attachDoc')" name="attachDoc" @del="delHandle">
            <template slot="title">{{ $t('receiverItemExtends.attachDoc') }}<span class="extends-title-gray">{{
                $t('receiverItemExtends.attachDocTip') }}</span></template>
            <ExtendsAttachItem @change="handleAttachList" :attach-list="recipient.attachRequires" :disabled="disabled" :index="index" @error="errorHandle"></ExtendsAttachItem>
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('mainDoc')" :title="$t('receiverItemExtends.mainDoc')" name="mainDoc" @del="delHandle">
            <ExtendsBoxContent :archiveId="recipient.signerConfig.archiveId" :disabled="disabled"></ExtendsBoxContent>
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('notifyOff')" :title="getIsForeignVersion ? $t('receiverItemHeader.notifyOffJa') : $t('receiverItemHeader.notifyOff')" name="notifyOff" @del="delHandle">
            {{ $t('receiverItemExtends.notifyOffTips') }}
        </ExtendsItem>
        <!-- 日文环境下不显示使用日文通知 -->
        <ExtendsItem v-if="visibleItems.includes('notifyForeign') && !getIsForeignVersion && !ifCrossPlatform" :title="$t('receiverItemExtends.notifyForeign')" name="notifyForeign" @del="delHandle">
            <span>{{ $t('receiverItemExtends.notifyForeignTips') }}{{ $t('receiverItemHeader.notifyForeignTips') }}</span><br>
            <el-radio v-model="noticeLanguage" label="EN">{{ $t('receiverItemExtends.English') }}</el-radio>
            <el-radio v-model="noticeLanguage" label="JA">{{ $t('receiverItemExtends.Japanese') }}</el-radio>
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('contractPayer')" :title="$t('receiverItemExtends.signerPay')" name="contractPayer" @del="delHandle">
            {{ $t('receiverItemExtends.signerPayTip') }}
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('contractDownloadControl')" :title="$t('receiverItemExtends.contractDownloadControl')" name="contractDownloadControl" @del="delHandle">{{ $t('receiverItemExtends.contractDownloadControlTips') }}</ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('ddl') && isSignerRole" :title="$t('receiverItemExtends.ddl')" name="ddl" @del="delHandle">
            <ExtendsDdl @change="updateDdl" :ddl="recipient.signerConfig.ddl"></ExtendsDdl>
        </ExtendsItem>
        <ExtendsItem v-if="recipient.editable" :title="$t('sendedEdit.name')" :hideDelete="true" name="sendedEditDesc">
            {{ $t('sendedEdit.sendedEditDesc') }}
            <p>{{ $t('sendedEdit.completeBy') }}
                <el-select class="editor-select" disabled v-model="editorRole.roleName" placeholder="">
                    <el-option :label="editorRole.roleName" :value="editorRole.roleName"></el-option>
                </el-select>
                {{ $t('sendedEdit.complete') }}
            </p>
        </ExtendsItem>
        <ExtendsItem v-if="recipient.receiverType === 'EDITOR'" :title="$t('receiverItemHeader.editor')" :hideDelete="true" name="sendedEditor">
            <p>{{ $t('sendedEdit.completeDescription.0') }}</p>
            <el-radio v-model="completerType" :label="2" :disabled="disableCompleteAttachment">{{ $t('sendedEdit.completeAttachment') }}</el-radio>
            <el-radio v-model="completerType" :label="1" :disabled="disableCompleteOther">
                <span>{{ $t('sendedEdit.completeOther') }}</span>
                <span class="common-font-color cur-pointer" @click="handleViewGuide">{{ $t('sendedEdit.completeDescription.1') }}</span>
            </el-radio>
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('twoFactorAuthentication')" name="twoFactorAuthentication" @del="delHandle">
            {{ $t('receiverItemExtends.twoFactorAuthenticationTips') }}
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('encryptionSign')" name="encryptionSign" @del="delHandle">
            {{ $t('receiverItemExtends.encryptionSignTips') }}
            <ExtendsPassword @changeSignPassword="updatePassword" :password="recipient.encryptionSign.encryptionSignPassword"></ExtendsPassword>
        </ExtendsItem>
        <ExtendsItem v-if="visibleItems.includes('existSummaryTask')" :title="$t('receiverItemExtends.existSummaryTask')" name="existSummaryTask" @del="delHandle">
            {{ $t('receiverItemExtends.contractExtractTips') }}
            <ExtendsContractExtract :receiverId="recipient.receiverId"></ExtendsContractExtract>
        </ExtendsItem>
    </div>
</template>

<script>
import ExtendsItem from './ExtendsItem';
import ExtendsAttachItem from './ExtendsAttachItem';
import ExtendsBoxContent from './ExtendsBoxContent';
import ExtendsDdl from './ExtendsDdl';
import ExtendsPassword from './ExtendsPassword';
import ExtendsContractExtract from './ExtendsContractExtract/index.vue';
import FaceTooltip from './faceTooltip';
import PrivateMessage from 'components/privateMessage';
import { mapState, mapActions, mapGetters } from 'vuex';
import i18n from 'src/lang';
export default {
    name: 'ReceiverExtends',
    components: {
        ExtendsItem,
        ExtendsAttachItem,
        ExtendsBoxContent,
        PrivateMessage,
        ExtendsDdl,
        ExtendsPassword,
        ExtendsContractExtract,
        FaceTooltip,
    },
    props: {
        visibleItems: {
            default: () => [''],
            type: Array,
        },
        disabled: {
            default: false,
            type: Boolean,
        },
        sendNoticeLanguage: {
            type: String,
            default: '',
        },
        vRouteOrder: {
            type: Number,
            default: 1,
        },
        receiverCompleterType: {
            type: Number,
            default: 0,
        },
    },
    provide() {
        return {
            getDisabledStatus: () => this.disabled,
        };
    },
    data() {
        return {
            attach: [],
            archiveId: '',
            offNotice: 1,
            noticeChooseType: '0',
            day: undefined,
            hour: undefined,
            minute: undefined,
            noticeLanguage: '',
            lang: i18n,
            completerType: 0, // 0: 非补全人 1: 补全其他 2: 补全附件
        };
    },
    computed: {
        ...mapState('template', ['recipients', 'templateStatus', 'internalSignInfoVO', 'ifCrossPlatform', 'crossPlatformRecipients', 'templatePermissions', 'ifExistAttachmentLabel', 'isLimitFaceConfig']),
        ...mapGetters(['getIsForeignVersion', 'getIsUae']),
        vErrors: {
            set(value) {
                this.updateRecipient({
                    errors: value,
                });
            },
            get() {
                return this.recipient.errors || [];
            },
        },
        // 存在补全角色
        existSendedEditRole() {
            return this.receivers.some(recipient => recipient.receiverType === 'EDITOR');
        },
        crossPlatformType() {
            return this.getCrossPlatformType();
        },
        receivers() {
            if (this.ifCrossPlatform) {
                return this.crossPlatformRecipients[this.crossPlatformType];
            }
            return this.recipients;
        },
        recipient() {
            return this.receivers[this.index];
        },
        notAllowNotify: {
            get() {
                return this.recipient.noticeConfig.notAllowNotify;
            },
            set(value) {
                this.updateRecipient({
                    'noticeConfig.notAllowNotify': value,
                });
            },
        },
        noticeSpecificUser: {
            get() {
                return this.recipient.noticeConfig.noticeSpecificUser;
            },
            set(value) {
                this.updateRecipient({
                    'noticeConfig.noticeSpecificUser': value,
                });
            },
        },
        isSignerRole() {
            return !(['EDITOR', 'CC_USER'].includes(this.recipient.receiverType));
        },
        index() {
            return this.indexFunc && this.indexFunc();
        },
        editorRole() {
            return this.receivers.find(a => a.receiverType === 'EDITOR') || {
                roleName: '',
            };
        },
        disableCompleteAttachment() {
            // 补全附件选项可勾选条件：模板配置了附件字段, 第一顺序签署人 模板使用阶段发件人获得"调整签署要求"权限
            return !this.ifExistAttachmentLabel || this.vRouteOrder !== 1 || (this.templateStatus === 'use' && !this.templatePermissions.modifySignRequirement);
        },
        disableCompleteOther() {
            // 补全其他选项可勾选条件：模板使用阶段发件人获得"调整签署要求"权限
            return this.templateStatus === 'use' && !this.templatePermissions.modifySignRequirement;
        },
    },
    watch: {
        visibleItems(val, oldVal) {
            if (val.includes('notifyForeign') && !oldVal.includes('notifyForeign')) {
                this.noticeLanguage = this.sendNoticeLanguage || 'EN';
            }
        },
        noticeLanguage(val) {
            this.$emit('updateLanguage', val);
        },
        completerType: {
            handler(val) {
                this.$emit('updateCompleterType', val);
            },
            immediate: true,
        },
        // 补全人非第一签署人，强制切换到"补全其他"
        vRouteOrder: {
            handler(order) {
                if (this.recipient.receiverType === 'EDITOR' && order !== 1) {
                    this.completerType = 1;
                }
            },
            immediate: true,
        },
    },
    inject: {
        indexFunc: {
            default: () => 0,
        },
        getCrossPlatformType: {
            default: () => '',
        },
    },
    methods: {
        ...mapActions('template', ['updateRecipientByIndex']),
        changeEditorComplement(value) {
            // 先配置补全人才可以再选择
            if (value && !this.existSendedEditRole) {
                this.$MessageToast.info(this.$t('sendedEdit.editorConfigFirstTip'));
            }
            if (value) {
                this.spliceErr('signInstructionDocumentInfo');
                this.spliceErr('signInstructionOriginDocumentInfo');
                this.spliceErr('signInstructionZipInfo');
            }
        },
        updateRecipient(data) {
            this.updateRecipientByIndex({ data, index: this.index, crossPlatformType: this.crossPlatformType });
        },
        delHandle(name) {
            if (name === 'notifyForeign') {
                this.noticeLanguage = '';
            }
            this.$emit('update:visibleItems', this.visibleItems.filter(item => item !== name));
        },
        spliceErr(key) {
            if (!this.vErrors.length) {
                return;
            }
            const i = this.vErrors.findIndex(item => item.fieldName === key);
            i > -1 && this.vErrors.splice(i, 1);
        },
        updatePrivateMessage(data) {
            this.updateRecipient({
                'communicateInfo.privateLetter': data.privateLetter,
                'communicateInfo.signInstructionDocumentInfo': data.signInstructionDocumentInfo,
                'communicateInfo.signInstructionOriginDocumentInfo': data.signInstructionOriginDocumentInfo,
                'communicateInfo.signInstructionZipInfo': data.signInstructionZipInfo,
            });
        },
        changePrivateMessageConfig(changeContent) {
            this.updateRecipient(changeContent);
        },
        // 合同附属资料变更
        handleAttachList(attachList) {
            this.updateRecipient({
                'attachRequires': attachList,
            });
        },
        handleViewGuide() {
            this.$emit('openCompleteConfigGuide');
        },
        errorHandle(has) {
            this.$emit('error', has);
        },
        updateDdl(ddl) {
            this.updateRecipient({
                'signerConfig.ddl': ddl,
            });
        },
        updatePassword(password) {
            this.updateRecipient({
                'encryptionSign.encryptionSignPassword': password,
            });
        },
        initCompleterType() {
            if (this.ifExistAttachmentLabel) {
                this.completerType = this.receiverCompleterType;
            } else {
                this.completerType = 1; // 未配置附件字段，强制切换到"补全其他"
            }
        },
    },
    created() {
        this.noticeLanguage = this.sendNoticeLanguage;
        this.recipient.receiverType === 'EDITOR' && this.initCompleterType();
    },
};
</script>

<style lang="scss">
.extends-collapses {
.err{
    color: #FF5500;;
}
  &.en-page{
    .el-collapse-item{
      .notify-checkbox{
        left: 260px;
      }
    }
  }
  &.ja-page{
    .el-collapse-item{
      .notify-checkbox{
        left: 188px;
      }
    }
  }
    padding: 0 20px;
  .el-collapse-item{
    position: relative;
    .notify-checkbox{
      position: absolute;
      top: 0;
      left: 140px;
      line-height: 41px;
      .el-checkbox__label{
        font-size: 12px;
      }
    }
    .extends-item__content {
        .el-radio__label {
            font-size: 12px;
        }
    }
  }

    .extends-title-gray {
        font-size: 12px;
        color: $--color-text-primary;
        line-height: 20px;
    }
    .el-textarea {
        .el-textarea__inner {
            padding: 10px;
        }
    }
    .editor-select {
        width: 150px;
    }
    .extends-upload {
        .add {
            color: $--color-primary-light-2;
            display: inline-block;
            padding: 5px 0;
            cursor: pointer;
            b {
                font-weight: 700;
            }
        }
        .tips {
            color: $--color-text-secondary;
        }
        .el-upload-list__item:first-child {
            margin-top: 0;
            font-size: 12px;
            color: $--color-text-primary;
            line-height: 16px;
        }
    }
}
</style>
