<template>
    <Header
        class="receiver-header-cpn"
        :color="color"
        :label="label"
        :edit-receiver-id="recipient.editReceiverId"
        :receiverId="recipient.receiverId"
        :recipient="recipient"
        :show-check-box="showCheckBox"
        :show-sended-edit-check="showSendedEditCheck"
        :show-batch-succ="showBatchSucc"
        :checked.sync="vChecked"
        :sended-edit-checked.sync="vSendedEditChecked"
        :auth-data="data.dropdownAuth.data"
        :type-data="data.dropdownSignType.data"
        :extends-data="data.dropdownExtends.data"
        :auth-selected.sync="vAuthSelected"
        :type-selected.sync="vTypeSelected"
        :extends-selected.sync="vExtendsSelected"
        :batch-num="recordCount"
        :template-status="templateStatus"
        :is-cancel="isCancel"
        @conflict="conflictHandle"
        @clickView="clickViewHandle"
    ></Header>
</template>

<script>
import Header from 'components/receiverItem/components/Header';
import { mapGetters, mapState } from 'vuex';
import { isShowFaceVerify, isShowFaceAndMsgVerify } from 'src/utils/checkReceiverHeader';
export default {
    name: 'ReceiverHeader',
    components: {
        Header,
    },
    props: {
        color: String,
        checked: {
            default: false,
            type: Boolean,
        },
        sendedEditChecked: {
            default: false,
            type: Boolean,
        },
        authSelected: {
            default: '',
            type: String,
        },
        signTypeSelected: {
            default: '',
            type: String,
        },
        extendsSelected: {
            default: () => [],
            type: Array,
        },
        recipient: Object,
        templateStatus: {
            default: 'edit',
            type: String,
        },
        existSendedEditRole: {
            default: false,
            type: Boolean,
        },
        isCancel: {
            default: false,
            type: Boolean,
        },
    },
    // 相差4层，既不想用store也不想用props层层传递
    inject: {
        recordCountFunction: {
            default: () => 0,
        },
        batchSuccFunction: {
            default: () => false,
        },
    },
    computed: {
        ...mapState({
            isBatchSend: state => state.template.isBatchSend,
            needReadBeforeSign: state => state.sendingPrepare.needReadBeforeSign,
            extendFields: state => state.commonHeaderInfo.extendFields,
            internalSignInfoVO: state => state.template.internalSignInfoVO,
            ifCrossPlatform: state => state.template.ifCrossPlatform,
            needStandard: state => state.template.needStandard,
            singleDocumentFlag: state => state.template.singleDocumentFlag,
            notCrossPlatformIsJa: state => state.template.notCrossPlatformIsJa,
        }),
        ...mapGetters(['checkFeat', 'getHybridUserType', 'getIsForeignVersion']),
        isInnerSignature() {
            return this.internalSignInfoVO.isInternalSignTemplate;
        },
        receiverIsJa() {
            return (this.ifCrossPlatform && this.recipient.owningPlatform === 'ja') || (!this.ifCrossPlatform && this.getIsForeignVersion);
        },
        recordCount() {
            return this.recordCountFunction && this.recordCountFunction();
        },
        batchSucc() {
            return this.batchSuccFunction && this.batchSuccFunction();
        },
        label() {
            return this.recipient.userType === 'ENTERPRISE' ? this.$t('receiverItemHeader.signerEnt') : this.$t('receiverItemHeader.signerPerson');
        },
        data() {
            const listPermission = { ...this.listShow };
            const dropdownSignTypeData = this.getIsForeignVersion ? [
                { label: this.$t('receiverItemHeader.sign'), value: 'sign', name: 'method' },
                { label: this.$t('receiverItemHeader.entSign'), value: 'entSign', name: 'method' },
                { label: this.$t('receiverItemHeader.stampSign'), value: 'stampSign', name: 'method' },
                { label: this.$t('receiverItemHeader.stamp'), value: 'stamp', name: 'method' },
                { label: this.$t('receiverItemHeader.requestSeal'), value: 'requestSeal', name: 'method' },
                { label: this.$t('receiverItemHeader.editor'), value: 'editor', name: 'method' },
                { label: this.$t('receiverItemHeader.cc'), value: 'cc', name: 'method' },
                { label: this.$t('receiverItemHeader.scanSign'), value: 'scanSign', name: 'method', tips: this.$t('receiverItemHeader.scanSignTip')  },
            ] : [
                { label: this.$t('receiverItemHeader.sign'), value: 'sign', name: 'method' },
                { label: this.$t('receiverItemHeader.stamp'), value: 'stamp', name: 'method' },
                { label: this.$t('receiverItemHeader.stampSign'), value: 'stampSign', name: 'method' },
                { label: this.$t('receiverItemHeader.entSign'), value: 'entSign', name: 'method' },
                { label: this.$t('receiverItemHeader.requestSeal'), value: 'requestSeal', name: 'method' },
                { label: this.$t('receiverItemHeader.editor'), value: 'editor', name: 'method' },
                { label: this.$t('receiverItemHeader.cc'), value: 'cc', name: 'method' },
                { label: this.$t('receiverItemHeader.scanSign'), value: 'scanSign', name: 'method', tips: this.$t('receiverItemHeader.scanSignTip')  },
            ];
            return {
                dropdownAuth: {
                    data: [
                        { label: this.$t('receiverItemHeader.needAuth'), value: 'needAuthPerson', name: 'authPerson' },
                        { label: this.$t('receiverItemHeader.notNeedAuth'), value: 'notNeedAuthPerson', name: 'authPerson' },
                        { label: this.$t('receiverItemHeader.needAuthEnt'), value: 'needAuthEnt', name: 'authEnt' },
                        { label: this.$t('receiverItemHeader.notNeedAuthEnt'), value: 'notNeedAuthEnt', name: 'authEnt' },
                        { label: this.$t('receiverItemHeader.notNeedAuth'), value: 'notNeedAuthJaEnt', name: 'authEnt' }, // 日本环境下的非跨平台合同 企业签署方默认不需要实名，且不能更改
                    ].filter(item => listPermission[item.value] === true),
                },
                dropdownSignType: {
                    data: dropdownSignTypeData.filter(item => listPermission[item.value] === true),
                },
                dropdownExtends: {
                    data: [
                        {
                            label: `*${this.$t('receiverItemHeader.signCheck')}`,
                            children: [
                                { label: this.$t('receiverItemHeader.messageAndFaceVerify'), name: 'face', value: 'messageAndFaceVerify' },
                                { label: this.$t('receiverItemHeader.faceFirst'), name: 'face', value: 'faceFirst' },
                                { label: this.$t('receiverItemHeader.faceMust'), name: 'face', value: 'faceMust' },
                                { label: this.$t('receiverItemHeader.noHand'), name: ['handwriting', 'handwritingRec'], value: 'noHand' },
                                { label: this.$t('receiverItemHeader.mustHand'), name: 'handwriting', value: 'mustHand' },
                                { label: this.$t('receiverItemHeader.notify'), name: 'notify', value: 'notify' },
                                { label: this.$t('receiverItemHeader.handwritingRec'), name: 'handwritingRec', value: 'handwritingRec' },
                                { label: this.$t('receiverItemHeader.readAll'), name: 'readAll', value: 'readAll' },
                                { label: this.$t('receiverItemHeader.ddl'), name: 'ddl', value: 'ddl' },
                                { label: this.$t('receiverItemHeader.encryptionSign'), name: 'encryptionSign', value: 'encryptionSign' },
                                { label: this.$t('receiverItemHeader.twoFactorAuthentication'), name: 'twoFactorAuthentication', value: 'twoFactorAuthentication' },
                                { label: this.$t('receiverItemHeader.signerAuthCheck'), name: 'signerAuthCheck', value: 'signerAuthCheck' },
                            ].filter(item => listPermission[item.value] === true),
                        },
                        {
                            label: `*${this.$t('receiverItemHeader.dataCollect')}`,
                            children: [
                                { label: this.$t('receiverItemHeader.attachDoc'), name: 'doc', value: 'attachDoc', tips: this.$t('receiverItemHeader.attachDocTips') },
                                { label: this.$t('receiverItemHeader.mainDoc'), name: 'doc', value: 'mainDoc', tips: this.$t('receiverItemHeader.mainDocTips') },
                            ].filter(item => listPermission[item.value] === true),
                        },
                        {
                            label: `*${this.$t('receiverItemHeader.other')}`,
                            children: [
                                { label: this.receiverIsJa ? this.$t('receiverItemHeader.notifyOffJa') : this.$t('receiverItemHeader.notifyOff'), name: 'notifyConfig', value: 'notifyOff' },
                                { label: this.$t('receiverItemHeader.notifyForeign'), name: 'notifyConfig', value: 'notifyForeign', tips: this.$t('receiverItemHeader.notifyForeignTips') },
                                { label: this.$t('receiverItemHeader.signerPay'), name: 'contractPayer', value: 'contractPayer' },
                                { label: this.$t('receiverItemHeader.contractDownloadControl'), name: 'contractDownloadControl', value: 'contractDownloadControl' },
                                { label: this.$t('receiverItemHeader.existSummaryTask'), name: 'existSummaryTask', value: 'existSummaryTask' },
                            ].filter(item => {
                                if (listPermission[item.value] === true) {
                                    // 前端隐藏抄送方付费
                                    if (this.recipient.receiverType === 'CC_USER' && item.value === 'contractPayer') {
                                        return false;
                                    }
                                    return true;
                                }
                            }),
                        },
                    ],
                },
            };
        },
        showCheckBox() {
            return this.templateStatus === 'edit' && !this.isInnerSignature;
        },
        showSendedEditCheck() {
            return this.checkFeat.sendedEdit && this.recipient.userType === 'PERSON' && this.$route.query.isDynamic !==
                'true' && !this.isCancel && this.signTypeSelected !== 'scanSign' && !this.ifCrossPlatform;
        },
        showBatchSucc() {
            return this.templateStatus === 'use' && this.isBatchSend && this.batchSucc && this.recipient.batchAdd;
        },
        isEntSignNeedOperatorAuth() {
            // 作废声明不可做配置变更
            if (this.isCancel) {
                return false;
            }
            // 开启了对内场景不需要实名不展示该配置项
            const notRequireAuthentication = this.internalSignInfoVO?.internalSignConfigVO?.notRequireAuthentication;
            return ['stamp', 'entSign', 'stampSign', 'requestSeal'].includes(this.signTypeSelected) && !notRequireAuthentication;
        },
        // 是否显示各项
        listShow() {
            const isDynamic = this.$route.query.isDynamic === 'true';
            const recipient = this.recipient;
            // 签约角色用户类型
            const isEntReceiver = recipient.userType === 'ENTERPRISE';
            const isPersonReceiver = recipient.userType === 'PERSON';
            // 接收人类型
            const isCCReceiver = recipient.receiverType === 'CC_USER'; // 抄送人
            const isEditorReceiver = recipient.receiverType === 'EDITOR'; // 补全人
            const isSignerReceiver = recipient.receiverType === 'SIGNER'; // 签署人
            // 签署配置
            const isEntSignature = recipient.signerConfig.signType === 'ENTERPRISE_SIGNATURE'; // 企业签字
            const isSealAndSignature = recipient.signerConfig.signType === 'SEAL_AND_SIGNATURE';
            const checkFeat = this.checkFeat;
            let result = {
                // 实名
                needAuthPerson: isPersonReceiver,
                notNeedAuthPerson: isPersonReceiver,
                // needAuthEnt: recipient.userType === 'ENTERPRISE' && checkFeat.entSignNeedOperatorAuth,
                // notNeedAuthEnt: recipient.userType === 'ENTERPRISE' && checkFeat.entSignNeedOperatorAuth,
                // 日本环境下的非跨平台合同 企业签署方默认不需要实名，且不能更改
                notNeedAuthJaEnt: isEntReceiver &&  this.notCrossPlatformIsJa,
                // 使用方式
                sign: isPersonReceiver,
                entSign: isEntReceiver && checkFeat.enterpriseSignature,
                stamp: isEntReceiver,
                stampSign: isEntReceiver,
                requestSeal: isEntReceiver && checkFeat.requestSeal && !isDynamic,
                scanSign: isPersonReceiver && checkFeat.scanSign && !isDynamic && !recipient.editable,
                // 乐高城、非动态模板
                cc: true,
                // 补全：乐高城开启，不在存在一个非自己补全角色，仅限企业的非动态模板
                editor: !(this.existSendedEditRole && !isEditorReceiver) && isEntReceiver && checkFeat.sendedEdit &&
                    !isDynamic,
                // 更多
                messageAndFaceVerify: isShowFaceAndMsgVerify(recipient), // 刷脸+验证码校验
                faceFirst: isShowFaceVerify(recipient),
                // 同上
                faceMust: isShowFaceVerify(recipient),
                noHand: (recipient.realNameAuthentication.requireIdentityAssurance || checkFeat.mustAuthPersonSign) &&
                    isPersonReceiver && !isCCReceiver,
                mustHand: (isPersonReceiver && !isCCReceiver) || ((isEntSignature || isSealAndSignature) &&
                    !this.isInnerSignature),
                // 个人签署、企业签字、盖章并签字
                notify: !isEditorReceiver && !isCCReceiver,
                handwritingRec: (isPersonReceiver && !isCCReceiver) || isEntSignature || isSealAndSignature,
                readAll: this.needReadBeforeSign && !isCCReceiver,
                attachDoc: !isCCReceiver,
                mainDoc: !isCCReceiver && !isEntSignature && !isDynamic,
                notifyOff: true,
                notifyForeign: true,
                // 能够使用的条件:1.当前接收人是签署人、或者抄送人 2.乐高城开启了签署方付费
                contractPayer: (isSignerReceiver || isCCReceiver) && this.checkFeat.singerPay,
                contractDownloadControl: checkFeat.contractDownload && !isCCReceiver,
                ddl: checkFeat.ddl && !isEditorReceiver && !isCCReceiver,
                signerAuthCheck: isEntReceiver && this.isEntSignNeedOperatorAuth,
                existSummaryTask: this.singleDocumentFlag && checkFeat.hubbleContractExtract && !this.isBatchSend && this.templateStatus === 'use' && !!this.recipient.receiverId,
            };
            if (this.isCancel) {
                result = {
                    notify: true,
                };
            }
            if (this.receiverIsJa) {
                result = {
                    ...result,
                    encryptionSign: !isCCReceiver, // 抄送人不支持
                    // twoFactorAuthentication: !isCCReceiver,  // 抄送人不支持
                    contractDownloadControl: false,
                    scanSign: false,
                    noHand: false,
                    // notifyOff: false,
                    messageAndFaceVerify: false,
                    faceFirst: false,
                    faceMust: false,
                    mustHand: false,
                    handwritingRec: false,
                    notifyForeign: false,
                    signerAuthCheck: false,
                    attachDoc: false,
                    mainDoc: false,
                };
            }
            if (this.ifCrossPlatform) { // 跨平台模版签署人
                result = {
                    ...result,
                    needAuthEnt: recipient.userType === 'ENTERPRISE' && this.needStandard,
                    notNeedAuthEnt: recipient.userType === 'ENTERPRISE' && !this.needStandard,
                    entSign: false,
                    stampSign: false,
                    requestSeal: false,
                    scanSign: false,
                    editor: false,
                    messageAndFaceVerify: false,
                    handwritingRec: false,
                    ddl: false,
                    attachDoc: false,
                    mainDoc: false,
                    notifyOff: false,
                    notifyForeign: false,
                    contractPayer: false,
                    contractDownloadControl: false,
                };
            }
            return result;
        },
        vChecked: {
            get() {
                return this.checked;
            },
            set(v) {
                this.$emit('update:checked', v);
            },
        },
        vSendedEditChecked: {
            get() {
                return this.sendedEditChecked;
            },
            set(v) {
                this.$emit('update:sendedEditChecked', v);
            },
        },
        vAuthSelected: {
            get() {
                return [this.authSelected];
            },
            set(v) {
                this.$emit('update:authSelected', v[0]);
            },
        },
        vTypeSelected: {
            get() {
                return [this.signTypeSelected];
            },
            set(v) {
                this.$emit('update:signTypeSelected', v[0]);
            },
        },
        vExtendsSelected: {
            get() {
                return this.extendsSelected;
            },
            set(v) {
                this.$emit('update:extendsSelected', v);
            },
        },
        // 判断和公有云 public 搭边的 getHybridUserType 全都不显示小图标
        hybridUser() {
            return (
                this.getHybridUserType !== 'public' &&
                this.getHybridUserType !== 'publicHybrid' &&
                this.getHybridUserType !== 'publicAliyun'
            );
        },
    },
    watch: {
        // 当某些项不需要展示时，父组件也要将这些项设置为false
        listShow(newValue, oldValue) {
            const turnFalseItems = Object.keys(newValue).filter(key => !newValue[key] && newValue[key] !== oldValue[key]);
            this.$emit('listOff', turnFalseItems);
        },
    },
    methods: {
        clickViewHandle() {
            this.$bus.emit('clickView', JSON.parse(JSON.stringify(this.recipient)));
        },
        conflictHandle(name) {
            switch (name) {
                // 刷脸的三个互斥
                case 'messageAndFaceVerify':
                    this.mutexMsgToast(this.$t('receiverItemHeader.messageAndFaceVerify'));
                    break;
                case 'faceFirst':
                    this.mutexMsgToast(this.$t('receiverItemHeader.faceFirst'));
                    break;
                case 'faceMust':
                    this.mutexMsgToast(this.$t('receiverItemHeader.faceMust'));
                    break;
                // 手写的两个互斥
                case 'noHand':
                    this.mutexMsgToast(this.$t('receiverItemHeader.noHand'));
                    break;
                case 'mustHand':
                    this.mutexMsgToast(this.$t('receiverItemHeader.mustHand'));
                    break;
                case 'attachDoc':
                    this.mutexMsgToast(this.$t('receiverItemHeader.attachDoc'));
                    break;
                case 'mainDoc':
                    this.mutexMsgToast(this.$t('receiverItemHeader.mainDoc'));
                    break;
                case 'notifyOff':
                    this.mutexMsgToast(this.$t('receiverItemHeader.notifyOff'));
                    break;
                case 'notifyForeign':
                    this.mutexMsgToast(this.$t('receiverItemHeader.notifyForeign'));
                    break;
                case 'handwritingRec':
                    this.mutexMsgToast(this.$t('receiverItemHeader.handwritingRec'));
                    break;
                default:
                    break;
            }
            const msg = {
                messageAndFaceVerify: '刷脸+验证码校验',
                faceFirst: '优先刷脸，备用验证码签署',
                faceMust: '必须刷脸签署',
                noHand: '使用上上签系统签名',
                mustHand: '必须手写签名',
                attachDoc: '添加合同附属资料',
                mainDoc: '提交签约主体资料',
                notifyOff: '关闭/短信邮件通知',
                notifyForeign: '使用外文通知',
                handwritingRec: '开启笔记识别',
            };
            const isDynamic = this.$route.query.isDynamic === 'true';
            this.$sensors.track({
                eventName: this.templateStatus === 'use' ? 'Ent_ContractSendDetailWindow_PopUp' : 'Ent_TemplateCreateWindow_PopUp',
                eventProperty: {
                    page_name: this.templateStatus === 'use' ? '配置签约方' : '预置签约方',
                    window_name: `已设置“${msg[name]}”，请先删除“${msg[name]}”的设置后再选择 `,
                    ...((this.templateStatus === 'edit' || !this.isBatchSend) && {   template_type: isDynamic ? '动态模板' : '静态模板' }),
                    first_category: '指定合同签约方',
                    ...(this.templateStatus === 'use' && { is_batch_send: this.isBatchSend }),
                },
            });
        },
        mutexMsgToast(msg) {
            this.$MessageToast.error(this.$t('receiverItem.mutexMsg', { msg }));
        },
    },
};
</script>

