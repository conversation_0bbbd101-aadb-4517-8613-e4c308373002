<template>
    <transition name="slider">
        <div class="label-edit-cpn"
            v-if="focusLabelOpt.labelId && focusLabel.labelType !=='QR_CODE'"
            :class="focusLabel.labelType || ''"
        >
            <div class="edit-word-bill-label" v-if="showWordBillConfig && isFirstStep">
                <el-form ref="configForm" :model="form">
                    <!-- 名称 -->
                    <el-form-item :label="$t('customLabelEdit.labelName')" class="form-name">
                        <el-input
                            v-model="form.labelName"
                            :minlength="1"
                            :maxlength="30"
                            :disabled="true"
                        >
                        </el-input>
                    </el-form-item>
                    <!-- 字段类型 -->
                    <el-form-item :label="$t('labelEdit.wordbillLabel.selectType')">
                        <el-radio-group
                            v-model="isSignLabel"
                            @change="onConfigChange('isSignLabel')"
                        >
                            <el-radio :label="false" class="type-select">
                                {{ $t('templateSubList.sceneConfig.contentField') }}
                                <el-radio-group v-if="!isSignLabel"
                                    class="inner-select"
                                    v-model="form.labelType"
                                    @change="onConfigChange('labelType', $event)"
                                >
                                    <div v-for="item in labelTypeArr"
                                        :key="item"
                                    >
                                        <el-radio :label="item">
                                            {{ labelInfo(item).name }}
                                        </el-radio>
                                    </div>
                                    <div v-if="form.labelExtends.receiverFill">
                                        <el-radio label="DATE_TIME">
                                            {{ labelInfo('DATE_TIME').name }}
                                        </el-radio>
                                    </div>
                                </el-radio-group>
                            </el-radio>
                            <el-radio :label="true" class="type-select" v-if="form.labelExtends.receiverFill">
                                {{ $t('pointPositionSite.signField') }}
                                <el-radio-group v-if="isSignLabel"
                                    class="inner-select"
                                    v-model="form.labelType"
                                    @change="onConfigChange('labelType', $event)"
                                >
                                    <div v-for="item in signLabelTypeArr"
                                        :key="item"
                                    >
                                        <el-radio :label="item">
                                            {{ labelInfo(item).name }}
                                        </el-radio>
                                    </div>
                                </el-radio-group>
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>

                <div class="next-step" v-if="showWordBillConfig">
                    <el-button type="primary" @click="stepSwitch">{{ $t('templateCommon.next') }}</el-button>
                </div>
            </div>
            <template v-else>
                <div class="type"><i class="type-icon" :class="iconClass"></i>{{ editTypeName }}</div>
                <!-- 更改签约方下拉框 -->
                <template v-if="CHANGE_RECEIVER_TYPES.includes(focusLabel.labelType) || (form.wordLabel &&
                    form.labelType === 'DATE')"
                >
                    <div class="receiver">
                        <!-- 接收方 -->
                        <div class="title">{{ $t('labelEdit.receiver') }}</div>
                        <span class="signers-opt-circle"
                            :style="computeStyleByIndex(signerIndex)"
                        >
                        </span>
                        <el-select class="signers-opt"
                            size="small"
                            v-model="signerIndex"
                            @change="changeSigner"
                            @visible-change="onSignerChangeVisible"
                            popper-class="sign-el-select label-edit-signers-select"
                            :disabled="(isSpecialSeal || focusLabel.ifDecorate) && !form.wordLabel"
                        >
                            <el-option
                                v-for="(item, index) in receivers"
                                :disabled="disableChangeSigner(item)"
                                :key="item.receiverId"
                                :label="item.showName"
                                :value="index"
                            >
                                <span class="signers-name"
                                    :style="computeStyleByIndex(index)"
                                >
                                </span>
                                <span class="signers-cirle">
                                    {{ item.showName }}
                                </span>
                            </el-option>
                        </el-select>
                    <!-- <template v-if="getIsForeignVersion && focusLabel.labelType === 'SIGNATURE'">
                            <br><br>
                            <div class="title">{{ $t('labelEdit.sealSet') }}</div>
                            <el-checkbox v-model="isSlant" @change="changeSlant"></el-checkbox><span>{{ $t('labelEdit.slant') }}</span>
                    </template> -->
                    </div>
                    <p v-if="focusLabel.pageType === 'ALL' && templateStatus === 'use'" class="seal-location-tip">
                        {{ $t('customLabelEdit.labelChangeAllPageTip') }}
                    </p>
                </template>
                <div class="sync-config" v-if="isShowSignPositionSync">
                    <span class="sync-text">{{ $t('labelEdit.sealSyncPosition.title') }}</span>
                    <el-switch
                        v-model="syncPos"
                        :disabled="!syncPos"
                        @change="changeSyncPos"
                    >
                    </el-switch>
                    <span class="tip-access" @click="$emit('showContractChangeTipDialog')">{{ $t('labelEdit.sealSyncPosition.description') }}</span>
                </div>
                <template v-if="CHANGE_TYPES.includes(focusLabel.labelType)">
                    <CustomLabelEdit class="receiver"
                        :immediateSave="true"
                        :key="labelDiffKey"
                        :receivers="receivers"
                        :editorMetaData="editorMetaData"
                        :currentPage="currentPage"
                        :isWordBillLabel="form.wordLabel"
                        @change-value="onMarkValueChange"
                        :canChangeReceiver="canChangeReceiver"
                        @change-location="onLocationChange"
                        :isBlankDocument="currentDoc.blankDocument"
                    >
                    </CustomLabelEdit>
                    <p
                        v-if="!['DATE','SINGLE_BOX','MULTIPLE_BOX','PICTURE', 'CONFIRMATION_REQUEST_REMARK'].includes(focusLabel.labelType) && !form.wordLabel"
                        class="business_field_info"
                    >
                        <!-- 请注意： -->
                        <b>{{ $t('labelEdit.info.0') }}</b>
                        <br>{{ $t('labelEdit.info.1') }}
                        <br>{{ $t('labelEdit.info.2') }}
                    </p>
                    <!-- 说明文案 -->
                    <p class="business_field_info" v-if="['CONFIRMATION_REQUEST_REMARK'].includes(focusLabel.labelType)">
                        {{ $t('labelEdit.info.3') }}
                    </p>
                </template>
                <div class="auto-position-container" v-if="isShowPositionConfig">
                    <p class="auto-position-container-tip">{{ $t('labelEdit.blankTip') }}</p>
                    <div class="keyword-position">
                        <!--  字段坐标位置 -->
                        <el-radio class="container-title" v-model="autoPositionRadio" label="keywordPosition" @change="onPositionRadioChange('keywordPosition')">{{ $t('labelEdit.autoPosition') }}<CommonTip
                            effect="dark"
                            :content="$t('labelEdit.autoPositionTip')"
                            placement="top"
                        /></el-radio>
                        <p class="title_sub">{{ $t('labelEdit.keywordMatch') }}</p>
                        <el-form :disabled="!['keywordPosition'].includes(autoPositionRadio)">
                            <!-- 合同中的关键字 -->
                            <el-form-item :label="$t('labelEdit.keyword')" class="form-name">
                                <el-input :placeholder="$t('labelEdit.keyword')"
                                    v-model="keywordPosition.keyword"
                                    :minlength="1"
                                    :maxlength="30"
                                    @input="onValueChangeImmediate('label', 'keyword')"
                                >
                                </el-input>
                            </el-form-item>
                            <!-- 第几处关键字 -->
                            <el-form-item :label="$t('labelEdit.keywordNum')" class="form-name">
                                <el-input-number :label="$t('labelEdit.keywordNumPlaceHolder')"
                                    v-model="keywordPosition.appearOrder"
                                    :min="1"
                                    :max="50"
                                    @change="onValueChangeImmediate('label', 'appearOrder')"
                                >
                                </el-input-number>
                            </el-form-item>
                            <el-form-item :label="$t('labelEdit.keywordMove')" class="form-name">
                                <p class="keyword-position-move">{{ $t('labelEdit.keywordMoveX') }}<span>{{ (keywordPosition.moveX*100).toFixed(2) }}%</span></p>
                                <p class="keyword-position-move">{{ $t('labelEdit.keywordMoveY') }}<span>{{ (-keywordPosition.moveY*100).toFixed(2) }}%</span></p>
                            </el-form-item>
                        </el-form>
                    </div>
                    <!--  固定坐标位置 -->
                    <div v-if="isShowPagefulPosition" class="static-position-container">
                        <el-radio class="container-title title static-position-radio"
                            v-model="autoPositionRadio"
                            label="pageType"
                            @change="onPositionRadioChange('pageType')"
                        >
                            {{ $t('labelEdit.allPage') }}<CommonTip
                                effect="dark"
                                :content="$t('labelEdit.allPageTip')"
                                placement="top"
                            />
                        </el-radio>
                        <p class="title_sub"><template v-if="lang === 'zh'">{{ $t('labelEdit.Projection') }}: </template>{{ $t('labelEdit.staticPosition') }}</p>
                    </div>
                    <div class="excel-header-position" v-if="isShowExcelHeader">
                        <el-radio class="title" v-model="autoPositionRadio" label="excelHeadKeyword" @change="onPositionRadioChange('excelHeadKeyword')">{{ $t('labelEdit.excelHeaderPosition.title') }}</el-radio>
                        <el-form class="header-position-form" :disabled="!['excelHeadKeyword'].includes(autoPositionRadio)">
                            <!-- 表头关键字 -->
                            <el-form-item :label="$t('labelEdit.excelHeaderPosition.keyword')">
                                <CommonTip
                                    class="position-form-tips"
                                    effect="dark"
                                    placement="top"
                                >
                                    <template #content>
                                        <p>{{ $t('labelEdit.excelHeaderPosition.headerKeyword') }}</p>
                                        <br />
                                        <p v-for="item in 3" :key="item">{{ $t(`labelEdit.excelHeaderPosition.headerKeywordTipsList.${item-1}`) }}</p>
                                        <br />
                                        <p>{{ $t('labelEdit.excelHeaderPosition.result') }}</p>
                                        <img class="position-form-img" style="width: 462px" src="~img/template/goods-verify-excel.png" alt="">
                                    </template>
                                </CommonTip>
                                <el-input :placeholder="$t('labelEdit.excelHeaderPosition.keywordPlaceHolder')"
                                    v-model="excelHeadKeyword.keyword"
                                    :minlength="1"
                                    :maxlength="30"
                                    @input="onValueChangeImmediate('excel', 'keyword')"
                                >
                                </el-input>
                            </el-form-item>
                            <!-- 第几处关键字 -->
                            <el-form-item :label="$t('labelEdit.excelHeaderPosition.keywordNum')">
                                <el-input-number :label="$t('labelEdit.keywordNumPlaceHolder')"
                                    v-model="excelHeadKeyword.appearOrder"
                                    :min="1"
                                    @change="onValueChangeImmediate('excel', 'appearOrder')"
                                >
                                </el-input-number>
                            </el-form-item>
                            <!-- 参照列列名 -->
                            <el-form-item :label="$t('labelEdit.excelHeaderPosition.referenceCol')">
                                <CommonTip
                                    class="position-form-tips"
                                    effect="dark"
                                    placement="top"
                                >
                                    <template #content>
                                        <p>{{ $t('labelEdit.excelHeaderPosition.headerKeyword') }}</p>
                                        <br />
                                        <p v-for="item in 3" :key="item">{{ $t(`labelEdit.excelHeaderPosition.setReferenceColTips.${item-1}`) }}</p>
                                    </template>
                                </CommonTip>
                                <el-input :placeholder="$t('labelEdit.excelHeaderPosition.referenceColPlaceHolder')"
                                    v-model="excelHeadKeyword.referenceColumn"
                                    :minlength="1"
                                    :maxlength="30"
                                    @input="onValueChangeImmediate('excel', 'referenceColumn')"
                                >
                                </el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
                <!-- 设置签名大小 -->
                <div class="config-size"
                    v-if="focusLabel.labelType === 'SIGNATURE' || (focusLabel.labelType ===
                        'SIGNATURE' && checkFeat.sealDragScale)"
                >
                    <h3 class="config-size__title">{{ $t('customLabelEdit.signatureSize') }}</h3>
                    <el-radio-group class="config-size__radios" v-model="isSignatureSizeDefault">
                        <el-radio :label="true">{{ $t('customLabelEdit.default') }}</el-radio>
                        <el-radio :label="false" :disabled="true">{{ $t('customLabelEdit.custom') }}</el-radio>
                    </el-radio-group>
                    <div class="config-size__detail">
                        <p class="config-size__detail-row">{{ $t('customLabelEdit.labelWidth') }}：{{ signatureSize.width
                        }} px</p>
                        <p class="config-size__detail-row">{{ $t('customLabelEdit.labelHeight') }}：{{ signatureSize.height
                        }} px</p>
                    </div>
                </div>
                <!-- 设置骑缝章 -->
                <div class="riding-seal" v-if="focusLabelOpt.type === 'ridingSeal'">
                    <div class="receiver">
                        <!-- 接收方 -->
                        <div class="title">{{ $t('labelEdit.receiver') }}</div>
                        <span class="signers-opt-circle"
                            :style="computeStyleByIndex(signerIndex)"
                        >
                        </span>
                        <el-select class="signers-opt"
                            size="small"
                            v-model="signerIndex"
                            popper-class="sign-el-select label-edit-signers-select"
                        >
                            <el-option
                                v-for="(item, index) in receivers"
                                :key="item.receiverId"
                                :label="item.showName"
                                :value="index"
                            >
                                <span class="signers-name"
                                    :style="computeStyleByIndex(index)"
                                >
                                </span>
                                <span class="signers-cirle">
                                    {{ item.showName }}
                                </span>
                            </el-option>
                        </el-select>
                    </div>
                    <div class="riding-seal-header"
                        v-if="!templateConfig.supportOtherSignature"
                        @click="openRidingSealRadio = !openRidingSealRadio"
                    >
                        <span>{{ $t('labelEdit.advancedSettings') }}</span>
                        <i :class="['el-icon-ssq-xiangxiazhankaijiantou', openRidingSealRadio ? 'rotate' : '']"></i>
                    </div>
                    <template v-if="openRidingSealRadio">
                        <div class="riding-seal-content">
                            <el-radio-group class="config-size__radios" v-model="singleDoublePage">
                                <el-radio v-for="item in ridingSealConfig" :key="item.label" :label="item.value">
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </div>
                        <p class="riding-seal-tip">*{{ $t('labelEdit.ridingSealSetTip') }}</p>
                    </template>
                </div>
                <div class="seal-tip" v-if="focusLabel.labelType === 'SEAL' && receiverSealLength>1 && !focusLabel.ifDecorate">
                    <h2>{{ $t('labelEdit.importantNotice') }}</h2>
                    <div class="tip-info">{{ $t('labelEdit.tipInfo') }}</div>
                    <CommonTip type="popover" width="408" trigger="click" placement="left" popper-class="seal-tip-popper">
                        <template #content>
                            <ul>
                                <li><strong>{{ $t('labelEdit.tipInfos.title1') }}</strong>{{ $t('labelEdit.tipInfos.con1') }}</li>
                                <li><strong>{{ $t('labelEdit.tipInfos.title2') }}</strong>{{ $t('labelEdit.tipInfos.con2') }}</li>
                                <li><strong>{{ $t('labelEdit.tipInfos.title3') }}</strong>{{ $t('labelEdit.tipInfos.con3') }}</li>
                            </ul>
                        </template>
                        <span class="tip-btn" slot="reference">{{ $t('labelEdit.detailedInstructions') }}</span>
                    </CommonTip>
                    <br />
                    <CommonTip v-if="!getIsForeignVersion" type="popover" width="684" trigger="click" placement="left">
                        <template #content>
                            <img src="~img/template/seal-tip.png" alt="" width="684px">
                        </template>
                        <span class="tip-btn" slot="reference">{{ $t('labelEdit.operationDiagram') }}</span>
                    </CommonTip>
                </div>
                <div class="seal-tip-bottom" v-if="focusLabel.labelType === 'SEAL' && focusLabel.ifDecorate">
                    <h2>{{ $t('labelEdit.friendlyReminder') }}</h2>
                    <p>{{ $t('labelEdit.decorateTipInfos.con1') }}</p>
                    <p>{{ $t('labelEdit.decorateTipInfos.con2') }}</p>
                </div>
                <div class="next-step" v-if="showWordBillConfig">
                    <el-button type="primary" @click="stepSwitch">{{ $t('templateCommon.previous') }}</el-button>
                </div>
            </template>
        </div>
    </transition>
</template>
<script>
import {
    labelInfo,
    CHANGE_RECEIVER_TYPES,
    CHANGE_TYPES,
    computeStyle,
    computeStyleByIndex,
    FDA_WIDTH_RATE,
    FLOAT_TYPES,
} from
    'utils/labelStyle.js';

import CustomLabelEdit from 'components/customLabelEdit';

import { mapGetters, mapMutations, mapState  } from 'vuex';

import { updateLabel, updateRideSealLabel } from 'src/api/template/pointPosition.js';
import { debounce, throttle } from 'pub-utils';
import { DATE_FORMATS, TIME_FORMATS } from 'pub-consts/const';

export default {
    // 这里有个和文档不符的地方：作为props的edit.signerIndex，通过v-model改变值：
    // 首先没有警告，其次不仅子组件的edit.signerIndex发生了改变，父组件的edit.signerIndex也发生了改变，即双向绑定发生在了父子组件之间
    // 虽然文档说明：“所有的 prop 都使得其父子 prop 之间形成了一个单向下行绑定：父级 prop 的更新会向下流动到子组件中，但是反过来则不行。这样会防止从子组件意外改变父级组件的状态，从而导致你的应用的数据流向难以理解。”
    // 但是：https://cn.vuejs.org/v2/guide/components-props.html#%E5%8D%95%E5%90%91%E6%95%B0%E6%8D%AE%E6%B5%81
    components: {
        CustomLabelEdit,
    },
    props: {
        canChangeReceiver: {
            type: Boolean,
            default: false,
        },
        idxInBatch: Number,
    },
    data() {
        return {
            isSignLabel: false,
            isFirstStep: true,
            labelDiffKey: 1, // 保证数据更新后CustomEdit组件更新
            form: {},
            cacheLabelType: '', // 缓存labelType，word单据修改字段类型时，如果时签名变化为盖章，要修改receiverId
            allowChangeSigner: false,
            signerIndex: -1,
            computeStyle,
            computeStyleByIndex,
            keywordPosition: {},
            CHANGE_RECEIVER_TYPES,
            CHANGE_TYPES,
            excelHeadKeyword: {},
            // 位置配置单选 excelHeadKeyword：excel表头定位，keywordPosition：关键字定位，pageType：按页渲染字段
            autoPositionRadio: 'keywordPosition',
            isOfdTemplate: this.$route.query.isOfdTemplate === 'true',
            isBatchReplace: this.$route.query.isBatchReplace === 'true',
            isSlant: false,
            syncPos: true,
            ridingSealConfig: [
                {
                    value: 1,
                    label: this.$tc('labelEdit.ridingConfig', 1),
                },
                {
                    value: 2,
                    label: this.$tc('labelEdit.ridingConfig', 2),
                },
            ],
            openRidingSealRadio: false,
            labelTypeArr: ['TEXT', 'SINGLE_BOX', 'MULTIPLE_BOX', 'COMBO_BOX', 'BIZ_DATE', 'NUMERIC_VALUE'],
            labelInfo,
        };
    },
    computed: {
        ...mapState('template', ['receivers', 'focusLabelOpt', 'templateStatus', 'ridingSealList', 'docList',
                                 'templateConfig']),
        // ...mapGetters(['getUserPermissons']),
        ...mapGetters('template', ['currentDoc']),
        ...mapGetters(['checkFeat', 'getIsForeignVersion']),
        lang() {
            return this.$i18n.locale;
        },
        signLabelTypeArr() {
            const arr = ['DATE'];
            if (this.receivers.some(a => a.signType === 'SIGNATURE')) {
                arr.unshift('SIGNATURE');
            }
            if (this.receivers.some(a => a.signType === 'SEAL')) {
                arr.unshift('SEAL');
            }
            return arr;
        },
        showWordBillConfig() {
            return this.form.wordLabel && this.templateStatus === 'edit';
        },
        // 印章个数
        receiverSealLength() {
            let len = 0;
            this.docList.map(doc => {
                doc.labels.map(label => {
                    if (label.labelType === 'SEAL' && label.receiverId && !label.ifDecorate) {
                        len++;
                    }
                });
            });
            console.log(len);
            return len;
        },
        focusLabel() {
            if (this.focusLabelOpt.type === 'ridingSeal') {
                return {
                    receiverId: this.focusLabelOpt.ridingseal.receiverId,
                };
            }
            return this.currentDoc.labels.find(label => label.labelId === this.focusLabelOpt.labelId) || {};
        },
        currentPage() {
            const pageNumber = this.focusLabel.pageNumber;
            return (this.currentDoc.documentPages && this.currentDoc.documentPages[pageNumber - 1]) || {};
        },
        signatureSize() {
            const { width: pageWidth, height: pageHeight } = this.currentPage;
            const { width: labelWidth, height: labelHeight } = this.form?.labelPosition || {};
            return {
                width: Math.round(pageWidth * labelWidth),
                height: Math.round(pageHeight * labelHeight),
            };
        },
        isSignatureSizeDefault: { // 签名是否是默认大小（未拖拽变化大小，百分比转数值可能有误差，这里增加2px内的误差兼容）
            get() {
                return Math.abs(this.signatureSize.width - labelInfo(this.focusLabel.labelType).width) < 2;
            },
            set(v) {
                if (!v) {
                    return;
                }
                this.setSignatureDefaultSize(); // 仅默认项支持选择
            },
        },
        // 编辑区
        editTypeName() {
            if (this.focusLabelOpt.type === 'ridingSeal') {
                return this.$t('pointPositionSite.decorataRidingSeal');
            }
            return JSON.stringify(this.focusLabel) !== '{}' ? labelInfo(this.focusLabel.labelType).name : '';
        },
        iconClass() {
            let icon = '';
            switch (this.focusLabel.labelType) {
                case 'PICTURE': icon = 'el-icon-ssq-tuchuzhanweifu'; break;
                case 'SINGLE_BOX': icon = 'el-icon-ssq-danxuananniu'; break;
                case 'MULTIPLE_BOX': icon = 'el-icon-ssq-fuxuankuang'; break;
                case 'COMBO_BOX': icon = 'el-icon-ssq-xialakuang'; break;
                case 'NUMERIC_VALUE': icon = 'el-icon-ssq-shuzi'; break;
                default:icon = 'el-icon-ssq-wenben';
            }
            return icon;
        },
        // 自定义修改
        editorMetaData() {
            const focusLabel = this.focusLabel;
            if (JSON.stringify(focusLabel) === '{}') {
                return;
            }
            const labelPosition = focusLabel.labelPosition;
            return {
                ...focusLabel,
                labelPosition: {
                    ...labelPosition,
                    width: (labelPosition.width * this.currentPage.width).toFixed(),
                    height: (labelPosition.height * this.currentPage.height).toFixed(),
                    vWidth: labelPosition.width,
                    vHeight: labelPosition.height,
                    // 签署位置的绝对值
                    vX: Math.round((labelPosition.x) * this.currentPage.width),
                    vY: Math.round((1 - labelPosition.y) * this.currentPage.height),
                },
            };
        },
        // 是否展示位置配置模块
        isShowPositionConfig() {
            const { blankDocument, billDocument, documentType } = this.currentDoc;
            // 单据不能是word单据
            return (blankDocument || (billDocument && documentType !== 4)) && this.focusLabelOpt.type !==
                'ridingSeal';
        },
        isShowPagefulPosition() {
            const labelType = this.form.labelType;
            // 仅限乐高城开启开关场景下的签署人填写的文本、日期、图片、签字、印章、签署日期
            return !this.isOfdTemplate && this.checkFeat.pagefulPosition &&
                (['SEAL', 'SIGNATURE', 'DATE'].includes(labelType) ||
                    (['TEXT', 'BIZ_DATE', 'PICTURE'].includes(labelType) && this.form.labelExtends.receiverFill));
        },
        // 是否显示excel表头关键字
        isShowExcelHeader() {
            // 乐高城开启【excel表头关键字】开关 【空白文档】中的【文本、单选、数字】标签设置【签署人】信息时展示
            return this.checkFeat.excelHeadKeyword && ['TEXT', 'SINGLE_BOX', 'NUMERIC_VALUE'].includes(this.focusLabel.labelType) && this.editorMetaData.labelExtends.receiverFill;
        },
        isShowSignPositionSync() {
            // 仅支持批量发不同合同场景下，所有字段
            return this.isBatchReplace && this.focusLabelOpt.type !== 'ridingSeal';
        },
        // 骑缝章配置操作
        singleDoublePage: {
            get() {
                return this.focusLabelOpt.ridingseal?.singleDoublePage;
            },
            set(v) {
                this.updateRidingSeal(v);
            },
        },
        isSpecialSeal() {
            const specialSealInfo = this.focusLabel.specialSealInfo || {};
            return  this.focusLabel.labelType === 'SEAL' && specialSealInfo.type && specialSealInfo.type !== 'NONE';
        },

    },
    watch: {
        focusLabel(v, ov) {
            if (JSON.stringify(v) !== '{}') {
                if (v.labelId !== ov.labelId) {
                    this.isFirstStep = true;
                }
                this.isSlant = !!v.labelExtends?.tiltDegrees;
                this.form = JSON.parse(JSON.stringify(v)); // 缓存form数据
                this.signerIndex = this.receivers.findIndex(item => item.receiverId === v.receiverId);
                this.keywordPosition = { ...v.keywordPosition } || {};
                this.excelHeadKeyword = { ...v.excelHeadKeyword } || {};
                if (this.isShowSignPositionSync) { // 批量发不同合同，默认同步所有合同
                    this.syncPos = v.labelExtends?.ap !== 1 && !v.notSignPosSync;
                }
                this.isSignLabel = this.signLabelTypeArr.includes(this.form.labelType);

                this.cacheLabelType = this.form.labelType;

                if (this.form.labelExtends.ifExcelKeywordSet) { // excel表头定位
                    this.autoPositionRadio = 'excelHeadKeyword';
                } else if (this.form.pageType) { // 按页渲染字段
                    this.autoPositionRadio = 'pageType';
                } else { // 关键字定位
                    this.autoPositionRadio = 'keywordPosition';
                }
            }
        },
    },
    methods: {
        ...mapMutations({
            setFocusLabelOpt: 'template/setFocusLabelOpt',
        }),
        ...mapMutations('template', ['setRidingSealList']),
        upSameLabel(data) {
            data.forEach((updateItem) => {
                let index = -1;
                let docI = -1;
                for (let i = 0; i < this.docList.length; i++) {
                    index =  this.docList[i].labels.findIndex((a) => a.labelId === updateItem.labelId);
                    if (index > -1) {
                        docI = i;
                        break;
                    }
                }
                if (index === -1) { // 新增的标签
                    this.currentDoc.labels.push(updateItem);
                    this.setFocusLabelOpt({
                        labelId: updateItem.labelId,
                        labelButtonInd: 0,
                    });
                } else {
                    this.$set(this.docList[docI].labels, index, updateItem);
                }
            });
            if (data.length - 1) {
                this.$MessageToast.info(this.$t('pointPosition.synLabel', { num: data.length - 1 })); // `已将${data.length - 1}个同名字段属性更新为当前字段属性值` 更新同名字段属性提示
            }
        },
        onConfigChange(key) {
            if (key === 'isSignLabel') {
                this.form.labelType = this.isSignLabel ? 'SEAL' : 'TEXT';
            }
            const { labelType, receiverId, labelExtends = {} } = this.form;
            const { dateFieldFormat, decimalPlace } = labelExtends;
            const { width: defaultLabelWidth, height: defaultLabelHeight, button } = labelInfo(this.form.labelType);

            this.form.labelPosition.width = defaultLabelWidth / this.currentPage.width;
            this.form.labelPosition.height = defaultLabelHeight / this.currentPage.height;
            if (!this.form.labelExtends.pxFontSize) {
                this.form.labelExtends.pxFontSize = 14;
            }
            if (labelType === 'DATE_TIME') {
                labelExtends.dateFieldFormat = dateFieldFormat || TIME_FORMATS[0].value;
                this.form.receiverId = this.receivers[0].receiverId;
                this.form.labelExtends.receiverFill = true;
            } else if (['BIZ_DATE', 'DATE'].includes(labelType)) { // 日期类型内容字段要传样式参数 SAAS-14898
                labelExtends.dateFieldFormat = dateFieldFormat ||  DATE_FORMATS[0].value;
            } else if (['NUMERIC_VALUE'].includes(labelType)) {
                labelExtends.decimalPlace = decimalPlace || 0;
            } else if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(labelType)) {
                const defaultItems = [{
                    itemX: 10,
                    itemY: 10,
                    itemValue: this.$t('pointPositionDoc.checkboxName', { count: 1 }),
                }, { itemX: 10, itemY: 41, itemValue: this.$t('pointPositionDoc.checkboxName', {
                    count: 2 }) }];
                defaultItems.forEach(item => {
                    item.itemX = item.itemX / this.currentPage.width;
                    item.itemY = this.form.labelPosition.height - (item.itemY + button.height) /
                        this.currentPage.height;
                });
                // 单、复选框选项位置增加
                this.form.labelExtends.items = defaultItems;
            } else if (['COMBO_BOX'].includes(labelType)) {
                this.form.labelExtends.items =
                    [{ itemValue: this.$t('pointPositionDoc.checkboxName', { count: 1 }) }, {
                        itemValue: this.$t('pointPositionDoc.checkboxName', { count: 2 }) }];
            } else if (FLOAT_TYPES.includes(labelType)) {
                // 由签署字段类型切换或者其他类型切换成签署字段
                if (receiverId === '0' || (this.cacheLabelType !== 'DATE' && labelType !== 'DATE' &&
                    this.cacheLabelType !== labelType)) {
                    this.form.receiverId = this.receivers.find(a => a.signType === labelType).receiverId;
                }
            }

            updateLabel(this.form).then((data) => {
                this.upSameLabel(data.length ? data : [this.form]);
                this.labelDiffKey++;
            });
        },
        stepSwitch() {
            this.isFirstStep = !this.isFirstStep;
        },
        updateRidingSeal(v) {
            const params = {
                ...this.focusLabelOpt.ridingseal,
                singleDoublePage: v,
            };
            updateRideSealLabel(params).then(() => {
                // 修改了骑缝章的单双页打印配置，对所有骑缝章生效，需要修改所有数据
                this.updateRidingSealList(v);
            });
        },
        updateRidingSealList(v) {
            const newRidingSealList = this.ridingSealList.map(item => {
                item.singleDoublePage = v;
                return item;
            });
            this.setRidingSealList(newRidingSealList);
            this.setFocusLabelOpt({
                labelId: this.focusLabelOpt.labelId,
                labelButtonInd: -1,
                type: 'ridingSeal',
                ridingseal: {
                    ...this.focusLabelOpt.ridingseal,
                    singleDoublePage: v,
                },
            });
        },
        fixBoundary(ratio, space) {
            if (ratio < 0) {
                return 0;
            }
            if (ratio > 1 - space) {
                return 1 - space;
            }
            return ratio;
        },
        setSignatureDefaultSize() {
            const { width, height } = labelInfo(this.focusLabel.labelType);
            const { width: pageWidth, height: pageHeight } = this.currentPage;
            const { x, y } = this.focusLabel.labelPosition || {};

            const newWidth = width / pageWidth;
            const newHeight = height / pageHeight;
            this.form.labelPosition.width = newWidth;
            this.form.labelPosition.height = newHeight;
            // 注意边界情况，宽高变化可能会出现签名溢出文档范围的情况
            this.form.labelPosition.x = this.fixBoundary(x, this.focusLabel?.labelExtends?.supportFDA
            ? newWidth * (1 + FDA_WIDTH_RATE) : newWidth);
            this.form.labelPosition.y = this.fixBoundary(y, newHeight);

            updateLabel(this.form).then(() => {
                this.onLocationChange(this.form.labelPosition);
                this.setFocusLabelOpt({
                    labelId: '',
                    labelButtonInd: -1,
                });
            });
        },
        // 调整印章同步其他合同：批量发不同合同场景下
        changeSyncPos(val) {
            if (!val) {
                this.$confirm(`<div class="content">${this.$t('labelEdit.sealSyncPosition.tipContent')}</div><p class="note">${this.$t('labelEdit.sealSyncPosition.noteText')}</p>`, this.$t('labelEdit.sealSyncPosition.closeTip'), {
                    type: 'warning',
                    customClass: 'sync-position-confirm un-loss-focus',
                    dangerouslyUseHTMLString: true,
                    confirmButtonText: this.$t('templateCommon.confirm'),
                    cancelButtonText: this.$t('templateCommon.cancel'),
                }).then(() => {
                    this.onMarkValueChange(this.form, 'labelExtends', 'ap');
                }).catch(() => {
                    this.syncPos = true;
                });
            }
        },
        changeSlant(val) {
            this.form.labelExtends.tiltDegrees = val ? -35 : 0;
            updateLabel(this.form).then(() => {
                this.focusLabel.labelExtends.tiltDegrees =  val ? -35 : 0;
            });
        },
        // 是否不可点击切换签署人
        disableChangeSigner(item) {
            const labelType = this.focusLabel.labelType;
            return (labelType === 'SEAL' && item.userType === 'PERSON') ||
                (labelType === 'SIGNATURE' && item.userType === 'ENTERPRISE') ||
                (labelType === 'CONFIRMATION_REQUEST_SEAL' && item.signType !== 'CONFIRMATION_REQUEST_SEAL') || (labelType === 'CONFIRMATION_REQUEST_REMARK' && item.signType !== 'CONFIRMATION_REQUEST_SEAL');
        },
        changeSigner(i) {
            if (i < 0) { // element ui bug fix
                return;
            }
            if (!this.allowChangeSigner) { // element ui bug fix
                return;
            }
            // 改变对应label的receiverId即可
            const focusLabel = this.focusLabel;
            const oriReceiverId = focusLabel.receiverId;
            focusLabel.receiverId = this.receivers[i].receiverId;
            updateLabel({
                labelId: focusLabel.labelId,
                receiverId: this.receivers[i].receiverId,
                index: this.syncPos ? undefined : this.idxInBatch, // 批量发不同合同不同步签署位置时要传合同index
            }).catch(() => {
                focusLabel.receiverId = oriReceiverId;
            });
        },
        onSignerChangeVisible(isShow) { // element ui bug fix
            this.allowChangeSigner = isShow;
        },
        // 文本
        onMarkValueChange(res, k, nextK) {
            if (k === 'labelName' && !res.labelName) {
                return this.$MessageToast.error(this.$t('labelEdit.nameError')); // 请填写名称！
            }

            if (k === 'labelExtends' && nextK === 'receiverFill') { // 切换字段填写人时重置数据
                this.form.pageType = null; // 发件方填写字段不支持按页渲染
                res.pageType = null;
            }
            if (!this.focusLabel.labelId) {
                return;
            }
            if (nextK === 'ap') { // 前端临时存储同步标记
                this.focusLabel.notSignPosSync = !this.syncPos;
            }
            const metaData = {
                labelId: this.focusLabel.labelId,
                ...res,
            };
            this.$emit('save-meta', this.focusLabel, k, nextK, metaData);
        },
        onValueChangeImmediate: debounce(function(type, k) {
            if (type === 'excel') {
                return this.onExcelHeaderChange(k);
            }
            this.onChange(k);
        }, 400),
        onChange(k) {
            if (this.keywordPosition[k] === this.focusLabel.keywordPosition[k]) { // 利用缓存数据，在名称未发生变化时，直接返回，不触发数据更新
                return;
            }

            const data = { keywordPosition: {} };
            data.keywordPosition = this.keywordPosition;
            data.excelHeadKeyword = this.excelHeadKeyword;
            this.onMarkValueChange(data, 'keywordPosition', k);
        },
        onExcelHeaderChange(k) {
            if (this.excelHeadKeyword[k] === this.focusLabel.excelHeadKeyword[k]) {
                return;
            }

            const data = { excelHeadKeyword: {} };
            data.excelHeadKeyword = this.excelHeadKeyword;
            data.keywordPosition = this.keywordPosition;
            this.onMarkValueChange(data, 'excelHeadKeyWord', k);
        },
        onPositionRadioChange(type) {
            const EMPTY_EXCEL_HEAD_KEYWORD = {
                appearOrder: null,
                keyword: null,
                labelId: null,
                referenceColumn: null,
            };
            const EMPTY_KEYWORD_POSITION = {
                appearOrder: null,
                genFromLabelId: null,
                keyword: null,
                moveX: 0,
                moveY: 0,
                width: null,
            };
            if (type === 'keywordPosition') {
                this.form.labelExtends.ifExcelKeywordSet = false;
                this.excelHeadKeyword = { ...EMPTY_EXCEL_HEAD_KEYWORD };
                this.form.excelHeadKeyword = this.excelHeadKeyword;
                this.form.pageType = null;
                updateLabel(this.form);
            } else if (type === 'pageType') {
                // 重置另两项的值
                this.keywordPosition = { ...EMPTY_KEYWORD_POSITION };
                this.form.labelExtends.ifExcelKeywordSet = false;
                this.excelHeadKeyword = { ...EMPTY_EXCEL_HEAD_KEYWORD };
                this.form.keywordPosition = this.keywordPosition;
                this.form.excelHeadKeyword = this.excelHeadKeyword;
                this.form.pageType = 'ALL';
                this.onMarkValueChange(this.form, 'pageType');
            } else {
                this.form.labelExtends.ifExcelKeywordSet = true;
                this.keywordPosition = { ...EMPTY_KEYWORD_POSITION };
                this.form.keywordPosition = this.keywordPosition;
                this.form.pageType = null;
                updateLabel(this.form);
            }
        },
        onLocationChange(position) {
            const labelList = this.currentDoc.labels;
            const oldLabelInd = labelList.findIndex(a => a.labelId === this.focusLabel.labelId);
            const label = labelList[oldLabelInd];
            const labelPosition = {
                ...label.labelPosition,
                ...position,
            };
            this.$set(labelList, oldLabelInd, {
                ...label,
                labelPosition,
            });
        },
    },
    mounted() {
        this.updateRidingSeal = throttle(this.updateRidingSeal, 500);
    },
};
</script>
<style lang="scss">
    .seal-tip-popper{
        ul{
            list-style-type: disc;
            padding-left: 10px;
        }
    }
    .sync-position-confirm {
        .el-icon-warning {
            top: 12px;
            font-size: 14px;
        }
    }
	// 编辑区
    .en-page .label-edit-cpn .auto-position-container .keyword-position .el-form-item__label {
        text-align: left;
    }
	.label-edit-cpn {
		position: absolute;
		background-color: $--color-white;
		border-left: 1px solid $border-color;
        overflow-y: auto;
        top: 0px;
        right: 0;
        width: 221px;
        height: 100%;
        z-index: 1;
        [dir=rtl] & {
            right: auto;
            left: 0;
            border-left: none;
            border-right: 1px solid $border-color;
        }
        .edit-word-bill-label {
            padding: 10px 14px;
            .type-select {
                display: inline-block;
                padding-left: 5px;
                margin-bottom: 15px;
                width: 100%;
                vertical-align: top;
                .inner-select {
                    display: block;
                    margin: 3px 0;
                    padding: 5px 15px;
                    width: 100%;
                    line-height: 16px;
                    background: aliceblue;
                }
            }
        }
        .next-step {
            margin-top: 10px;
            text-align: center;
        }

        .sync-config {
            margin-top: 15px;
            padding-left: 15px;
            color: $--color-text-primary;
            .sync-text {
                margin-right: 10px;
            }
            .tip-access {
                margin-left: 10px;
                color: $--color-primary;
                cursor: pointer;
            }
            .el-switch {
                margin-top: -2px;
            }
            .el-switch__core {
                width: 28px!important;
            }
            [dir=rtl] & {
                padding-left: 0;
                padding-right: 15px;
                .sync-text {
                    margin-left: 10px;
                    margin-right: 0;
                }
                .tip-access {
                    margin-left: 0;
                    margin-right: 10px;
                }
            }
        }
		.disableMask{
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			background: rgba(255, 255, 255, 0.5);
			z-index: 5;
			cursor: not-allowed;
            [dir=rtl] & {
                left: auto;
                right: 0;
            }
		}
		.type {
			line-height: 38px;
			padding-left: 22px;
			font-size: 14px;
			color: $--color-text-primary;
			font-weight: bold;
			border-bottom: 1px solid $border-color;
			.type-icon {
				font-size: 16px;
				margin-right: 6px;
			}
            [dir=rtl] & {
                padding-left: 0;
                padding-right: 22px;
                .type-icon {
                    margin-right: 0;
                    margin-left: 6px;
                }
            }
		}
        .signers-opt{
            .el-input__inner {
				padding-left: 36px !important;
				border-radius: 1px;
				height: 26px;
				font-size: 12px;
                [dir=rtl] & {
                    padding-left: 0 !important;
                    padding-right: 36px;
                }
            }
        }
        .info{
            padding: 0px 14px 10px;
        }
		.receiver {
			position: relative;
			padding: 10px 14px;
			.title {
				font-size: 12px;
				color: $--color-text-primary;
				padding-bottom: 10px;
			}
            .el-select{
                width:100%;
            }
            .image-tips-input{
                .el-input__inner{
                    padding-right: 0;
                }
            }
			.el-input__icon {
				color: $--color-text-primary;
			}
			.signers-opt-circle {
				z-index: 99;
				position: absolute;
				top: 42px;
				left: 34px;
				width: 14px;
				height: 14px;
				// border: 1px solid #82C9FC;
				border-radius: 50%;
                [dir=rtl] & {
                    left: auto;
                    right: 34px;
                }
			}
        }
        .config-size {
            padding-left: 15px;
            color: $--color-text-primary;
            [dir=rtl] & {
                padding-left: 0;
                padding-right: 15px;
            }
            &__title {
                line-height: 30px;
            }
            &__radios .el-radio__label{
                font-size: 12px;
            }
            &__detail {
                margin-top: 10px;
                line-height: 24px;
            }
        }
        .seal-location-tip {
            margin: 0 15px;
            color: $--color-text-secondary;
            line-height: 16px;
        }
      .auto-position-container{
            .auto-position-container-tip{
                padding: 10px;
            }
          .container-title{
              padding-bottom: 10px;
              margin: 0;
              .el-radio__label{
                font-size: 16px;
                padding-left: 0;
              }
          }
          .el-input-number .el-input input {
              pointer-events: none; // CFD-15412
          }
          .title_sub{
                  font-size: 12px;
                  padding-bottom: 15px;
              }
          .keyword-position{
              padding: 10px 14px;
              .title{
                  font-size: 14px;
                  padding-bottom: 20px;
              }

              .el-form-item__label{
                  font-size: 12px;
                  color: $--color-text-regular;
              }
              .keyword-position-move{
                  display: inline-block;
                  width: 100%;
                  font-size: 12px;
                  span{
                      padding-left: 20px;
                      [dir=rtl] & {
                          padding-left: 0;
                          padding-right: 20px;
                      }
                  }
              }
          }
          .static-position-container{
            padding: 10px 14px;
          }
          .excel-header-position{
              padding: 10px 14px;
              .title{
                  font-size: 14px;
                  padding-bottom: 12px;
              }
              .el-form-item__label{
                  font-size: 12px;
                  color: $--color-text-regular;
              }
              .header-position-form{
                  .tips{
                      color: $--color-text-regular;
                  }
              }
          }
      }
		&.TEXT, &.DATE, &.BIZ_DATE, &.DATE_TIME, &.TEXT_NUMERIC, &.PICTURE,&.SINGLE_BOX,&.MULTIPLE_BOX{
			.receiver {
				.el-input__inner {
					padding-left: 10px;
                    [dir=rtl] & {
                        padding-left: 0;
                        padding-right: 10px;
                    }
				}
			}
        }
        // 字段使用提示
        .business_field_info {
            padding: 10px;
            border-bottom: 1px solid $border-color;
            font-size: 12px;
        }
        .seal-tip{
            padding: 10px 14px ;
             color: #333;
            h2{
                font-size: 14px;
            }
            .tip-info{
                font-size: 12px;
            }
            .tip-btn{
                display: inline-block;
                color:  $--color-primary;
                cursor: pointer;
            }
        }
        .seal-tip-bottom{
            padding: 10px 14px ;
             color: #333;
             position: absolute;
             bottom:0;
            h2{
                font-size: 14px;
            }
        }
	}
	.label-edit-signers-select {
		.signers-name {
			display: inline-block;
			position: relative;
			top: 0;
			width: 16px;
			height: 16px;
			border-radius: 50%;
		}
		.signers-cirle {
			display: inline-block;
			position: relative;
			top: -4px;
			margin-left: 8px;
            [dir=rtl] & {
                margin-left: 0;
                margin-right: 8px;
            }
		}
	}
	// 动画
	.slider-enter-active{
		transition: all .3s ease;
	}
	.slider-leave-active {
		transition: all .3s ease;
	}
	.slider-enter, .slider-leave-to /* .fade-leave-active in below version 2.1.8 */ {
		transform: translateX(210px);
	}
    .riding-seal-header {
        padding: 0 14px;
        margin-bottom: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        i {
            color: $btn-disabled-font-color;
        }
        .rotate {
            transform: rotate(180deg);
        }
    }
    .riding-seal-content {
        padding: 0 14px;
        .el-radio {
            margin-right: 0;
            margin-bottom: 10px;
        }
        .el-radio__label {
            white-space: break-spaces;
        }
    }
    .riding-seal-tip {
        padding: 0 14px;
        color: $--color-info;
    }
</style>
