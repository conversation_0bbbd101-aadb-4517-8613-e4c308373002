<!-- 签约校验弹窗-短信校验方式 -->
<template>
    <div class="validation-verCode">
        <div class="error" v-show="isErrorShow">
            <i class="login-error-i iconfont el-icon-ssq-prohibit-filling"></i>
            <div class="login-error-r">
                <span class="login-error-detail">
                    {{ errorMsg }}<span v-show="forgetPassShow">{{ $t('signValidation.if') }}<span class="highlight" @click="handleClickForget">{{ $t('signValidation.forgetPassword') }}</span></span>
                </span>
            </div>
        </div>
        <div class="certificate-tip" v-if="!hideTip">
            {{ signType === 'SEAL_AND_SIGNATURE' ? $t('signValidation.verifyAllTip'): $t('signValidation.verifyTip') }}
        </div>
        <div class="sign-va-form">
            <el-form @submit.native.prevent>
                <!-- 验证码 -->
                <el-form-item class="countdown-item" :label="sendType === 'E' ? $t('signValidation.email') : $t('signValidation.phoneNumber')">
                    <span class="phone">{{ notice ? maskNotice : $t('signValidation.setNotificationInUserCenter') }}</span>
                </el-form-item>
                <div v-if="isVerifyCodeValid" class="verify-code-wrapper">
                    <el-form-item :label="sendType == 'E' ? $t('signValidation.mailVerificationCode') : $t('signValidation.verificationCode')" class="pass-form-item">
                        <div class="verify-flex">
                            <el-input
                                class="verify-input"
                                v-model="verifyCode"
                                auto-complete="off"
                                :maxlength="6"
                                :placeholder="$t('signValidation.signPwdType')"
                            >
                                <ElIDelete slot="icon"></ElIDelete>
                            </el-input>
                            <CountDown class="countDown" :clickedFn="handleClickSendCode" :disabled="countDownDisabled" ref="btn" :second="60"></CountDown>
                        </div>
                    </el-form-item>

                    <!-- "签约密码优先"展示 -->
                    <div v-if="showSignPasswordPriority" class="valid-type-switch" @click="switchValidType">{{ $t('signValidation.useSignPsw') }}</div>

                    <!-- 非"签约密码优先"下展示，设置/使用签约密码 -->
                    <div v-if="!isSignPasswordPriorityOpen && !shouldSignVaSecondCheck && !hasFaceSignContract" class="forgetPass-form-item">
                        <span v-if="signPwdExistence" class="valid-type-switch" @click="switchValidType">{{ $t('signValidation.useSignPsw') }}</span>
                        <span v-else class="valid-type-switch" @click="$emit('openSetSignPwdDialog')">{{ $t('signValidation.setSignPsw') }}</span>
                    </div>
                </div>

                <!-- 签约密码 -->
                <div v-if="!isVerifyCodeValid || !hidePass" class="sign-password-wrapper">
                    <el-form-item class="pass-form-item" :label="$t('signValidation.signPsw')">
                        <el-input
                            type="password"
                            :maxlength="6"
                            auto-complete="new-password"
                            v-model="signPass"
                            v-focus
                            :placeholder="$t('signValidation.signPwdType')"
                        >
                            <ElIDelete slot="icon"></ElIDelete>
                        </el-input>
                    </el-form-item>
                    <div class="forgetPass-form-item">
                        <span v-if="!isVerifyCodeValid" class="valid-type-switch inline-gap" @click="switchValidType">
                            {{ $t('signValidation.useVerCode') }}</span>
                        <span @click="handleClickForget">{{ $t('signValidation.forgetPassword') }}</span>
                    </div>
                </div>

                <div class="switch">
                    <!-- 一直收不到短信？试试 -->
                    <span v-if="phoneNotice" class="voiceCodeLabel">{{ $t('signValidation.msgTip') }}</span>
                    <span v-if="phoneNotice" class="highlight" @click="handleClickVoice">{{ $t('signValidation.voiceVerCode') }}</span>
                    <span v-if="mailNotice && phoneNotice">
                        {{ $t('signValidation.or') }}
                        <span v-show="phoneNotice && sendType === 'E'" class="highlight" @click="handleClickMailAndPhone">
                            {{ $t('signValidation.SMSVerCode') }}
                        </span>
                        <span v-show="mailNotice && (sendType === 'S' || sendType === 'V') " class="highlight" @click="handleClickMailAndPhone">
                            {{ $t('signValidation.emailVerCode') }}
                        </span>
                    </span>
                    <p
                        v-if="toFaceBtnShow"
                        class="validation-verCode__toFace"
                    >
                        {{ $t('signValidation.doNotWantUseVerCode') }}？{{ $t('signValidation.try') }}
                        <span
                            class="highlight"
                            @click="handleToFace"
                        >
                            {{ $t('signValidation.goToFaceVerify') }}
                        </span>
                    </p>
                </div>
            </el-form>

            <div class="sign-pwd-remind" v-if="showSignPwdRemind && !isOldBatchSign">
                <div class="sign-pwd-remind__mask"></div>
                <img
                    class="sign-pwd-remind__img"
                    src="~img/signValidation/signPwdRemind.png"
                    alt="signPwdRemind"
                />
                <img class="sign-pwd-remind__icon"
                    src="~img/signValidation/closeIcon.png"
                    @click="handleCloseRemind"
                    alt="signPwdRemindClose"
                />
            </div>

            <el-button
                class="confirm-sign-btn"
                type="primary"
                @click="handleClickConfirmSign"
                :loading="submitDiable"
            >
                {{ $t('signValidation.submit') }}
            </el-button>
        </div>
    </div>
</template>

<script>

import ElIDelete from 'pub-components/elIconfont/elIDelete/index.vue';
import CountDown from 'pub-components/countDown/index.vue';
import { goReturnUrl } from 'pub-utils/business/returnUrl.js';
import { getMaskPhone, getMaskEmail } from 'pub-utils/regsUtils.js';
import { getNoticeType, getSendEntConfig, getSignPwdExist, getIfTriggerSignPwd } from 'src/api/components/signValidation.js';

export default {
    components: {
        ElIDelete,
        // ElIEye,
        CountDown,
    },
    props: {
        hasFaceSignContract: {
            type: Boolean,
            default: false,
        },
        ifSetSignPwdSuccess: {
            type: Boolean,
            default: false,
        },
        ssoSigning: {
            type: Object,
        },
        channel: {
            type: String,
        },
        contractId: {
            type: String,
        },
        hidePass: {
            type: Boolean,
        },
        hideTip: {
            type: Boolean,
        },
        userAccount: {
            type: String,
        },
        signerFillLabels: {
            type: Array,
            default: () => [],
        },
        isHybridCloudContract: {
            type: Boolean,
        },
        isAllSignaturesInactive: {
            type: Boolean,
        },
        returnUrl: {
            type: String,
        },
        isApplySeal: {
            type: Boolean,
        },
        signType: {
            type: String,
        },
        isOldBatchSign: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            signPass: '',
            verifyCode: '',

            notice: '',
            mailNotice: '',
            phoneNotice: '',
            countDownDisabled: false,
            verifyKey: '',
            sendType: 'S', // S：手机短信；E：邮件；V：语音

            isErrorShow: false,
            errorMsg: '',
            forgetPassShow: false,
            // 确定按钮的状态
            submitDiable: false,

            senderFaceSignOpen: false, // 发件方乐高城是否开通刷脸签署
            isVerifyCodeValid: true, // true：验证码校验，false：签约密码校验
            isSignPasswordPriorityOpen: false, // 签署人是否打开优先签约密码校验
            signPwdExistence: false, // 用户是否配置了签约密码
            showSignPwdRemind: false, // 是否展示签约密码提醒
        };
    },
    computed: {
        toFaceBtnShow() {
            // 去刷脸入口展示逻辑：当前登录账号的个人身份已实名；单份签署页面；发件方乐高城开通刷脸签署；
            return this.$store.state.commonHeaderInfo.platformUser.authStatus === 2 && this.senderFaceSignOpen && !this.isHybridCloudContract;
        },
        // 邮箱或者手机号脱敏
        maskNotice() {
            return this.sendType === 'E' ? getMaskEmail(this.notice) : getMaskPhone(this.notice);
        },
        showSignPasswordPriority() {
            // 是否展示优先签约密码校验：用户打开优先签约密码校验，并且签署要求中没有必须刷脸、刷脸优先、刷脸+验证码的配置
            return (this.isSignPasswordPriorityOpen  && !this.hasFaceSignContract);
        },
        // 是否开启了双重校验开关
        shouldSignVaSecondCheck() {
            return this.$store.getters.SIGN_SECOND_CHECK;
        },
        // 签署校验方式三种：验证码、签约密码、验证码+签约密码
        signVerifyType() {
            return this.shouldSignVaSecondCheck ? 'verifyAndPwd' : (this.isVerifyCodeValid ? 'verify' : 'pwd');
        },
    },
    watch: {
        showSignPasswordPriority: {
            handler(val) {
                this.isVerifyCodeValid = !val;
            },
            immediate: true,
        },
        ifSetSignPwdSuccess: {
            handler(val) {
                if (val) {
                    this.signPwdExistence = true;
                    this.isSignPasswordPriorityOpen = true;
                }
            },
            immediate: true,
        },
    },
    methods: {
        switchValidType() {
            this.signPass = '';
            this.verifyCode = '';
            this.verifyKey = '';
            this.isErrorShow = false;
            this.errorMsg = '';
            this.isVerifyCodeValid = !this.isVerifyCodeValid;
        },

        getContractSenderConfig() {
            // 合同发件方乐高城配置
            if (this.$route.query.contractId) {
                getSendEntConfig(this.$route.query.contractId).then(res => {
                    this.senderFaceSignOpen = ((res && res.data) || {}).canFacesign;
                });
            }
        },
        // 点击发送验证码，不知道为什么要包一层
        handleClickSendCode() {
            this.send();
        },
        recordVerifyCode() {
            return this.$http.post(`/contract-api/contracts/add-verify-code-record`, {
                contractId: null,
                receiverId: null,
            });
        },
        // 发送验证码
        send() {
            this.countDownDisabled = true;
            setTimeout(this.sended, 0);
            // sendVerCode
            this.$http.sendVerCode({ // 不传target，
                code: 'B008',
                sendType: this.sendType,
                bizTargetKey: this.$route.query.contractId, // contractId
            })
                .then((res) => {
                    this.verifyKey = res.data.value;
                    this.$MessageToast.success(this.$t('signValidation.SentSuccessfully'));
                })
                .catch(() => {
                    this.$refs.btn.reset();
                })
                .finally(() => {
                    this.recordVerifyCode().then(() => {
                        this.getIfShowSignPwdRemind();
                    });
                });
        },
        // 发送成功，开始倒计时
        sended() {
            this.$refs.btn.run();
            this.countDownDisabled = false;
        },
        // 发送语音验证码
        handleClickVoice() {
            if (this.$refs.btn.time > 0) {
                this.$MessageToast.error(this.$t('signValidation.intervalTip'));
                return;
            }
            this.sendType = 'V';
            this.notice = this.phoneNotice.code;
        },
        // 发送短信、邮件验证码
        handleClickMailAndPhone() {
            if (this.$refs.btn.time > 0) {
                this.$MessageToast.error(this.$t('signValidation.intervalTip'));
                return;
            }
            if (this.sendType === 'E') {
                this.sendType = 'S';
                this.notice = this.phoneNotice.code;
            } else {
                this.sendType = 'E';
                this.notice = this.mailNotice.code;
            }
        },
        // 忘记签约密码
        handleClickForget() {
            window.localStorage && window.localStorage.setItem('ForgetPassReturnSignHref', '/' +  this.$router.options.base + this.$route.fullPath);
            this.$router.push(`/resetSignPassword?userAccount=${this.userAccount}`);
        },
        // 签约校验
        async handleClickConfirmSign() {
            const res = await this.$hybrid.offlineTip({ operate: this.$t('signValidation.signVerification') });
            if (!res) {
                return;
            }
            this.$parent.$emit('loading', 1);
            this.submitDiable = true;
            this.postConfirmSign({
                signPass: this.signPass,
                verifyCode: this.verifyCode,
                verifyKey: this.verifyKey,
                notice: this.notice,
                signPlatform: 'WEB',
                archiveCollectContractId: this.$route.query.archiveCollectContractId,
            })
                .then(() => {
                    this.$MessageToast.success(this.isApplySeal ? this.$t('signValidation.appliedSeal') : this.$t('signValidation.operationCompleted'))
                        .then(() => {
                            // 当前是档案+采集流程，签署成功跳转采集成功页面
                            if (sessionStorage.getItem('entCustomerInfo')) {
                                const entCustomerInfo = JSON.parse(sessionStorage.getItem('entCustomerInfo'));
                                return location.href = `${location.origin}/damp/pages/success/success?archiveId=${entCustomerInfo.archiveId}`;
                            }
                            /* 如果客户定义了跳转地址，则首先跳转 */
                            if (this.returnUrl) {
                                goReturnUrl(this.returnUrl);
                                return;
                            }
                            if (this.ssoSigning.signing_agree && this.ssoSigning.signing_agree.url) {
                                this.$router.push(`${this.ssoSigning.signing_agree.url}?status=6&contractId=${this.contractId}&type=sign`);
                            } else {
                                // 链接进来
                                if (this.channel === 'notice') {
                                    this.$router.push(`/sign/sign-tip?status=6&contractId=${this.contractId}&type=sign${this.isAllSignaturesInactive ? '&applySeal=true' : ''}`);
                                } else { // 登录进来
                                    this.$router.push('/doc-manage/list');
                                }
                            }
                        });
                })
                .catch(err => {
                    this.isErrorShow = true;
                    this.forgetPassShow = err.response.data.code === '010051';
                    this.errorMsg = err.response.data.message;
                })
                .finally(() => {
                    this.submitDiable = false;
                    this.$parent.$emit('loading', 0);
                });
        },
        /**
         * @param  {Object}   data 确认签署提交的密码，验证码等数据
         * @return {promise}
         * @desc   确认签署发送的请求
         */
        postConfirmSign(data) {
            const contractId = this.contractId || this.$route.query.contractId; // 部分场景取不到 contractId
            // 混合云合同签署时提交type=text的labels值
            if (this.isHybridCloudContract) {
                data = { signerFillLabels: this.signerFillLabels, ...data };
            }
            return this.$hybrid.makeRequest({
                url: `${signPath}/contracts/${contractId}/confirm`,
                hybridTarget: '/contracts/confirm',
                method: 'post',
                data: {
                    contractId, // 混3在 data 中增加 contractId
                    ...data,
                },
                contractId,
            });
        },
        /**
         * @description 跳转去刷脸
         */
        handleToFace() {
            this.$emit('toFace');
        },
        getSignPasswordPriorityConfig() {
            this.$http.post(`/contract-api/contracts/get-sender-config-batch`)
                .then((res) => {
                    const { senderContractSignConfiguration } = res.data;
                    this.isSignPasswordPriorityOpen = senderContractSignConfiguration.prioritySigningPassword;
                });
        },
        handleCloseRemind() {
            this.$http.post('/users/prompt/interrupt', {
                ruleName: 'SET_SIGN_PWD',
                interruptDays: 1,
            }).finally(() => {
                this.showSignPwdRemind = false;
            });
        },
        showSignPwdDialog() {
            this.$confirm(this.$t('signValidation.signPwdRemind'), {
                confirmButtonText: this.$t('signValidation.toSetSignPwd'),
                type: 'warning',
                customClass: 'set-sign-pwd-dialog',
                showCancelButton: false,
            }).then(() => {
                this.$emit('openSetSignPwdDialog');
            }).catch(() => {
                this.handleCloseRemind();
            });
        },
        getIfShowSignPwdRemind() {
            if (!this.signPwdExistence) {
                getIfTriggerSignPwd()
                    .then((res) => {
                        this.showSignPwdRemind = res.data?.value;
                        if (this.showSignPwdRemind && this.isOldBatchSign) {
                            this.showSignPwdDialog();
                        }
                    });
            }
        },
    },
    created() {
        getNoticeType()
            .then(res => {
                this.mailNotice = res.data.filter(item => item.type === 1)[0] || '';
                this.phoneNotice = res.data.filter(item => item.type === 2)[0] || '';
                this.notice = this.phoneNotice.code || this.mailNotice.code;
                this.sendType = this.phoneNotice.code ? 'S' : 'E';
            })
            .then(() => {
                this.$watch('sendType', function() {
                    if (this.$refs.btn.time === 0) {
                        this.send();
                    }
                });
            });
        getSignPwdExist()
            .then((res) => {
                this.signPwdExistence = res.data?.value;
                this.getIfShowSignPwdRemind();
            });
    },
    mounted() {
        this.getContractSenderConfig();
        this.getSignPasswordPriorityConfig();
    },
};
</script>
<style lang="scss">
.sign-pwd-remind {
    &__mask {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #000;
        opacity: 0.5;
        z-index: 9998;
    }
    &__img {
        width: 384px;
        position: absolute;
        z-index: 9999;
        bottom: 148px;
        right: 160px;
    }
    &__icon {
        position: absolute;
        width: 24px;
        z-index: 9999;
        bottom: 110px;
        right: 300px;
        cursor: pointer;
    }
}
.set-sign-pwd-dialog {
    .el-message-box__content {
        border-top: none;
    }
}
</style>
