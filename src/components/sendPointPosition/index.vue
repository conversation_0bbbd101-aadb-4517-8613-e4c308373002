<!-- 单份、批量发送指定位置页 -->
<template>
    <div class="point-position-page">
        <!-- 左侧边-签约方、字段展示区域 -->
        <PointPositionSite
            v-if="canAdd !== 'none' && !isDataBoxPreview"
            :canAdd="canAdd"
            @site-drag-start="onDragStart"
        >
        </PointPositionSite>
        <!-- 模板文档主展示区域 -->
        <PointPositionDoc
            :canAdd="canAdd"
            :canDrag="isCanDrag"
            :idxInBatch="idxInBatch"
            :canSendlabelEdit="false"
            :templateId="templateId"
            :iconDragStatus="dragStatus"
            :float="float"
            @save-meta="onMetaSave"
            @up-same-label="upSameLabel"
            @change-doc="changeDoc"
            :decorateCanChange="false"
        ></PointPositionDoc>
        <!-- 右侧边-合同缩略图区域 -->
        <PointPositionMiniDoc
            :documents="docList"
            @change-page="changePage"
            @change-doc="changeDoc"
            @change-contract="changeContractIndex"
            @next-sign-position="nextSignPosition"
        ></PointPositionMiniDoc>
        <!-- 右侧边-字段编辑区域，叠加在合同缩略图区域之上 -->
        <LabelEdit
            @save-meta="onMetaSave"
            :canChangeReceiver="false"
            :idxInBatch="idxInBatch"
            v-if="canAdd!=='none' && !isDataBoxPreview"
            @showContractChangeTipDialog="showContactChangeTipDialog = true"
        >
        </LabelEdit>
        <!-- 鼠标跟随icon -->
        <div id="flying-icon"
            class="flying-icon"
            v-show="float.show"
            :class="float.class"
        >
        </div>
        <!-- 保存前错误提醒dialog -->
        <LabelLackTip
            :tipObject="tipObject"
            :invalidRidingSealDocs="invalidRidingSealDocs"
            :show.sync="dialogTip"
            @confirm="getWorkFlows(true)"
            :title="$t('pointPosition.saveTemplateTip')"
            :confirmButtonText="$t('pointPosition.send')"
            @change-doc-by-id="changeDocById"
            :canChangeDoc="true"
        ></LabelLackTip>
        <!--年审风险提示-->
        <RiskTipsDialog ref="riskTips" @operation="operationType" :riskTipsData="tipObject.boxEntReviewResult" :nextType="nextType" :validData="validData"></RiskTipsDialog>
        <!-- 计费弹窗 -->
        <SignCharge v-if="dialogCharge"
            @cancel="dialogCharge = false"
            @confirm="onChargeConfirm"
            :flowInfos="flowInfos"
            :isIndividualApproval="isIndividualApproval"
        ></SignCharge>
        <!-- 发送前审批 -->
        <ApprovalDialog v-if="dialogApproval"
            :type="1"
            :title="$t('pointPosition.contractDispatchApply')"
            :allWorkFlows="allWorkFlows"
            @close="dialogApproval = false"
            @apply-approval="applyApproval"
        ></ApprovalDialog>
        <!-- 批量发送倒计时弹窗 -->
        <CounterDialog
            v-if="dialogCounter"
            :seconds="seconds"
            @close="handleCounterDialogClose"
        >
        </CounterDialog>
        <SetApprovalDialog
            v-if="setApprovalDialogVisible"
            :workFlowErrorMsg="workFlowErrorMsg"
            @close="setApprovalDialogVisible=false"
        >
        </SetApprovalDialog>
        <SendedEditGuide :visible.sync="sendedEditGuideVisible"
            :editor-name="sendedEditor"
        ></SendedEditGuide>
        <ContractChangeTipDialog
            :dialog-visible.sync="showContactChangeTipDialog"
            :contract-num="contractNum"
        ></ContractChangeTipDialog>
        <ContractExtractSidebar :receiverId="receivers[0].receiverId"></ContractExtractSidebar>
    </div>
</template>
<script>
import { scrollToYSmooth } from 'pub-utils/dom.js';
import PointPositionSite from 'components/pointPositionSite';
import PointPositionDoc from 'components/pointPositionDoc';
import PointPositionMiniDoc from 'components/pointPositionMiniDoc';
import LabelEdit from 'components/labelEdit';
import LabelLackTip from 'components/labelLackTip';
import ApprovalDialog from 'components/approvalDialog';
import CounterDialog from 'components/counterDialog';
import SignCharge from 'components/signCharge';
import SendedEditGuide from 'components/sendedEditGuide';
import ContractExtractSidebar from 'components/contractExtractSidebar';
import ContractChangeTipDialog from './ContractChangeDialog';
import { getDocumentLabels, getWorkFlows, validDraft, submitWorkflow, send, timingSend, getJumpInfo, getContractId, fdaOpened } from 'src/api/template/pointPosition.js';
import { initWatermark, initRidingSeal } from 'utils/decorate.js';
import { pointPositionMixin } from 'src/mixins/pointPosition.js';
import { getAllContractDataWithFields, getBatchSendContractDataWithFields } from 'src/api/send/localFields';
import { mapState, mapMutations, mapGetters, mapActions } from 'vuex';

import RiskTipsDialog from 'src/components/riskTipsDialog';
import SetApprovalDialog from 'src/components/setApprovalDialog';
import { formatDocInfo, mergeLabelValueOfLocalField } from 'utils/docDataResolve';
import { pointPositionCacheDraft } from '@/api/send';
export default {
    components: {
        PointPositionSite,
        PointPositionDoc,
        LabelEdit,
        LabelLackTip,
        PointPositionMiniDoc,
        ApprovalDialog,
        SignCharge,
        RiskTipsDialog,
        CounterDialog,
        SetApprovalDialog,
        SendedEditGuide,
        ContractChangeTipDialog,
        ContractExtractSidebar,
    },
    mixins: [pointPositionMixin],
    data() {
        return {
            tipObject: {
                receivers: [],
                documents: [],
                errorMsg: [],
                boxEntReviewResult: {},
            },
            showContactChangeTipDialog: false,
            sendedEditGuideVisible: false,
            sendedEditor: '',
            dialogTip: false,
            dialogCharge: false,
            // 年审风险提示
            riskTips: false,
            // 审批流
            dialogApproval: false,
            commitFlow: [],
            defInfoId: '',
            allWorkFlows: [],
            redirectUrl: '', // 发送后跳转路由
            nextType: 'send', // send, approval继续按钮为发送/审批
            timer: null,
            validData: {},
            idxInBatch: 0, // 当前预览的是第几份合同
            seconds: 0,
            dialogCounter: false,
            sendCountDownTimer: null,
            isDataBoxPreview: this.$route.query.isDataBoxPreview === 'true', // 是否是档案+合同预览
            flowInfos: [], // 审批流信息
            isIndividualApproval: false, // 是否是按发件方配置审批流
            setApprovalDialogVisible: false,
            workFlowErrorMsg: '',
            invalidRidingSealDocs: [],
            // 统计当前无效的骑缝章数据
            notFindKeyWordList: [], // 没有匹配到关键字定位到列表
            isDynamic: this.$route.query.isDynamic === 'true',
            enterTime: 0,
        };
    },
    computed: {
        ...mapGetters(['checkFeat']),
        ...mapState('template', {
            templateId: state => state.templateId,
            templatePermissions: state => state.templatePermissions,
            canDrag: state => state.templatePermissions.dragSignLabel,
            isBatchSend: state => state.isBatchSend,
            excelRowCount: state => state.excelRowCount,
            ridingSealList: state => state.ridingSealList,
            contractNum: state => state.contractNum,
            distinctEntSignerCount: state => state.distinctEntSignerCount,
        }),
        ...mapState({
            localFieldSupportType: state => state.localFieldSupportType,
        }),
        // 是否开启字段本地化
        isLocalFieldScene() {
            return this.$hybrid.isGammaLocalField(this.localFieldSupportType);
        },
        canAdd() { // 'all','some','none  新增和删除标签权限
            return this.templatePermissions.modifySignLabel ? 'some' : 'none';
        },
        isCanDrag() {
            return this.canDrag && !this.isDataBoxPreview;
        },
    },
    methods: {
        ...mapMutations('template', ['setDraftId', 'setFdaConfig']),
        ...mapActions('template', ['findCrossPlatform']),
        getMarksType() {
            const marksType = [];
            const mapObject =  {
                'SEAL': '盖章',
                'SIGNATURE': '签字',
                'CONFIRMATION_REQUEST_SEAL': '',
                'DATE': '签署日期',
                'TEXT': '文本',
                'TEXT_NUMERIC': '数字',
                'NUMERIC_VALUE': '数值',
                'CONFIRMATION_REQUEST_REMARK': '不符合章备注',
                'SINGLE_BOX': '单选框',
                'MULTIPLE_BOX': '复选框',
                'PICTURE': '图片',
                'BIZ_DATE': '日期',
                'DATE_TIME': '时刻',
                'COMBO_BOX': '下拉框',
                'QR_CODE': '二维码',
            };
            this.docList.map(doc => {
                doc.labels.map(label => {
                    marksType.push(mapObject[label.labelType]);
                });
            });
            return Array.from(new Set(marksType));
        },
        getNotFindKeyWordList() {
            const documentNames = [];
            for (let i = 0; i < this.docList.length; i++) {
                const labels = this.docList[i].labels.filter(label => label.remind);
                if (labels.length > 0) {
                    this.notFindKeyWordList.push({
                        index: i,
                        labels,
                    });
                    documentNames.push(this.docList[i].documentName);
                }
            }
            if (this.notFindKeyWordList.length) {
                let message = `<p>${this.$t('components.sendPointPosition.index-1c6dcb-1')}</p>`;
                if (this.notFindKeyWordList.length > 1) {
                    message =  message + `<p>${this.$t('components.sendPointPosition.index-1c6dcb-4')}${documentNames.join('、')}</p>`;
                }
                this.$sensors.track({
                    eventName: 'Ent_ContractSendDetailWindow_PopUp',
                    eventProperty: {
                        page_name: '指定签署位置',
                        is_batch_send: this.isBatchSend,
                        ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                        window_name: '关键字匹配提示',
                    },
                });
                this.$confirm(message, this.$t('components.sendPointPosition.index-1c6dcb-2'), {
                    confirmButtonText: this.$t('components.sendPointPosition.index-1c6dcb-3'),
                    distinguishCancelAndClose: true,
                    dangerouslyUseHTMLString: true,
                    type: 'warning',
                }).then(() => {
                    this.$sensors.track({
                        eventName: 'Ent_ContractSendDetailWindow_BtnClick',
                        eventProperty: {
                            page_name: '指定签署位置',
                            is_batch_send: this.isBatchSend,
                            ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                            window_name: '关键字匹配提示',
                            icon_name: '确定',
                        },
                    });
                    this.setCurrentDocIndex(this.notFindKeyWordList[0].index);
                    setTimeout(() => {
                        const $scrollEl = document.querySelector('.point-position-doc-list');
                        const docPages = document.querySelectorAll('.point-position-doc-pages')[this.currentDocIndex].children;
                        const initY = document.querySelector('.point-position-doc-wrapper').offsetTop;
                        const y = (docPages[0].clientHeight - 150) * this.zoom + initY;
                        scrollToYSmooth($scrollEl, y, 400, 'ease-out');
                    }, 400);
                }).catch(() => {
                    this.$sensors.track({
                        eventName: 'Ent_ContractSendDetailWindow_BtnClick',
                        eventProperty: {
                            page_name: '指定签署位置',
                            is_batch_send: this.isBatchSend,
                            ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                            window_name: '关键字匹配提示',
                            icon_name: '取消',
                        },
                    });
                });
            }
        },
        // 当前查看合同序号变化，标记批量发送时，当前预览合同的索引号
        changeContractIndex(idx) {
            this.idxInBatch = idx;
            this.changeDoc(0);
            this.getDocumentInfo();
        },
        // 点击按钮设置类型，以及跳转链接
        setNextInfo(redirectUrl, type) {
            this.redirectUrl = redirectUrl;
            // 继续按钮类型为发送前审批/发送
            this.nextType = type;
        },
        applyApproval(commitFlow) {
            this.isIndividualApproval = commitFlow.length > 1; // 多个发件方选择灵活配置时才不能走定时发送
            this.flowInfos = commitFlow[0].workflows;
            submitWorkflow(commitFlow).then(() => {
                this.dialogApproval = false;
                this.dialogCharge = true;
            });
        },
        // 指定签署位置保存并退出
        handleSavePartly() {
            const loading = this.$loading();
            pointPositionCacheDraft(this.$route.params.draftId).then(() => {
                this.$sensors.track({
                    eventName: 'Ent_ContractSendDetail_Result',
                    eventProperty: {
                        page_name: '指定签署位置',
                        is_batch_send: this.isBatchSend,
                        ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                        first_category: '顶部导航栏',
                        module_list: this.getMarksType(),
                        is_success: true,
                        icon_name: '暂存并退出',
                    },
                });
                this.immediatelyRedirectAfterSend();
            }).catch((err) => {
                this.$sensors.track({
                    eventName: 'Ent_ContractSendDetail_Result',
                    eventProperty: {
                        page_name: '指定签署位置',
                        is_batch_send: this.isBatchSend,
                        ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                        first_category: '顶部导航栏',
                        module_list: this.getMarksType(),
                        is_success: false,
                        request_url: err.config.url,
                        fail_reason: err.response?.data?.message || err.message,
                        fail_error_code: err.response?.data?.code,
                        fail_http_code: err.response?.status,
                        icon_name: '暂存并退出',
                    },
                });
            }).finally(() => {
                loading.close();
            });
        },
        // 保存模板前校验
        async toNext({ redirectUrl, nextType }) {
            if (this.$route.query.sendType === 'ssoConfirm' && nextType === 'send') {
                const draftIdList = sessionStorage.getItem('sso-send-confirm-draftIdList');
                return this.$router.replace(`/send/sso-confirm?draftIdList=${encodeURIComponent(draftIdList)}`);
            }
            this.setNextInfo(redirectUrl, nextType);
            if (nextType === 'savePartly') {
                this.handleSavePartly();
                return;
            }
            try {
                const loading = this.$loading();
                // 校验有无无效骑缝章，统计数据
                await this.calcInvalidRidingSealData();
                // SAAS-32939: 年审条目计数过滤重复、个人数量
                const { data } = await validDraft(this.distinctEntSignerCount > 500);
                loading.close();
                this.validData = data;
                if (data.editorRoleName) {
                    this.sendedEditor = data.editorRoleName;
                    this.sendedEditGuideVisible = true;
                    return;
                }
                if (data.boxEntReviewResult.boxEntReviewDTOList && data.boxEntReviewResult.boxEntReviewDTOList.length) { // 当有年审信息，年审提示，否则走正常流程
                    this.tipObject.boxEntReviewResult = data.boxEntReviewResult;
                    this.setRiskTipsVisible();
                } else {
                    const hasSignTypeError = data.signTypeError?.needSeal.length ||
                        data.signTypeError?.needSignature.length || data.signTypeError?.needSealSignature.length;
                    if (data.receivers.length || data.documents.length || data.errorMsg.length ||
                        this.invalidRidingSealDocs.length || hasSignTypeError || data.allEmptyReceivers.length) { // 增加无效骑缝章的提示
                        this.dialogTip = true;
                        this.tipObject = data;
                    } else {
                        // 发送模版走审批流，发送前审批直接走计费跳过审批
                        nextType === 'send' ? this.getWorkFlows() : this.dialogCharge = true;
                    }
                }
            } catch (error) {
                console.log(error);
            }
        },
        // 设置风险提示框的显示
        setRiskTipsVisible() {
            this.$refs.riskTips.showRiskTips();
        },
        // 是否选择了全部发送
        operationType(type, nextType, data) {
            if (type === 'confirm') {
                const hasSignTypeError = data.signTypeError.needSeal.length ||  data.signTypeError.needSignature.length ||  data.signTypeError.needSealSignature.length;
                if (this.invalidRidingSealDocs.length > 0 || data.receivers.length || data.documents.length ||
                    data.errorMsg.length || hasSignTypeError || data.allEmptyReceivers.length) {
                    this.dialogTip = true;
                    this.tipObject = data;
                } else {
                    // 发送模版走审批流，发送前审批直接走计费跳过审批
                    nextType === 'send' ? this.getWorkFlows() : this.dialogCharge = true;
                }
            }
        },
        // 审批流
        getWorkFlows(isFromLackTip) {
            getWorkFlows()
                .then(res => {
                    setTimeout(() => {
                        const flowsData = res.data;
                        this.allWorkFlows = res.data;
                        if (isFromLackTip) {
                            this.dialogTip = false;
                        }
                        // 触发审批流程
                        if (flowsData.length) {
                            this.dialogApproval = true;
                        } else { // 不触发审批流程，弹出计费弹窗
                            this.dialogCharge = true;
                        }
                    }, 200);
                })
                .catch(() => {});
        },

        // 自己签弹窗
        mySelfSignBox(resultId, target = 'detail') {
            this.$confirm(this.$t('pointPosition.nowSignText'), this.$t('pointPosition.nowSignTip'), {
                confirmButtonText: this.$t('pointPosition.nowSign'),
                cancelButtonText: this.$t('pointPosition.laterSign'),
                customClass: 'messageBox',
                type: 'warning',
            }).then(() => {
                const loading = this.$loading();
                // 轮训接口拿到contractId
                this.timer = setInterval(() => {
                    getContractId(resultId)
                        .then((res) => {
                            const { contractId, resultType, resultMessage } = res.data;
                            if (resultType === 'SUCCESS' && contractId) {
                                loading.close();
                                const routerToUrl = target === 'sign' ? `/sign/signing?contractId=${contractId}` : `/doc-manage/detail/${contractId}`;
                                this.$router.push(routerToUrl);
                                clearInterval(this.timer);
                            } else if (resultType === 'FAILURE') {
                                loading.close();
                                return Promise.reject(resultMessage);
                            }
                        })
                        .catch((err) => {
                            loading.close();
                            clearInterval(this.timer);
                            const msg = err;
                            this.$alert(msg, {
                                confirmButtonText: this.$t('pointPosition.confirm'),
                                customClass: 'field-error-alert',
                                confirmButtonLoading: false,
                            }).finally(() => {
                                this.doRedirect();
                            });
                        });
                }, 1000);
            }).catch(() => {
                // 选择稍后签署，跳转回模版列表页
                this.doRedirect();
            });
        },

        // 筛选无效骑缝章数据，在弹窗时进行提示
        calcInvalidRidingSealData() {
            this.invalidRidingSealDocs = []; // 先重置
            const allDocIds = this.docList.map(d => d.documentId);

            const docRidingSealVO = {};
            this.ridingSealList.forEach(rs => {
                // 无效的骑缝章，指定文档时指定的为空
                if (!rs.selectAll && !rs.selectDocumentIds?.length) {
                    return;
                }
                if (rs.selectAll) { // 全部文档需要判断
                    allDocIds.forEach(docId => {
                        docRidingSealVO[docId] = (docRidingSealVO[docId] || []).concat(rs.receiverId);
                    });
                    return;
                }
                rs.selectDocumentIds.forEach(documentId => {
                    docRidingSealVO[documentId] = (docRidingSealVO[documentId] || []).concat(rs.receiverId);
                });
            });
            this.docList.filter(({ documentId }) => Object.keys(docRidingSealVO).includes(documentId))
                .forEach(({ documentId,
                            labels,
                            documentName,
                            pageSize,
                            attachments,
                }) => {
                    if (pageSize === 1 && !attachments.length) {
                        this.invalidRidingSealDocs.push({
                            pageLimit: true,
                            documentName,
                            documentId,
                        });
                        return;
                    }
                    const sealLabelReceiverIds = labels.filter(label => label.receiverId &&
                        ['CONFIRMATION_REQUEST_SEAL', 'SEAL'].includes(label.labelType))
                        .map(label => label.receiverId);
                    const emptyReceivers = docRidingSealVO[documentId].filter(receiverId =>
                        !sealLabelReceiverIds.includes(receiverId));
                    if (emptyReceivers.length) {
                        this.invalidRidingSealDocs.push({
                            pageLimit: false,
                            documentName,
                            documentId,
                            receiverRoleNames: emptyReceivers.map(rId => {
                                const roleName = this.receivers.find(r => r.receiverId === rId).roleName;
                                return `"${roleName}"`;
                            }).join('、'),
                        });
                    }
                });
        },
        // 计费弹窗——点击确认，执行模板发送逻辑
        async onChargeConfirm(params = {}) {
            const loading = this.$loading();
            // 混合云网络校验
            const passRes = await this.$hybrid.offlineTip();
            if (!passRes) {
                return;
            }
            // sendTime、receivedTime都为空时走老的send接口，任何一个有值都走新的timingSend接口
            const isTimingSend = params.sendTime || params.receivedTime;
            const sendFun =  isTimingSend ? timingSend : send;
            params = {
                ...params,
                bizNo: this.$route.query.bizNo || null,
            };
            // 发送并签署
            const isSignAfterSend = params && params.signAfterSendRoleNames && params.signAfterSendRoleNames.length;
            const send_type = isTimingSend ? '定时发送合同' : (isSignAfterSend ? '发送合同并签署合同' : '发送合同');
            return sendFun({ type: this.nextType, ...params })
                .then((sendResult) => {
                    this.$http.addBizPoint('SEND_CONTRACT', this.$route.params.draftId);
                    this.sendContractsDone({
                        isTimingSend,
                        result: sendResult,
                        params,
                    });
                    this.$sensors.track({
                        eventName: 'Ent_ContractSend_Result',
                        eventProperty: {
                            page_name: '指定签署位置',
                            first_category: '顶部导航栏',
                            draft_id: this.$route.params.draftId,
                            result_id: sendResult.data.resultId,
                            ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                            send_type: send_type,
                            is_success: true,
                            icon_name: '发送',
                        },
                    });
                    this.receivers.forEach(item => {
                        this.$sensors.track({
                            eventName: 'Ent_ContractSendSuccessList_Result',
                            eventProperty: {
                                page_name: '指定签署位置',
                                first_category: '顶部导航栏',
                                draft_id: this.$route.params.draftId,
                                result_id: sendResult.data.resultId,
                                ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                                company_id: item.enterpriseId,
                                company_name: item.userType === 'ENTERPRISE' ? item.enterpriseName : '个人签署人',
                                signatory_role: item.roleName,
                                send_type: send_type,
                                icon_name: '发送',
                            },
                        });
                    });
                })
                .catch((err) => {
                    this.$sensors.track({
                        eventName: 'Ent_ContractSend_Result',
                        eventProperty: {
                            page_name: '指定签署位置',
                            first_category: '顶部导航栏',
                            draft_id: this.$route.params.draftId,
                            ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                            send_type: send_type,
                            is_success: false,
                            request_url: err.config.url,
                            fail_reason: err.response?.data?.message || err.message,
                            fail_error_code: err.response?.data?.code,
                            fail_http_code: err.response?.status,
                            icon_name: '发送',
                        },
                    });
                    if (err.response.data.code === '190026') {
                        this.setApprovalDialogVisible = true;
                        this.workFlowErrorMsg = err.response.data.message;
                    } else {
                        this.$MessageToast.error(err.response.data.message);
                    }
                })
                .finally(() => {
                    loading.close();
                    this.dialogCharge = false;
                });
        },
        // 模板合同发送完的处理逻辑
        sendContractsDone({ isTimingSend, result, params  }) {
            const resultId =  result.data.resultId;

            // SAAS-20968 是不是发送并签署合同，发送完会自动签署
            const isSignAfterSend = params && params.signAfterSendRoleNames && params.signAfterSendRoleNames.length;
            // 乐高城有没有配置发送成功后的跳转url
            const hasRedirectUrl = this.redirectUrl !== '';

            // 发送成功第一时间要执行跳转的在这里进行拦截
            if (isSignAfterSend || hasRedirectUrl)  {
                // 发送并签署，直接跳转
                return this.immediatelyRedirectAfterSend();
            }

            // 批量发送，轮询展示批量发送的时间, 设置过定时发送不展示计时弹窗
            if (this.isBatchSend && !isTimingSend) {
                this.getBatchSendResult(resultId);
            } else {
                getJumpInfo()
                    .then((res) => {
                        const { shouldJump, target } = res.data;
                        if (shouldJump) {
                            this.mySelfSignBox(resultId, target);
                        } else {
                            this.doRedirect();
                        }
                    });
            }
        },
        // 执行跳转的逻辑，有redirectUrl则跳redirectUrl
        immediatelyRedirectAfterSend() {
            this.redirectUrl ? this.goCustomUrl() : this.doRedirect();
        },
        // 跳转redirectUrl
        goCustomUrl() {
            this.$router.push(this.redirectUrl);
        },
        // 默认的路径跳转，默认跳转到模板管理列表
        doRedirect() {
            const defaultPath = '/template/list';
            this.$router.push(defaultPath);
        },
        getBatchSendResult(resultId) {
            // 计算发送并签署的秒和分
            this.seconds = (this.excelRowCount + 1) * 2;
            // 大于1分钟时，每分钟查询一次；小于1分钟时，每3s查询一次
            let interval =  this.seconds > 60 ? 6 : 3;

            this.dialogCounter = true;
            const intervalFn = () => {
                this.sendCountDownTimer = setTimeout(() => {
                    getResult();
                    this.seconds = this.seconds - interval;
                    interval =  this.seconds > 60 ? 60 : 3;
                    intervalFn();
                }, interval * 1000);
            };
            intervalFn();

            const getResult = () => {
                this.$http.get(`/template-api/v2/draft/all-send-complete?resultId=${resultId}`, { noToast: 1 })
                    .then(res => {
                        if (res.data === true) {
                            this.dialogCounter = false;
                            this.sendCountDownTimer && clearTimeout(this.sendCountDownTimer);
                            this.$router.push('/doc-manage/batch-log');
                        }
                    });
            };
        },
        getDocumentInfosWithLocalFields() {
            return (this.isBatchSend ? getBatchSendContractDataWithFields(this.templateId, this.idxInBatch)
                : getAllContractDataWithFields(this.templateId, this.idxInBatch))
                .then(({ data }) => {
                    const { localFields, result: { receivers = [], decorate = {}, documents = [] } } = data;
                    // 数据的receiver包含补全人，补全人不用展示在签署方列表里
                    this.setReceivers(receivers.filter(receiver => receiver.receiverType !== 'EDITOR'));
                    this.setEditors(receivers.filter(receiver => receiver.receiverType === 'EDITOR'));
                    this.setWatermarkList(initWatermark(decorate.watermarks, receivers));
                    this.setRidingSealList(initRidingSeal(decorate.ridingSeals, receivers));
                    mergeLabelValueOfLocalField(documents, localFields);
                    this.setDocList(formatDocInfo(documents));
                    this.getNotFindKeyWordList();
                });
        },
        getDocumentInfo() {
            const loadingInstance = this.$loading();

            if (this.isLocalFieldScene) {
                return this.getDocumentInfosWithLocalFields().finally(() => {
                    loadingInstance.close();
                });
            } else {
                getDocumentLabels(this.idxInBatch)
                    .then(({ data }) => {
                        // 数据的receiver包含补全人，补全人不用展示在签署方列表里
                        this.setReceivers(data.receivers.filter(receiver => receiver.receiverType !== 'EDITOR'));
                        this.setEditors(data.receivers.filter(receiver => receiver.receiverType === 'EDITOR'));
                        this.setWatermarkList(initWatermark(data.decorate.watermarks, data.receivers));
                        this.setRidingSealList(initRidingSeal(data.decorate.ridingSeals, data.receivers));
                        this.setDocList(formatDocInfo(data.documents));
                        this.getNotFindKeyWordList();
                    }).finally(() => {
                        loadingInstance.close();
                    });
            }
        },
        initData() {
            this.setTemplateStatus('use');
            this.setDraftId(this.$route.params.draftId);
            this.findCrossPlatform(this.$route.params.draftId); // 判断是否跨平台合同
            fdaOpened().then(({ data: fdaData }) => {
                this.setFdaConfig(fdaData);
            });
            this.getDocumentInfo();
        },
        handleCounterDialogClose() {
            this.dialogCounter = false;
            this.$router.push('/doc-manage/batch-log');
        },
    },
    beforeDestroy() {
        this.$sensors.track({
            eventName:
                'Ent_ContractSendDetail_PageLeave',
            eventProperty: {
                page_name: '指定签署位置',
                ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
        this.sendCountDownTimer && clearTimeout(this.sendCountDownTimer);
    },
    mounted() {
        this.enterTime = new Date().getTime();
        this.initData();
        this.$sensors.track({
            eventName: 'Ent_ContractSendDetail_PageView',
            eventProperty: {
                page_name: '指定签署位置',
                is_batch_send: this.isBatchSend,
                ...(!this.isBatchSend && { template_type: this.isDynamic ? '动态模板' : '静态模板' }),
            },
        });
    },
};
</script>
<style lang="scss">
.point-position-page{
    height: 100%;
    position: relative;
    overflow-x: hidden;
    .flying-icon {
        z-index: 9999;
        pointer-events: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 28px;
        height: 28px;
        line-height: 27px;
        text-align: center;
        font-size: 18px;
        background-color: $--color-white;
        border-radius: 4px;
        box-shadow:1px 1px 13px $--border-color-base, -1px 1px 13px $--border-color-base;
    }
}
</style>
