<template>
    <div class="point-position-doc" :class="{'no-site': canAdd === 'none'}">
        <!-- 合同内容 -->
        <div class="point-position-doc-con documents-content" ref="docContent">
            <template v-if="docList.length">
                <p class="point-position-doc-title">{{ currentDoc.documentName }}</p>
                <div class="point-position-doc-list" @scroll="onDocScroll">
                    <!-- 水印头部标签 -->
                    <div class="watermarks-container" :style="{'width': `${docWidth - 100}px`}">
                        <Watermark
                            @delete="handleDeleteLabel(watermark.decorateId, watermarkList, index, 'watermarkList')"
                            v-for="(watermark, index) in watermarkList"
                            :key="watermark.receiverId"
                            :watermark="watermark"
                            :color="computeRgbColor(watermark, true)"
                            :readonly="true"
                            :decorateCanChange="decorateCanChange"
                        >
                        </Watermark>
                    </div>
                    <div class="point-position-doc-wrapper" :style="{'width': `${docWidth - 100}px`}">
                        <!-- 文档循环 -->
                        <template v-for="(doc, docIndex) in docList">
                            <div :style="{transform:`scale(${zoom})`,'transform-origin': 'left top', width:
                                     `${docMaxWidth}px`}"
                                v-if="currentDocIndex == docIndex"
                                :key="docIndex"
                                class="point-position-doc-pages"
                            >
                                <!-- 所有页面 -->
                                <div v-for="(page, pageIndex) in doc.documentPages"
                                    class="point-position-doc-page"
                                    :key="pageIndex"
                                    :style="{'padding-right': currentDocRidingSeals.length ? '116px': ''}"
                                >
                                    <div class="point-position-doc-page-con"
                                        :style="{width: `${page.width}px`,height: `${page.height}px`}"
                                        :page-index="pageIndex"
                                    >
                                        <img
                                            class="image"
                                            :width="page.width"
                                            :height="page.height"
                                            v-lazy.previewUrl="{ src: `${getPageUrl(page, pageIndex)}`,
                                                                 split: 2,
                                                                 total: doc.documentPages.length,
                                                                 index:pageIndex }"
                                            :data-previewUrl="`${getPageUrl(page, pageIndex)}`"
                                            alt=""
                                        />
                                        <div class="water-mark-back" :style="{background: waterMarkPng ? `url(${waterMarkPng}) repeat` : 'none'}"></div>
                                        <div v-if="boxSelectObj.isBoxSelecting && pageIndex === boxSelectObj.pageIndex"
                                            class="box-select-bg"
                                            :style="{
                                                width: `${boxSelectBgInfo.width}px`,
                                                height: `${boxSelectBgInfo.height}px`,
                                                top: `${boxSelectBgInfo.top * 100}%`,
                                                left: `${boxSelectBgInfo.left * 100}%`,
                                            }"
                                        ></div>
                                        <Labels
                                            v-for="label in labelList(page.pageNumber,page.attachmentId,pageIndex,doc)"
                                            :key="label.labelId"
                                            :pageHeight="page.height"
                                            :pageWidth="page.width"
                                            :zoom="zoom"
                                            :color="computeRgbColor(label)"
                                            :label="label"
                                            :canDrag="canDrag"
                                            :canAdd="canAdd"
                                            :canSendlabelEdit="canSendlabelEdit"
                                            :isBoxSelected="isLabelInSelectBox(label.labelId)"
                                            :canCopySeal="canCopySeal(label)"
                                            @delete="handleDeleteLabel(label.labelId, currentDoc.labels, $event, 'label')"
                                            @deleteAllDecorate="deleteAllDecorate(label.labelId)"
                                            @label-start="onLabelStart($event, label)"
                                            @label-move="onLabelMove($event, label, page)"
                                            @label-end="onLabelEnd($event, label)"
                                            @label-button-change="onLabelButtonChange($event, label, 'labelExtends' ,'items')"
                                            @showQRCodeList="contractQrCodeDialogVisible = true"
                                            @changeSpecialSeal="onChangeSpecialSeal($event, label)"
                                        >
                                        </Labels>
                                        <!-- 选中框 -->
                                        <MarqueeComp
                                            v-if="hasMarquee(pageIndex)"
                                            class="test-1"
                                            :zoom="zoom"
                                            :pageHeight="page.height"
                                            :pageWidth="page.width"
                                            @save-label="saveLabel"
                                        ></MarqueeComp>
                                        <!-- 骑缝章 -->
                                        <div class="riding-seals" v-if="currentDocRidingSeals.length">
                                            <div class="riding-seals-bg"></div>
                                            <RidingSeal
                                                v-for="(ridingSeal, sealIndex) in currentDocRidingSeals"
                                                :key="ridingSeal.receiverId"
                                                :ridingseal="ridingSeal"
                                                :pageHeight="page.height"
                                                :color="computeRgbColor(ridingSeal, true)"
                                                :canDrag="canDrag"
                                                :canAdd="canAdd"
                                                :sealIndex="sealIndex"
                                                :zoom="zoom"
                                                @delete="handleDeleteRidingSeal(ridingSeal.orignIndex, $event)"
                                            >
                                            </RidingSeal>
                                        </div>
                                        <!-- AI 智能坐标 -->
                                        <div
                                            v-for="trigger in nowHolderTriggerList(pageIndex)"
                                            class="trigger-con"
                                            :style="{
                                                'pointer-events': iconDragStatus == 'started' ? 'none' : 'auto',
                                                'display': activePlaceHolder.includes(trigger.triggerId) ? 'block' : 'none',
                                                top: `${(1-trigger.y-curTriggerHeight)*100}%`,
                                                left: `${trigger.x*100}%`,
                                                width: `${trigger.width*page.width}px`,
                                                height: `${curTriggerHeight*page.height}px`,
                                                background: intoPlaceHolder === trigger.triggerId ? '#00ffff' : 'transparent',
                                            }"
                                            :key="trigger.triggerId"
                                        >
                                        </div>
                                    </div>
                                    <!-- 页脚 -->
                                    <p class="point-position-doc-page-footer" :key="pageIndex+'size'" :style="{width: `${page.width}px`}">
                                        <span class="footer-text">{{ page.documentName }}</span>
                                        <span class="fr">{{ $t('pointPositionDoc.pageTip', { pageNum: pageIndex + 1, pageSize: doc.documentPages.length}) }}</span>
                                        <CommonTip class="item"
                                            effect="dark"
                                            placement="top"
                                            :content="$tc('pointPositionDoc.viewHighDpiImg', page.showHighDpiImg ? 0
                                                : 1)"
                                        >
                                            <i slot="reference"
                                                :class="page.showHighDpiImg ? 'switch-icon el-icon-zoom-out' :
                                                    'switch-icon el-icon-zoom-in'"
                                                @click="switchToHighDpi(page)"
                                            ></i>
                                        </CommonTip>
                                    </p>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <!-- 进入下一份文档 -->
                <div class="next-doc-btn" v-if="currentDocIndex < docList.length-1" @click="changeDoc">{{ $t('pointPositionDoc.nextDoc') }}</div>
            </template>
        </div>

        <!-- 合同查验码弹窗 -->
        <ContractQrCodeDialog
            :visible.sync="contractQrCodeDialogVisible"
            @showQRCodePreviewDialog="handleShowQRCodePreviewDialog"
            ref="contractQrCodeDialog"
        ></ContractQrCodeDialog>

        <!-- 合同查验码预览弹窗 -->
        <QRBlockPreviewDialog
            v-if="contractQRBlockPreviewDialog.visible"
            class="QRCode-preview-dialog"
            :isRealDataMode="false"
            :block="contractQRBlockPreviewDialog.codeObject"
            :isPreviewVisible="contractQRBlockPreviewDialog.visible"
            @close-block-preview="contractQRBlockPreviewDialog.visible = false"
            ref="contractQRBlockPreviewDialog"
        ></QRBlockPreviewDialog>
        <!-- 框选编辑区 -->
        <BoxSelectEdit :boxSelectLabels="boxSelectLabels" @confirmChange="onBatchChange"></BoxSelectEdit>
        <BoxSelectTip v-if="showBoxSelectTipBtn"></BoxSelectTip>
        <LabelNameConflictDialog
            :visible.sync="labelNameConflictDialog.visible"
            :targetLabel="labelNameConflictDialog.targetLabel"
            :allLabels="allDocInputLabels"
            @confirm="handleConfirmLabelNameConflict"
            @close="handleCloseLabelNameConflict"
        />
    </div>
</template>
<script>
import { mapState, mapMutations, mapActions, mapGetters } from 'vuex';
import { FIELDPAGESPACE } from 'const/const.js';
import { throttle, debounce } from 'pub-utils/index.js';
import { FLOAT_TYPES, TEMPERORY_TYPES } from 'utils/labelStyle.js';

import { labelCoordinateTransform } from 'utils/labelCoordinates.js';
import { COLOR_TYPES, INPUT_TYPES, labelInfo, textLabelInfo, FDA_WIDTH_RATE } from 'utils/labelStyle.js';
import { rgbColorInfo } from 'const/const.js';
import { createWaterMark, RIDESEAL_SHADOW_WIDTH } from 'utils/decorate.js';

import { deleteLabel, deleteRideSealByDocs, updateLabel, deleteLabelAllDecorate } from 'src/api/template/pointPosition.js';
import { triggerMixin } from 'src/mixins/trigger.js';

import QRBlockPreviewDialog from 'pub-businessComponents/qrPreview/index.vue';
import Labels from 'components/labels';
import Watermark from 'components/watermarkEdit';
import Marquee from 'components/marquee';
import RidingSeal from 'components/ridingSeal';
import ContractQrCodeDialog from 'components/contractQrCodeDialog';
import BoxSelectEdit from 'components/pointPositionDoc/BoxSelectEdit';
import BoxSelectTip from 'components/pointPositionDoc/BoxSelectTip';
import LabelNameConflictDialog from 'components/labelNameConflictDialog';

export default {
    components: {
        Labels,
        Watermark,
        RidingSeal,
        'MarqueeComp': Marquee,
        ContractQrCodeDialog,
        QRBlockPreviewDialog,
        BoxSelectEdit,
        BoxSelectTip,
        LabelNameConflictDialog,
    },
    mixins: [triggerMixin],
    props: {
        hasAttachment: {
            type: Boolean,
            default: false,
        },
        iconDragStatus: {
            type: String,
        },
        float: {
            type: Object,
        },
        holderTriggerList: {
            type: Array,
            default: () => {
                return [];
            },
        },
        canAdd: {
            type: String,
        },
        canDrag: {
            type: Boolean,
        },
        decorateCanChange: {
            type: Boolean,
            default: true,
        },
        canSendlabelEdit: { // 发件方标签可操作
            type: Boolean,
            default: true,
        },
        idxInBatch: Number,
    },
    data() {
        return {
            initTime: new Date().getTime(),
            pageSpace: FIELDPAGESPACE,
            docWidth: 0,
            tempLabel: null,
            rgbColorInfo: rgbColorInfo.slice(1),
            move: { // 正在移动的标签
            },
            creation: { // 左侧拖拽出来的标签
                status: 'pedding',
            },
            activePlaceHolder: [],
            intoPlaceHolder: null,
            // AI 区域的高度根据拖出来业务字段的高度展示
            curTriggerHeight: 0,
            // 触发 AI 功能的标签类型，因为目前没有一个通用参数判断是业务字段，所以业务字段增加类型时这里也需要对应增加
            AIAttachFields: ['TEXT', 'BIZ_DATE', 'TEXT_NUMERIC'],
            contractQrCodeDialogVisible: false,
            contractQRBlockPreviewDialog: {
                visible: false,
                codeObject: {},
            },
            boxSelectObj: {
                startPosX: null, // 框选基于文档左上角的起始位置信息
                startPosY: null,
                startClientX: null, // 基于页面的起始坐标，用于计算移动距离
                startClientY: null,
                // endClientX: null,
                // endClientY: null,
                isBoxSelecting: false, // 标记框选操作状态
                pageIndex: 0, // 当前进行框选的页码
            },
            boxSelectBgInfo: { // 框选背景框的信息
                width: 0,
                height: 0,
                top: 0,
                left: 0,
                widthPer: 0,
                heightPer: 0,
            },
            boxSelectLabels: [], // 框选范围内的字段信息
            isBatchReplace: this.$route.query.isBatchReplace === 'true', // 是否是批量发不同合同
            labelNameConflictDialog: { // 字段名称相同提示弹窗
                visible: false,
                targetLabel: {},
            },
        };
    },
    computed: {
        ...mapState(['currentSummarySentence']),
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
        }),
        ...mapState('template', ['docList', 'currentDocIndex', 'currentPageIndex', 'receivers', 'receiverIndex',
                                 'watermarkList', 'templateStatus', 'ridingSealList', 'focusLabelOpt', 'isBatchSend', 'ifCrossPlatform', 'templatePermissions', 'templateId', 'templateConfig']),
        ...mapGetters('template', ['currentDoc']),
        ...mapGetters(['getIsForeignVersion', 'checkFeat']),
        // 是否是混3
        hybridVersionIsGama() {
            return !!this.hybridServer && this.$hybrid.isGamma(); // 是否是混3
        },
        canCopySeal() {
            return (label) => {
                // 混3不能复制后半文档部分不能复制，且复制章没有的情况下才可复制, 开启投影的不能复制
                return !this.hybridVersionIsGama && this.canDocCopySeal && (label.pageNumber < Number(this.currentDoc.pageSize / 2) && !label.ifDecorate && this.currentDoc.decorateSealSize === 0 && label.pageType !== 'ALL');
            };
        },
        canDocCopySeal() {
            // 国内平台，有新增签署字段权限
            return !this.getIsForeignVersion && !this.ifCrossPlatform && this.templatePermissions.modifySignLabel && this.currentDoc.pageSize > 10 && !this.templateConfig.supportOtherSignature;
        },
        getPageUrl() {
            return (page, pageIndex) => `${page.highQualityPreviewUrl}${this.currentPageIndex ===
                pageIndex && page.showHighDpiImg ? '&highQuality=true' : ''}&_t=${this.initTime}`;
        },
        showBoxSelectTipBtn() {
            // 字段数多于9个时展示字段对齐/框选功能引导按钮，按产品要求限制中文展示
            return this.currentDoc?.labels?.length > 9 && this.$i18n.locale === 'zh';
        },
        currentDocRidingSeals() { // 当前文档的骑缝章list
            return (this.ridingSealList || []).filter((rd, index) => {
                rd.orignIndex = index;
                return rd.selectAll ||
                    (rd.selectDocumentIds || []).includes(this.docList[this.currentDocIndex].documentId);
            });
        },
        docMaxWidth() { // 文档宽度(是否有骑缝章计算宽度)
            const maxWidth = this.currentDoc.documentPageMaxWidth || 0;
            return this.currentDocRidingSeals.length > 0 ? maxWidth + RIDESEAL_SHADOW_WIDTH : maxWidth;
        },
        zoom() { // 合适的缩放
            return (this.docWidth - 100) / this.docMaxWidth;
        },
        // 某一页的标签
        labelList() {
            return (pageNumber, attachmentId, pageIndex, doc = { documentPages: [] }) => {
                // 当前文档的labelList
                const labels = this.currentDoc.labels;
                return (labels || []).concat(this.tempLabel || []).filter(label => {
                    let isPageNumber = label.pageNumber === pageNumber && !label.pageType;
                    if (attachmentId || label.attachmentId) { // attachmentId 为在附页上或者附件的标签
                        isPageNumber = isPageNumber && label.attachmentId === attachmentId;
                    }
                    // pageType 'ALL'：按页渲染的字段，需要添加到每一页,LAST:包含附件的最后一页
                    const isAll = label.pageType === 'ALL';
                    const isLastAttachmentLabel = label.pageType === 'LAST' && pageIndex ===
                        (doc.documentPages?.length - 1);
                    const isEditStatusWordBillLabel = this.templateStatus === 'edit' && label.wordLabel;
                    return !isEditStatusWordBillLabel && (isAll || isLastAttachmentLabel || isPageNumber);
                });
            };
        },
        // 水印图片
        waterMarkPng() {
            const watermarkText = (this.watermarkList || []).map(item => item.watermarkText);
            if (!watermarkText.length) {
                return null;
            }
            return createWaterMark(watermarkText, 300, 200);
        },
        // 聚焦标签后的3个圆角
        hasMarquee() {
            return (pageIndex) => {
                const focusLabelId = this.focusLabelOpt.labelId;
                if (!focusLabelId) {
                    return false;
                }
                const focuslabel = this.currentDoc.labels.find(label => label.labelId === focusLabelId);
                const { labelType, pageNumber, pageType } = focuslabel || {};
                const enabledTypes = ['TEXT', 'DATE_TIME', 'BIZ_DATE', 'TEXT_NUMERIC', 'PICTURE',
                                      'CONFIRMATION_REQUEST_REMARK', 'COMBO_BOX', 'NUMERIC_VALUE', 'SIGNATURE'].includes(labelType);
                const labelReceiver =
                    this.receivers.find(receiver => receiver.receiverId === focuslabel?.receiverId) || {};
                const receiverIsJa = (!labelReceiver.owningPlatform && this.getIsForeignVersion) || labelReceiver.owningPlatform === 'ja';
                const sealScaleEnable = (this.checkFeat.sealDragScale || receiverIsJa) && labelType === 'SEAL';
                return (enabledTypes || sealScaleEnable) && (pageIndex === (pageNumber - 1) || pageType === 'ALL');
            };
        },
        isLabelInSelectBox() {
            return labelId => this.boxSelectLabels.some(label => labelId === label.labelId);
        },
        allDocInputLabels() { // 全部的具名业务字段，插入字段时同名比较要用
            return this.docList.reduce((total, curDoc) => {
                total = total.concat(curDoc.labels.filter(label => INPUT_TYPES.includes(label.labelType)).map(a => ({
                    labelName: a.labelName,
                    labelId: a.labelId,
                    labelType: a.labelType,
                })));
                return total;
            }, []);
        },
    },
    watch: {
        zoom(v) {
            this.setZoom(v);
        },
        currentSummarySentence: {
            handler(v) {
                this.setSummarySentences(v);
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        ...mapMutations('template', ['setDocList', 'setCurrentDocIndex', 'setFocusLabelOpt', 'setCurrentPageIndex', 'setZoom', 'setRecipients']),
        ...mapActions('template', ['delAllQRCodeLabels']),
        // 指定签署印章切换
        async onChangeSpecialSeal(sealChange, label) {
            await this.saveLabel(label, {
                ...label,
                specialSealInfo: {
                    ...label.specialSealInfo,
                    ...sealChange,
                },
            }, false);
            // 后续该签署人拖章均用该印章
            this.setRecipients(this.receivers.map(item => {
                if (item.receiverId === label.receiverId) {
                    item.specialSealInfo = {
                        ...label.specialSealInfo,
                        ...sealChange,
                    };
                }
            }));
        },
        computeRgbColor(label, receiverFill) {
            // 签署方或者印章、签名、日期类型时，根据身份选择
            if (receiverFill || COLOR_TYPES.includes(label.labelType) || (label.labelExtends && label.labelExtends.receiverFill)) {
                return this.rgbColorInfo[this.findReceiverIdx(label.receiverId) % 8] || 'transparent';
            } else {
                // 发件方使用固定颜色
                return  rgbColorInfo[0];
            }
        },
        switchToHighDpi(page) {
            page.showHighDpiImg = !page.showHighDpiImg;
        },
        findReceiverIdx(id) {
            return this.receivers.findIndex(item => item.receiverId === id);
        },
        onBatchChange(newLabels) {
            const labelList = this.currentDoc.labels;
            newLabels.forEach(nLabel => {
                const oldLabelInd = labelList.findIndex(a => a.labelId === nLabel.labelId);
                this.$set(labelList[oldLabelInd], 'labelPosition', nLabel.labelPosition);
            });
        },
        onMousedown(e) {
            // 避免鼠标右键点击触发框选
            if (e && e.button === 2) {
                return;
            }
            const $el = e.target.closest('.point-position-doc-page-con');
            // 1）仅限文档内框选；2）仅在编辑阶段支持框选；3）点击框选编辑栏时不做处理
            if (!$el ||
                this.templateStatus === 'use' ||
                e.target.closest('.box-select-edit') ||
                e.target.closest('.marquee-label')
            ) {
                return;
            }

            // 1）拖字段过程中不做框选并取消选中，2）点击字段时取消选中
            if (this.iconDragStatus === 'started' || e.target.closest('.label-container')) {
                this.boxSelectLabels = [];
                return;
            }

            this.resetLabelFocus();
            const pageI = Number($el.getAttribute('page-index') || 0);
            const postion = this.getLabelPostion(e, $el);
            // 记录起始点信息
            this.boxSelectObj = {
                startPosX: postion.x,
                startPosY: postion.y,
                startClientX: e.clientX,
                startClientY: e.clientY,
                isBoxSelecting: true,
                pageIndex: pageI,
            };
        },
        onBoxSelectMove(e) {
            const { startPosX, startPosY, startClientX, startClientY, pageIndex } =
                this.boxSelectObj;
            const { height: pageHeight, width: pageWidth } = this.docList[this.currentDocIndex].documentPages[pageIndex];
            let offsetX = e.clientX - startClientX;
            let offsetY = e.clientY - startClientY;
            // 框选距离不能超出当前文档边界
            offsetX = e.clientX > startClientX ? Math.min(offsetX, pageWidth * this.zoom - startPosX) : Math.max(-startPosX, offsetX);
            offsetY = e.clientY > startClientY ? Math.min(offsetY, pageHeight * this.zoom - startPosY) : Math.max(-startPosY, offsetY);

            // clientX坐标经过zoom，需要处理为实际值
            this.boxSelectBgInfo.height = +(Math.abs(offsetY) / this.zoom).toFixed(4);
            this.boxSelectBgInfo.width = +(Math.abs(offsetX) / this.zoom).toFixed(4);
            this.boxSelectBgInfo.top = +((Math.min(startPosY, startPosY + offsetY)) / pageHeight / this.zoom).toFixed(4);
            this.boxSelectBgInfo.left = +(Math.min(startPosX, startPosX + offsetX) / pageWidth / this.zoom).toFixed(4);
            this.boxSelectBgInfo.widthPer = +(this.boxSelectBgInfo.width / pageWidth).toFixed(4); // 用于计算字段包含关系;
            this.boxSelectBgInfo.heightPer = +(this.boxSelectBgInfo.height / pageHeight).toFixed(4);
        },
        onBoxSelectEnd() {
            const { left: selectStartX, top: selectStartY, widthPer: selectWidth, heightPer: selectHeight } =
                this.boxSelectBgInfo;
            const selectEndX = selectStartX + selectWidth; // 计算选区右下角坐标
            const selectEndY = selectStartY + selectHeight;
            const { pageIndex } = this.boxSelectObj;
            const curPageLabels = this.labelList(Number(pageIndex) + 1); // pageIndex 0对应pageNumber 1

            this.boxSelectLabels = [];
            curPageLabels.forEach(label => {
                const { x, y, height } = label.labelPosition;
                const left = x;
                // 新的坐标系根据文字定位，标签的左下角相对于pdf左下角的位置
                const top = 1 - y - height;
                // label左上角点坐标要在选区内，selectStartX =< left <= selectEndX，selectStartY <= top <= selectEndY
                if (left >= selectStartX && left <= selectEndX && top >= selectStartY && top <= selectEndY) {
                    this.boxSelectLabels.push(label);
                }
            });
            this.resetBoxSelectInfo();
        },
        resetBoxSelectInfo() {
            // 框选完毕重制数据
            this.$set(this, 'boxSelectObj', {
                startPosX: null,
                startPosY: null,
                startClientX: null,
                startClientY: null,
                isBoxSelecting: false,
                pageIndex: 0,
            });
            // mouseup后清除选区背景框数据
            this.$set(this, 'boxSelectBgInfo', {
                width: 0,
                height: 0,
                top: 0,
                left: 0,
                widthPer: 0,
                heightPer: 0,
            });
        },
        // 标签拖拽移动
        onLabelStart(e, label) {
            if (!label.labelId) {
                return;
            }
            this.move.eX = e.clientX;
            this.move.eY = e.clientY;
            this.move.labelX = label.labelPosition.x;
            this.move.labelY = label.labelPosition.y;
            this.handleKeywordMove(label, 'start');
        },
        // 标签拖动过程中，计算新的标签位置
        onLabelMove(e, label, page) {
            console.log('onLabelMove');
            if (!label.labelId) {
                return;
            }
            const distanceX = (e.clientX - this.move.eX) / this.zoom;
            const distanceY = (e.clientY - this.move.eY) / this.zoom;
            label.labelPosition.x = this.move.labelX + distanceX / (page.width);
            label.labelPosition.y = this.move.labelY - distanceY / (page.height);
            this.handleKeywordMove(label, 'move');
        },
        // 标签拖拽结束
        async onLabelEnd(e, label) {
            console.log('onLabelEnd');
            const $el = e.target.closest('.point-position-doc-page-con');
            // 修复弱网情况下，用户创建标签接口未返回，又快速拖动标签导致标签重复创建的问题
            if (!label.labelId) {
                return;
            }
            let pageNumber, y, x, width, height, actualWidth;
            const labelPosition = label.labelPosition;
            const originLabelPageNumber = label.pageNumber;
            // 未拖出本页面时，直接修正
            if (labelPosition.y >= -labelPosition.height && labelPosition.y <= 1) { // 当前页，直接修正边界
                pageNumber = label.pageNumber;
                width = labelPosition.width;
                height = labelPosition.height;
                y = labelPosition.y;
                x = labelPosition.x;
                if (label.attachmentId) {
                    pageNumber = Number($el.getAttribute('page-index')) + 1;
                }
            } else if (labelPosition.y < -labelPosition.height) { // 向下拖动跨页
                ({ pageNumber, x, y, width, height } = this.computedYDown(label));
            } else if (labelPosition.y > 1) { // 向上拖动，超出当前页面
                if (label.attachmentId) {
                    label.pageNumber = Number($el.getAttribute('page-index')) + 1;
                }
                ({ pageNumber, x, y, width, height } = this.computedYUp(label));
            }
            if (label.labelType === 'SIGNATURE' && label.labelExtends.supportFDA) {
                const { width: pageWidth } = (this.currentDoc.documentPages || [])[pageNumber - 1];
                actualWidth = (Math.round(width * pageWidth * (1 + FDA_WIDTH_RATE))) / pageWidth; // FDA信息宽度为签名的1.5倍
            } else {
                actualWidth = width;
            }
            labelPosition.x = this.fixBoundary(x, actualWidth);
            labelPosition.y = this.fixBoundary(y, height);
            label.pageNumber = pageNumber;
            // 拖拽完成要修正标签的宽高
            labelPosition.width = width;
            labelPosition.height = height;

            this.handleKeywordMove(label, 'end');
            try {
                await this.saveLabel(label, {
                    ...label,
                    labelPosition,
                    documentId: this.currentDoc.documentId,
                }, true);
            } catch (err) {
                labelPosition.x = this.move.labelX;
                labelPosition.y = this.move.labelY;
                label.pageNumber = originLabelPageNumber;
            }
        },
        // 编辑区保存标签修改
        onLabelButtonChange(metaData, label, type, nextK) {
            this.$emit('save-meta', label, type, nextK, metaData);
        },
        // 计算向上拖动时移动到第几页
        computedYUp(label) {
            const page = this.currentDoc.documentPages || [];
            const pageNumber = label.pageNumber;
            const labelPosition = label.labelPosition;
            // 获取初始时标签所在页面的宽高
            const { width: oriWidth, height: oriHeight } = page[pageNumber - 1];
            // 超出时的默认值为第一页的最顶部
            let newPageIndex = 0;
            let height = 0;
            let y = 1 - labelPosition.height;
            const distanceY = Math.abs(labelPosition.y - 1) * oriHeight;
            const pageList = page.slice(0, pageNumber - 1).reverse();
            pageList.some((page, pageIndex) => {
                height += (page.height + this.pageSpace);
                if (distanceY <= height) {
                    newPageIndex = pageNumber - pageIndex - 2;
                    // 计算百分比
                    y = 1 - (height - distanceY) / page.height;
                }
                return distanceY <= height;
            });

            const { width: pageWidth, height: pageHeight } = page[newPageIndex];
            const x = (oriWidth * labelPosition.x - (oriWidth - pageWidth) / 2) / pageWidth;
            return {
                pageNumber: newPageIndex + 1,
                y,
                x,
                width: labelPosition.width * oriWidth / pageWidth, // 根据新页面的宽高，重新计算标签的宽高比
                height: labelPosition.height * oriHeight / pageHeight,
            };
        },
        // 计算向上拖动时移动到第几页
        computedYDown(label) {
            const page = this.currentDoc.documentPages || [];
            const pageNumber = label.pageNumber;
            const labelPosition = label.labelPosition;
            // 获取初始时标签所在页面的宽高
            const { width: oriWidth, height: oriHeight } = page[pageNumber - 1];
            const distanceY = Math.abs(labelPosition.y) * oriHeight;
            // 超出时的默认值为最后一页的最底部
            let newPageIndex = page.length - 1;
            let y = 0;
            let height = 0;
            // 从当前文档的当前页的下一页开始计算高度
            page.slice(pageNumber).some((page, pageIndex) => {
                height += (page.height + this.pageSpace);
                if (distanceY <= height) {
                    newPageIndex = pageIndex + pageNumber;
                    // 计算百分比
                    y = (height - distanceY) / (page.height);
                }
                return distanceY <= height;
            });
            const { width: pageWidth, height: pageHeight } = page[newPageIndex];
            const x = (oriWidth * labelPosition.x - (oriWidth - pageWidth) / 2) / pageWidth;

            return {
                pageNumber: newPageIndex + 1,
                y,
                x,
                width: labelPosition.width * oriWidth / pageWidth, // 根据新页面的宽高，重新计算标签的宽高比
                height: labelPosition.height * oriHeight / pageHeight,
            };
        },
        // 订正坐标的边界
        fixBoundary(ratio, space) {
            if (ratio < 0) {
                return 0;
            }
            if (ratio > 1 - space) {
                return 1 - space;
            }
            return ratio;
        },
        // 保存标签
        saveLabel(label, opts, isMove) {
            // SAAS-21442 模板字段坐标一致，导出排序不一致问题
            const page = this.currentDoc.documentPages || [];
            const pageNumber = opts.pageNumber;
            const labelPosition = opts.labelPosition;
            const currentPage = page[pageNumber - 1];
            // 获取初始时标签所在页面的宽高
            const { width, height, attachmentId } = currentPage;
            // 换算x,y坐标在右侧编辑框的绝对值
            const vX = Math.round((labelPosition.x) * width);
            const vY = Math.round((1 - labelPosition.y) * height);
            labelPosition.x = vX / width;
            labelPosition.y = 1 - vY / height;
            if (isMove) {
                // CFD-14732 附件标签 拖到合同，数据修正
                opts.attachmentId = attachmentId || null;
                opts.pageNumber = currentPage.pageNumber;
            }
            // 如果是签署位置不同步，则增加index参数-合同索引，前者为前端临时标记，后者为后端数据标记
            if (opts.notSignPosSync || opts.labelExtends?.ap === 1) {
                opts.index = this.idxInBatch;
            }
            return updateLabel(opts, this.$hybrid.isGamma()).then(res => {
                this.updateLabels(label, res.data, isMove);
            });
        },
        // 更新标签
        updateLabels(label, data, isMove) {
            const labelList = this.currentDoc.labels;
            if (isMove) { // 如果只是拖拽位置，只更新当前项
                // notSignPosSync标记批量发不同合同不同步签署位置，只针对当前合同起作用
                const index = label.notSignPosSync || label.labelExtends?.ap === 1 ? 0 : data.findIndex((a) =>
                    a.labelId === label.labelId);
                const newLabel = data[index];
                const oldLabelInd = labelList.findIndex(a => a.labelId === label.labelId);

                this.$set(labelList, oldLabelInd,  newLabel);
            } else if (data.length > 1) { // 存在同名字段需要更新，else不存在的话只需要更新自己
                this.$emit('up-same-label', data);
            } else if (!label.labelId) { // 新增标签且没有同名字段
                this.setFocusLabelOpt({
                    labelId: data[0].labelId,
                    labelButtonInd: 0,
                });
                labelList.push(data[0]);
            }
        },
        // 新建标签
        onMousemove: throttle(function(e) {
            if (this.boxSelectObj.isBoxSelecting) {
                return this.onBoxSelectMove(e);
            }
            if (this.iconDragStatus !== 'started') {
                return;
            }
            const $el = e.target.closest('.point-position-doc-page-con');
            if (!$el) {
                this.creation.status = 'pedding';
                this.tempLabel = null;
                return;
            }
            const pageI = $el.getAttribute('page-index');
            const { documentId, documentPages: page } = this.currentDoc;
            const postion = this.getLabelPostion(e, $el);
            const x = postion.x / this.zoom;
            const y = postion.y / this.zoom;
            const { width: pageWidth, height: pageHeight, pageNumber, attachmentId } = page[pageI];
            // pageNumber值比pageIndex大1，限制最大页码
            // const pageNumber = pageI > pageSize ? pageSize : parseFloat(pageI) + 1;
            const jaReceiver = (!this.receivers[this.receiverIndex].owningPlatform && this.getIsForeignVersion) || this.receivers[this.receiverIndex].owningPlatform === 'ja';
            // 判断如果是文本标签的话，取自适应宽高  labelInfo 中返回的是常量值
            // eslint-disable-next-line prefer-const
            let { width: labelWidth, height: labelHeight } = ['TEXT', 'DATE', 'BIZ_DATE', 'TEXT_NUMERIC', 'DATE_TIME'].includes(this.float.type) ? textLabelInfo(this.float) : labelInfo(this.float.type, { jaReceiver });
            let items;
            if (['MULTIPLE_BOX', 'SINGLE_BOX'].includes(this.float.type)) {
                items = [{ itemX: 10, itemY: 10, itemValue: this.$t('pointPositionDoc.checkboxName', { count: 1 }) }, { itemX: 10, itemY: 41, itemValue: this.$t('pointPositionDoc.checkboxName', { count: 2 }) }];
                // 如果是从业务字段拖过来的
                if (this.float.items) {
                    items = this.float.items.map((name, index) => {
                        return { itemX: 10, itemY: (10 + 31 * index), itemValue: name };
                    });
                    labelHeight = this.changeLabelSize(items);
                }
            }
            // 下拉框选项
            if (['COMBO_BOX'].includes(this.float.type)) {
                items = [{ itemValue: this.$t('pointPositionDoc.checkboxName', { count: 1 }) }, { itemValue: this.$t('pointPositionDoc.checkboxName', { count: 2 }) }];
                // 如果是从业务字段拖过来的
                if (this.float.items) {
                    items = this.float.items.map((name) => {
                        return { itemValue: name };
                    });
                }
            }
            if (this.float.type === 'CONFIRMATION_REQUEST_SEAL') {
                items = [{ itemX: 10, itemY: 10, itemValue: 'AGREE' }, { itemX: 242, itemY: 10, itemValue: 'REFUSE' }];
            }
            // 转换坐标
            let { labelPosition, labelExtends } = labelCoordinateTransform(
                {
                    labelPosition: {
                        x,
                        y,
                        width: labelWidth,
                        height: labelHeight,
                    },
                    labelType: this.float.type,
                    labelExtends: {
                        items,
                        ...(this.float.labelExtends || {}),
                    },
                },
                pageWidth,
                pageHeight,
            );
            labelPosition = { // 拖拽过程中的边界修正
                ...labelPosition,
                y: this.fixBoundary(labelPosition.y, labelPosition.height),
                x: this.fixBoundary(labelPosition.x, labelPosition.actualWidth), // FDA只做展示，新增一个包含fda宽度的actualWidth变量来控制边界修正
            };
            labelExtends = { // 额外属性
                ...labelExtends,
                receiverFill: this.float.receiverFill,
                pxFontSize: this.float.fontSize,
                required: this.float.necessary,
                refBizFieldId: this.float.refBizFieldId,
                alignment: labelExtends.alignment || 'left',
            };
            if (this.creation.status === 'ready' && this.tempLabel) {
                // 修改标签
                this.tempLabel = {
                    ...this.tempLabel,
                    pageNumber,
                    labelPosition,
                    labelExtends,
                };
                this.attractionField(page[pageI], this.tempLabel); // 智能坐标引力场
            } else {
                // 创建标签
                this.tempLabel = {
                    labelId: '', // 新建标签，labelId为空
                    receiverId: this.receivers[this.receiverIndex].receiverId,
                    labelType: this.float.type,
                    labelName: this.float.name,
                    documentId,
                    pageNumber,
                    labelPosition,
                    labelExtends,
                    keywordPosition: {
                        keyword: '',
                        appearOrder: '',
                        moveX: 0,
                        moveY: 0,
                    },
                };
                // 发送阶段印章有指定章配置
                if (this.templateStatus === 'use' && !this.isBatchSend && this.float.type === 'SEAL') {
                    this.tempLabel.specialSealInfo = this.receivers[this.receiverIndex].specialSealInfo;
                }
                this.creation.status = 'ready';
            }
            this.tempLabel.attachmentId = attachmentId;
        }, 24),
        async onMouseup() {
            if (this.boxSelectObj.isBoxSelecting) {
                return this.onBoxSelectEnd();
            }
            if (this.creation.status !== 'ready') {
                return;
            }
            // 新建标签时清空框选
            this.boxSelectLabels = [];
            this.creation.status = 'done';
            const mapObject =  {
                'SEAL': '盖章',
                'SIGNATURE': '签字',
                'CONFIRMATION_REQUEST_SEAL': '',
                'DATE': '签署日期',
                'TEXT': '文本',
                'TEXT_NUMERIC': '数字',
                'NUMERIC_VALUE': '数值',
                'CONFIRMATION_REQUEST_REMARK': '不符合章备注',
                'SINGLE_BOX': '单选框',
                'MULTIPLE_BOX': '复选框',
                'PICTURE': '图片',
                'BIZ_DATE': '日期',
                'DATE_TIME': '时刻',
                'COMBO_BOX': '下拉框',
                'QR_CODE': '二维码',
            };
            const isDynamic = this.$route.query.isDynamic === 'true';
            this.$sensors.track({
                eventName: this.templateStatus === 'use' ? 'Ent_ContractSendDetail_BtnClick' : 'Ent_TemplateCreate_BtnClick',
                eventProperty: {
                    page_name: '指定签署位置',
                    ...(this.templateStatus === 'edit' &&  { template_type: '静态模板' }),
                    ...((this.templateStatus === 'use' && !this.isBatchSend) && { template_type: isDynamic ? '动态模板' : '静态模板' }),
                    ...(this.templateStatus === 'use' && { is_batch_send: this.isBatchSend }),
                    first_category: '合同内容框',
                    icon_name: mapObject[this.tempLabel.labelType],
                },
            });
            this.attracionAmend();
            if (!FLOAT_TYPES.includes(this.tempLabel.labelType) && this.allDocInputLabels.find(a => a.labelName ===
                this.tempLabel.labelName)) {
                this.labelNameConflictDialog = {
                    visible: true,
                    targetLabel: { ...this.tempLabel },
                };
                return;
            }
            await this.postLabelUpdate();
        },
        handleConfirmLabelNameConflict(name) {
            this.tempLabel.labelName = name;
            this.postLabelUpdate();
        },
        handleCloseLabelNameConflict() {
            this.labelNameConflictDialog = {
                visible: false,
                targetLabel: {},
            };
            this.tempLabel = null;
        },
        async postLabelUpdate() {
            try {
                await this.saveLabel({}, {
                    labelId: '', // 新建标签，labelId为空
                    // contractId: this.templateId,
                    ...this.tempLabel,
                    appendedForDraft: this.templateStatus === 'use' && TEMPERORY_TYPES.includes(this.tempLabel.type)
                        ? true : undefined,
                    labelName: this.formatNewLabelName(this.tempLabel),
                }, false);
            } finally {
                this.tempLabel = null;
            }
        },
        // 获取鼠标相对于目标元素左/上边界的距离
        getLabelPostion(event, targetEl) {
            const { left, top } = targetEl.getBoundingClientRect();
            return {
                x: event.clientX - Math.floor(left),
                y: event.clientY - Math.floor(top),
            };
        },
        // 计算高度
        changeLabelSize(newButtons) {
            const sortList = newButtons.sort((a, b) => a.itemY - b.itemY);
            const buttonFirstY = sortList[0].itemY;
            const buttonLastY = sortList[newButtons.length - 1].itemY;
            const buttonStyle = labelInfo(this.float.type).button;
            return buttonLastY - buttonFirstY + buttonStyle.initSplit * 2 + buttonStyle.height;
        },
        // 重置选中的标签
        resetLabelFocus() {
            this.setFocusLabelOpt({
                labelId: '',
                labelButtonInd: -1,
            });
        },
        // 取消聚焦
        hideFocusLabel(e) {
            if (
                !e.target.closest('.un-loss-focus') && // 去除部分标记组件
                !e.target.closest('.marquee-label') &&
                !e.target.closest('.label-container') &&
                !e.target.closest('.label-edit-cpn') &&
                !e.target.closest('.next-step') && // 单据模板识别字段编辑
                !e.target.closest('.edit-label-row') && // 单据模板字段编辑
                !e.target.closest('.option') &&
                !e.target.closest('.option-delete') &&
                !e.target.closest('.label-name-conflict-dialog') &&
                !e.target.closest('.riding-seal-item') // 增加骑缝章的内容
            ) {
                this.resetLabelFocus();
            }
        },
        // 删除骑缝章
        handleDeleteRidingSeal(index, allDelete) {
            const { decorateId, receiverId, selectDocumentIds, selectAll } = this.ridingSealList[index];
            const currentDocumentId = this.currentDoc.documentId;
            const params = {
                selectAll: allDelete,
                receiverId,
                selectDocumentIds: allDelete ? [] : [currentDocumentId],
            };
            deleteRideSealByDocs({
                decorateId,
                params,
            }).then(() => {
                if (allDelete || selectDocumentIds.length === 1 || this.docList.length === 1) { // 全删、删除最后一个骑缝章时移除骑缝章数据
                    this.ridingSealList.splice(index, 1);
                } else if (selectAll) { // 如果之前是选中全部文档，此时selectDocumentIds为[]，需要单独处理
                    this.ridingSealList[index].selectAll = false;
                    this.ridingSealList[index].selectDocumentIds = this.docList.map(a => a.documentId).filter(b => b !==
                        currentDocumentId);
                } else {
                    // 删除当前的骑缝章只需要移除对应的documentId
                    const targetIndex = this.ridingSealList[index].selectDocumentIds.findIndex(a => a ===
                        currentDocumentId);
                    this.ridingSealList[index].selectAll = false;
                    this.ridingSealList[index].selectDocumentIds.splice(targetIndex, 1);
                }
                this.$MessageToast.success(this.$t('templateCommon.deleteSucc'));
            });
        },
        // 删除所有装饰章
        deleteAllDecorate(labelId) {
            deleteLabelAllDecorate(this.templateId, labelId).then(() => {
                this.$MessageToast.success(this.$t('pointPositionDoc.deleteTip'));
                for (let i = this.currentDoc.labels.length - 1; i >= 0; i--) {
                    if (this.currentDoc.labels[i].ifDecorate) { // 删除装饰章
                        this.currentDoc.labels.splice(i, 1);
                    }
                }
                this.resetLabelFocus();
                this.currentDoc.decorateSealSize = 0;
            });
        },
        // 删除标签
        handleDeleteLabel(id, list, index, type) {
            // 删除查验码
            if (list[index].labelType === 'QR_CODE') {
                return this.confirmDelQRCodeLabel(id);
            }
            deleteLabel(id, type).then(() => {
                this.$MessageToast.success(this.$t('pointPositionDoc.deleteTip'));
                if (list[index].ifDecorate) {
                    this.currentDoc.decorateSealSize--;
                }
                list.splice(index, 1);
                this.resetLabelFocus();
            });
        },
        // 删除查验码二次确认
        confirmDelQRCodeLabel(labelId) {
            this.$confirm('可使用扫一扫查验码的方式更快鉴别电子合同真伪。如果合同参与方需要在线下使用合同，建议您保留查验码。', '提示', {
                confirmButtonText: '不删除',
                cancelButtonText: '删除',
                type: 'warning',
                distinguishCancelAndClose: true,
                beforeClose: (action, instance, done) => {
                    if (action === 'cancel') {
                        this.handleBatchDelQRCodeLabel(labelId);
                    }
                    done();
                },
            });
        },
        // 批量删除合同查验码
        handleBatchDelQRCodeLabel(labelId) {
            this.delAllQRCodeLabels({ labelId }).then(() => {
                this.$MessageToast.success(this.$t('pointPositionDoc.deleteTip'));
                this.resetLabelFocus();
            });
        },
        // 预览二维码样式弹窗
        handleShowQRCodePreviewDialog(codeObject) {
            this.contractQRBlockPreviewDialog.visible = true;
            this.contractQRBlockPreviewDialog.codeObject = codeObject;
        },
        // 文档滚动计算当前页
        onDocScroll: debounce(function(e) {
            const initY = document.querySelector('.point-position-doc-wrapper') && document.querySelector('.point-position-doc-wrapper').offsetTop || 0;
            const scrollTop = (e.target.scrollTop - initY)  / this.zoom;
            const $currentDoc = document.querySelectorAll('.point-position-doc-pages')[this.currentDocIndex];
            const docPages = ($currentDoc && $currentDoc.querySelectorAll('.point-position-doc-page')) || [];
            let pageIndex = 0;
            for (let i = 0; i < docPages.length; i++) {
                if (docPages[i].offsetTop + docPages[i].offsetHeight > scrollTop + 1) {
                    pageIndex = i;
                    break;
                }
            }
            this.setCurrentPageIndex(pageIndex);
        }, 100),
        // 切换文档
        changeDoc() {
            this.$emit('change-doc', this.currentDocIndex + 1);
        },
        // 临时字段-处理单选框，复选框name，不符合章备注
        formatNewLabelName(label) {
            const canNotSameName = (['SINGLE_BOX', 'MULTIPLE_BOX', 'COMBO_BOX'].includes(label.labelType) && !this.float.items) || label.labelType === 'CONFIRMATION_REQUEST_REMARK';
            if (canNotSameName) {
                let sameTypeLabels = [];
                for (let i = 0; i < this.docList.length; i++) {
                    const tempList =  this.docList[i].labels.filter(a => a.labelType === label.labelType); // 当前已存在同类型标签
                    sameTypeLabels = sameTypeLabels.concat(tempList);
                }

                let count = sameTypeLabels.length;
                const nameStr = labelInfo(label.labelType).name;
                let sameNameLabel;
                do {
                    count++;
                    sameNameLabel = sameTypeLabels.filter(a => {
                        return a.labelName === `${nameStr}${count}`;
                    });
                } while (sameNameLabel.length > 0);

                return `${nameStr}${count}`;
            }
            return label.labelName;
        },
        // 标签移动，空白模版关键字偏移处理
        handleKeywordMove(label, type) {
            if ((this.currentDoc.blankDocument || this.currentDoc.billDocument) && label.keywordPosition && label.keywordPosition.keyword) {
                if (type === 'start') {
                    this.move.keywordX = label.keywordPosition.moveX;
                    this.move.keywordY = label.keywordPosition.moveY;
                } else if (['move', 'end'].includes(type)) {
                    const { keywordX, keywordY } = this.move;
                    const labelPosition = label.labelPosition;
                    label.keywordPosition = {
                        ...label.keywordPosition,
                        moveX: keywordX + (labelPosition.x - this.move.labelX),
                        moveY: keywordY + (labelPosition.y - this.move.labelY),
                    };
                }
            }
        },
        clearSummaryMarks() {
            try {
                const parents = document.querySelectorAll('.point-position-doc-page-con');
                parents.forEach(parent => {
                    parent.querySelectorAll('.summary-sentence-mark').forEach(a => a.remove());
                });
            } catch (error) {
                console.log(error);
            }
        },
        setSummarySentences(data) {
            this.clearSummaryMarks();
            if (data?.markPositions?.length) {
                data.markPositions.forEach((a, index) => {
                    a.contentPositions && a.contentPositions.forEach((b, idx) => {
                        const dom = document.createElement('div');
                        dom.className = 'summary-sentence-mark';
                        dom.style.position = 'absolute';
                        dom.style.left = `${b.x * 100}%`;
                        dom.style.top = `${b.y * 100}%`;
                        dom.style.width = `${b.width * 100}%`;
                        dom.style.height = `${b.height * 100}%`;
                        dom.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
                        const parents = document.getElementsByClassName('point-position-doc-page-con');
                        parents[b.page - 1].appendChild(dom);
                        index === 0 && idx === 0 && (dom.scrollIntoView({ block: 'center' }));
                    });
                });
            }
        },
    },
    mounted() {
        this.docWidth = this.$refs.docContent?.getBoundingClientRect().width;
        const _this = this;
        window.onresize = () => {
            _this.docWidth = _this.$refs.docContent.getBoundingClientRect().width;
        };
        document.addEventListener('mousedown', this.onMousedown);
        document.addEventListener('mousemove', this.onMousemove);
        document.addEventListener('mouseup', this.onMouseup);
        document.addEventListener('click', this.hideFocusLabel);
    },
    beforeDestroy() {
        document.removeEventListener('mousedown', this.onMousedown);
        document.removeEventListener('mousemove', this.onMousemove);
        document.removeEventListener('mouseup', this.onMouseup);
        document.removeEventListener('click', this.hideFocusLabel);
        window.onresize = null;
    },
};
</script>
<style lang="scss">
.point-position-doc {
    height: 100%;
    overflow: auto;
    margin-right: 211px;
    margin-left: 211px;
    &.no-site {
        margin-left: 0;
    }
    [dir=rtl] & {
        margin-left: 211px;
        &.no-site {
            margin-right: 0;
        }
    }
    .point-position-doc-con{
        overflow: auto;
        height: 100%;
        background: $--background-color-regular;
        display: flex;
        flex-direction: column;
        [dir=rtl] & {
            direction: ltr;
        }
        .point-position-doc-wrapper{
            height: 100%;
            margin: 0 auto;
            flex: 1;
            padding: 0 10px;
        }
        .point-position-doc-title{
            line-height: 38px;
            font-size: 16px;
            color: $--color-text-primary;
            border-bottom: 1px solid $--border-color-light;
            text-align: center;
        }
        .point-position-doc-list{
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            // overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        .next-doc-btn {
            color: $theme-color;
            margin: 15px 0;
            text-align: center;
            font-weight: 700;
            font-size: 14px;
            cursor: pointer;
        }
        .box-select-bg {
            background: rgba(0,0,0, .05);
            border: 1px solid $border-color;
            position: absolute;
            z-index: 20000;
        }
    }
    // .point-position-doc-page{
        // transform-origin: top left;
        // display: inline-block;
    // }
    // .point-position-doc-page-con-wrapper{
    //     margin: 0 auto;
    // }
    .point-position-doc-page-con{
        position: relative;
        user-select: none;
        margin: 0 auto;
        .image{
            background-color: $--color-white;
            background-repeat: repeat;
            box-shadow: 0 0 8px 0 rgba(0,0,0,0.15);
            width: 100%;
            height: 100%;
        }
        .water-mark-back{
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }
        .trigger-con{
            border: 1px dashed $theme-color;
            position: absolute;
            box-sizing: border-box;
        }
        .riding-seals {
            position: absolute;
            box-sizing: border-box;
            padding-top: 1px;
            width: 160px;
            border: 1px dashed $theme-color;
            background: rgba(18, 127, 210, 0.05);
            top: 0;
            bottom: 0;
            right: -116px;
            z-index: 1;
            height: 100%;

            &-bg {
                margin-left: 41px;
                width: 116px;
                height: 100%;
                background: $--background-color-secondary;
                background-image: linear-gradient(270deg, rgba(255, 255, 255, 0.50) 0%, rgba(217, 217, 217, 0.50) 100%);
                background-size: 23px;
            }
        }
    }
    .point-position-doc-page-footer{
        font-size: 12px;
        color: $--color-info;
        line-height: 20px;
        text-align: left;
        margin: 0 auto;
        .footer-text {
            display: inline-block;
            width: calc(100% - 120px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .switch-icon {
            float: right;
            line-height: 20px;
            padding-right: 5px;
            cursor: pointer;
        }
    }

    .QRCode-preview-dialog *{
        box-sizing: border-box;
    }
}

</style>
