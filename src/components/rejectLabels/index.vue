<template>
    <div class="label-container">
        <div class="send-label-default" :style="defaultStyle">
            <div class="send-label-wrapper"
                :style="wrapperStyle"
            >
                <!-- 印章、签名等图片字段-->
                <div
                    v-if="imgLabelTypes.includes(labelType) "
                >
                    <img
                        :width="labelWidth"
                        :height="labelHeight"
                        :src="labelImgUrl"
                        alt="img"
                    />
                    <CommonTip
                        v-if="['PICTURE','SEAL','SIGNATURE'].includes(labelType)"
                        effect="dark"
                        :content="$t('rejectSigner.placeTop')"
                        placement="right"
                    >
                        <span slot="reference" class="zIndexIcon" @click="changeZIndex($event)">
                            <i class="el-icon-ssq-zhiyudiceng"></i>
                        </span>
                    </CommonTip>
                </div>
                <!-- 非图片字段-->
                <div v-else>
                    {{ formatValue(label) }}
                </div>
                <!-- 驳回重签选项 CFD-23449 25为固定checkbox宽度+5，避免类似场景出现布局异常 -->
                <RejectSignerRadio :isInLeftSide="pageWidth * label.x < 25"
                    :label="label"
                    @rejectOption="handelRejectLabel"
                ></RejectSignerRadio>
            </div>
        </div>
    </div>
</template>
<script>
import RejectSignerRadio from 'components/rejectSignerRadio';
import dayjs from 'dayjs';
const IMG_LABEL_TYPES = ['SEAL', 'SIGNATURE', 'PICTURE', 'CONFIRMATION_REQUEST_SEAL'];
export default {
    components: {
        RejectSignerRadio,
    },
    props: {
        pageHeight: {
            type: Number,
        },
        pageWidth: {
            type: Number,
        },
        label: {
            type: Object,
        },
        color: {
            type: String,
        },
        cacheZIndex: {
            type: Number,
            default: 10,
        },
    },
    data() {
        return {
            imgLabelTypes: IMG_LABEL_TYPES,
        };
    },
    computed: {
        labelType() {
            return this.label.type;
        },
        // 默认标签定位
        defaultStyle() {
            return this.labelDefaultStyle(this.label);
        },
        // 默认标签大小
        wrapperStyle() {
            return this.labelWrapperStyle(this.label, this.color, this.labelWidth, this.labelHeight);
        },
        // 标签的高度
        labelHeight() {
            return this.pageHeight * this.label.height;
        },
        // 标签的宽度
        labelWidth() {
            if (this.label.labelType === 'CONFIRMATION_REQUEST_SEAL') {
                return this.pageWidth * this.label.width / 2;
            }
            return this.pageWidth * this.label.width;
        },
        isTemplateSpecialSeal() {
            return this.label.templateSpecialSealPreview.useTemplateSpecialSeal;
        },
        labelImgUrl() {
            return this.isTemplateSpecialSeal ? this.label.templateSpecialSealPreview.previewUrl : this.label.previewUrl;
        },
    },
    methods: {
        formatValue(label) {
            const { value, dateFieldFormat, type } = label;
            if (!value) {
                return '';
            }
            // 日期、时间格式需要转换后展示
            switch (type) {
                case 'BIZ_DATE':
                case 'DATE':
                    return dayjs(value).format(dateFieldFormat.toUpperCase());
                case 'DATE_TIME':
                    return dayjs(Number(value)).format(dateFieldFormat);
                default:
                    return label.value;
            }
        },
        // 处理驳回操作
        handelRejectLabel(item) {
            this.$emit('rejectLabel', item);
        },
        labelWrapperStyle(label, color, labelWidth, labelHeight) {
            const labelType = label.labelType;
            let textAlign = 'left';
            if (['TEXT', 'TEXT_NUMERIC'].includes(labelType)) {
                if (label.labelExtends) {
                    const { alignment } = label.labelExtends;
                    textAlign = alignment;
                }
            }
            return {
                backgroundColor: `rgba(${color}, 0.8)`,
                width: `${labelWidth}px`,
                height: `${labelHeight}px`,
                'text-align': textAlign,
                zIndex: ['PICTURE', 'SEAL', 'SIGNATURE'].includes(labelType) ? '10' : '11',
            };
        },
        labelDefaultStyle(label) {
            const { x, y, height } = label.labelPosition;
            const isRefuseSeal = label.type === 'CONFIRMATION_REQUEST_SEAL' && label.answerType === 'REFUSE';
            const left = isRefuseSeal ? (x + this.labelWidth / this.pageWidth) : x;
            // 新的坐标系根据文字定位，标签的左下角相对于pdf左下角的位置
            const top = 1 - y - height;
            const transformHeight = ['SEAL', 'SIGNATURE', 'CONFIRMATION_REQUEST_SEAL'].includes(label.labelType) ? '-26' : 0;
            return {
                top: `${top * 100}%`,
                left: `${left * 100}%`,
                marginTop: `${transformHeight}px`,
                fontSize: `${(label.labelExtends && label.labelExtends.pxFontSize) || 14}px`,
            };
        },
        changeZIndex(e) {
            const labelContainer = e.target.closest('.send-label-default');
            labelContainer.style.zIndex = this.cacheZIndex - 1; // 通过父级维护zindex保证每次切换层级都会变小一级
            this.$emit('updateZIndex');
        },
    },
};
</script>
<style lang="scss">
.label-container {
     .send-label-default {
        position: absolute;
        border-radius: 2px;
        z-index: 10;
        user-select: none;
        .zIndexIcon{
            position: absolute;
            top: 100%;
            right: 0;
            width: 40px;
            height: 40px;
            background: #E7F3FB;
            border-radius: 2px;
            margin-top: 4px;
            text-align: center;
            cursor: pointer;
            i {
                font-size: 24px;
                color: #127FD2;
                line-height: 40px;
            }
        }
        .send-label-name {
            text-align: left;
            width: 100%;
            border-radius: 2px;
            background-color: rgba($label-back-color, 0.8);
            margin-bottom: 2px;
            font-size: 12px;
            padding-left: 5px;
            box-sizing: border-box;
            white-space: nowrap;
            line-height: 24px;
        }
        .icon {
            width: 100%;
            height: 100%;
        }
        .send-label-wrapper {
            position: relative;
            border-radius: 2px;
            cursor: move;
            text-align: left;
            .describe-tooltip {
                visibility: hidden;
                position: absolute;
                z-index: 999;
                text-align: left;
                bottom: 22px;
                background-color: #FFF7B6 !important;
                opacity: 0.8;
                color: #333333;
                border: 1px solid #999;
                font-size: 12px;
            }
            .request-describe {
                top: -24px;
                height: 20px;
            }
            &:hover .describe-tooltip {
                visibility: visible;
            }
        }
    }
}

</style>
