<template>
    <div class="address-book__inner-member">
        <div class="address-book__outer-contact-notice">
            <span>{{ $t('addressBook.myContacts.tips') }}</span>
            <span class="invite-btn" @click="toConsole">{{ $t('addressBook.myContacts.toConsole') }}</span>
        </div>
        <DragBox :width1="memberListWidth[0]" :width2="memberListWidth[1]" :width3="memberListWidth[2]" @change="handleWidthChange">
            <template slot="item1">
                <div class="address-book__content-search search-list fl">
                    <DeptTree
                        v-if="type"
                        :type="type"
                        :entGroupId="entGroupId"
                        @getCurrentDept="handleGetCurrentDept"
                        @updateKeyword="updateKeyword"
                        ref="CompanyDeptTree"
                    ></DeptTree>
                </div>
            </template>
            <template slot="item2">
                <MemberList
                    :hide-select-all="hideSelectAll"
                    :member-list="memberList"
                    :select-type="selectType"
                    :selected-members.sync="selectedMembers"
                    :renderNameFun="userName"
                    @checkAll="handleCheckAllChange"
                />
            </template>
            <template slot="item3">
                <SelectedMembers
                    :render-name-fun="userName"
                    :selected-members="selectedMembers"
                    @cancelMemberSelect="cancelSelectedMember"
                />
            </template>
        </DragBox>
    </div>
</template>

<script>
import DeptTree from '../deptTree';
import MemberList from '../memberList';
import SelectedMembers from '../selectedMembers';
import { addressListDragMixin } from 'src/mixins/addressListDrag.js';
export default {
    components: {
        DeptTree,
        MemberList,
        SelectedMembers,
    },
    mixins: [addressListDragMixin],
    props: {
        onlyActive: {
            type: Boolean,
            default: true,
        },
        selectType: {
            type: String,
            default: 'checkBox',
        },
        hideSelectAll: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            type: '',
            entGroupId: '',
            selectedAllMembers: false,
            memberList: [],
            cacheMembers: [],
            selectedMembers: [], // 返回给父组件的选中的成员
            keyword: '',
        };
    },
    computed: {
        hasGroupConsole() {
            return this.$store.state.commonHeaderInfo.hasGroupConsole;
        },
        isSelectedAll() {
            return this.selectedMembers.length > 0 && this.selectedMembers.length < this.memberList.length;
        },
        isGroupMember() {
            return Number(this.$store.state.commonHeaderInfo.groupVersion) > 0;
        },
    },
    watch: {
        selectedMembers: 'handleChoose',
    },
    methods: {
        toConsole() {
            // 集团企业跳到集团控制台
            window.open(this.$store.state.commonHeaderInfo.hasGroupConsole ? '/console/group/account/members' : '/console/enterprise/account/members');
        },
        handleChoose() {
            this.$emit('choose', this.selectedMembers, {
                type: 'innerMember',
            });
        },
        userName(member) {
            return `${member.empName} ${member.account}`;
        },
        checkGroup() {
            // CFD-19780 判断是集团成员就获取集团所有企业的内部通讯录，不需要集团控制台权限
            if (this.isGroupMember) {
                this.$http.get('/ents/group').then(res => {
                    this.entGroupId = res.data.entGroupId;
                    this.type = 'group';
                });
            } else {
                this.type = 'company';
            }
        },
        handleGetCurrentDept(dept) {
            this.currentDept = dept;
            this.getMembers();
        },
        updateKeyword(keyword) {
            this.keyword = keyword;
            this.type === 'group' ? this.getGroupMembers() : this.getMembers();
        },
        getGroupMembers() {
            this.$http.get(`/ents/employees/search-all?searchContent=${this.keyword}`).then(res => {
                const memberList = [];
                this.cacheMembers = {};

                res.data.forEach(member => {
                    member.formatUserId = `${member.userId}${member.entName}`;
                    memberList.push(member);
                    // 通过 userId 标志缓存部门成员
                    this.cacheMembers[member.formatUserId] = member;
                });
                this.memberList = memberList;
            });
        },
        getMembers() {
            const defaultPaging = {};
            if (this.type === 'group') {
                defaultPaging.entId = this.currentDept.entId;
            }
            this.$http({
                method: 'get',
                url:
                    `/ents/employees/search?searchContent=${this.keyword}&deptId=${this.currentDept.deptId}&skipPermissionCheck=true`,
                params: defaultPaging,
            }).then(res => {
                const memberList = [];
                this.cacheMembers = {};

                res.data.forEach(member => {
                    memberList.push(member);
                    // 通过 userId 标志缓存部门成员
                    this.cacheMembers[member.userId] = member;
                });
                this.memberList = memberList;
            });
        },
        handleCheckAllChange(value) {
            // 如果全选则将 cacheMember 中的成员添加到已选成员，否则将已选成员置空
            // 并且切换全选 checkBox 的 indeterminate 状态
            if (value) {
                for (const i in this.cacheMembers) {
                    this.selectedMembers.push(this.cacheMembers[i]);
                }
            } else {
                this.selectedMembers = [];
            }
        },
        handleSelectChange(value) {
            const checkedCount = value.length;
            this.selectedAllMembers = checkedCount === this.memberList.length;
        },
        cancelSelectedMember(index) {
            this.selectedMembers.splice(index === 0 ? 0 : index, 1);
        },
    },
    created() {
        this.checkGroup();
    },
};
</script>
<style lang="scss">
.address-book__inner-member{
    height: 100%;
    .address-book__content-member {
        .select-all {
            margin-bottom: 15px;
        }
    }
}
</style>
