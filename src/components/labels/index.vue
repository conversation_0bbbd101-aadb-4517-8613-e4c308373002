<template>
    <div class="label-container">
        <div class="send-label-default" :style="defaultStyle" :class="{'hybrid-font': !!hybridServer, [label.labelId]: true}">
            <div class="send-label-name"
                v-if="['SEAL', 'SIGNATURE', 'CONFIRMATION_REQUEST_SEAL'].includes(labelType)"
            >{{ receiverName }}</div>
            <div class="send-label-wrapper"
                :style="wrapperStyle"
                @mousedown="onDragStart($event,'label')"
            >
                <span v-if="label.description && ['TEXT', 'CONFIRMATION_REQUEST_REMARK'].includes(labelType)" class="describe-tooltip" :class="['CONFIRMATION_REQUEST_REMARK'].includes(labelType) ? 'request-describe' : ''">{{ label.description }}</span>
                <!-- 印章 -->
                <template v-if="labelType=== 'SEAL'">
                    <SealLabel
                        :iconType="iconType"
                        :receiverIsJa="receiverIsJa"
                        :label="label"
                        :labelDragScale="Number((labelWidth / defaultSealLabelWidth).toFixed(2, 10))"
                        @changeSpecialSeal="changeSpecialSeal"
                    ></SealLabel>
                    <div class="copySealBtn no-drag" v-if="canCopySeal"><el-button type="primary" @click="showCopyDialog=true">{{ $t('labels.sealCopy') }}</el-button></div>
                </template>
                <!-- 签名 -->
                <SignatureLabel v-else-if="labelType === 'SIGNATURE'"
                    :iconType="iconType"
                    :color="color"
                    :labelWidth="labelWidth"
                    :label="label"
                    :receiverIsJa="receiverIsJa"
                ></SignatureLabel>
                <!-- 签署日期类型，垂直居中 -->
                <DateLabel v-else-if="labelType === 'DATE'" :labelHeight="labelHeight" :label="label"></DateLabel>
                <WriteLabel
                    v-else-if="['TEXT', 'TEXT_NUMERIC', 'NUMERIC_VALUE', 'CONFIRMATION_REQUEST_REMARK'].includes(labelType)"
                    :label="label"
                    :bgColor="color"
                ></WriteLabel>
                <!-- 单选框,复选框 -->
                <BoxLabel
                    v-else-if="['SINGLE_BOX','MULTIPLE_BOX'].includes(labelType)"
                    :buttonsStyle="buttonsElStyle"
                    :label="label"
                    :color="color"
                    :isFocus="isFocus"
                    :labelButtonInd="labelButtonInd"
                    :labelButtonStyle="labelButtonStyle"
                    @onDragStart="onDragStart"
                >
                </BoxLabel>
                <!-- 询证章 -->
                <ConfirmSealLabel
                    v-else-if="labelType ==='CONFIRMATION_REQUEST_SEAL'"
                    :buttonsStyle="buttonsElStyle"
                    :label="label"
                    :color="color"
                    :isFocus="isFocus"
                    :labelButtonInd="labelButtonInd"
                    :iconType="iconType"
                    :labelButtonStyle="labelButtonStyle"
                    @onDragStart="onDragStart"
                >
                </ConfirmSealLabel>
                <!-- 临时字段：图片类型 -->
                <PictureLabel v-else-if="labelType==='PICTURE'" :label="label"></PictureLabel>
                <!-- 业务字段日期类型 -->
                <BizDateLabel v-else-if="['BIZ_DATE', 'DATE_TIME'].includes(labelType)" :label="label"></BizDateLabel>
                <!-- 业务字段下拉框类型 -->
                <DropDownLabel v-else-if="labelType=='COMBO_BOX'" :label="label"></DropDownLabel>
                <!-- 二维码 -->
                <QrCodeLabel v-else-if="labelType=='QR_CODE' && !getIsForeignVersion" :label="label" :labelWidth="labelWidth" :labelHeight="labelHeight"></QrCodeLabel>
                <!-- 印章、图片置于底部的按钮 -->
                <CommonTip
                    effect="dark"
                    :content="$t('rejectSigner.placeTop')"
                    placement="right"
                    v-if="['PICTURE', 'SEAL', 'SIGNATURE'].includes(labelType)"
                >
                    <span slot="reference" class="zIndexIcon" @click="changeZIndex($event)">
                        <i class="el-icon-ssq-zhiyudiceng"></i>
                    </span>
                </CommonTip>
            </div>
            <!-- 标签边框 -->
            <div class="send-label-border" :style="labelBorderStyle">
            </div>
            <!-- 删除按钮 9px为icon的一半宽度-->
            <i class="label-close-icon el-icon-ssq-guanbi"
                :style="{left: `${labelWidth - 9}px`}"
                :class="{'label-close-sign':
                    ['SEAL', 'SIGNATURE'].includes(label.labelType)}"
                @click="onDelete"
                v-if="canAdd !== 'none' &&
                    !(!canSendlabelEdit && (isSenderLabel || label.appendedForDraft)) &&
                    labelType !== 'QR_CODE' &&
                    needCloseIcon &&
                    !isDataBoxPreview"
            ></i>
            <!-- 增加选择框 -->
            <div class="add-btn"
                v-if="['MULTIPLE_BOX','SINGLE_BOX'].includes(label.labelType) && canAdd !== 'none' && !(!canSendlabelEdit && isSenderLabel) && !isDataBoxPreview"
                @click="addOption"
                :style="addBtnStyle"
            >
                <svg class="icon" aria-hidden="true">
                    <image width="28" height="16" :xlink:href="addImg" style="pointer-events: none;"></image>
                </svg>
            </div>
            <!-- 合同中关键字定位的关键字 -->
            <div v-if="keyWordStyle" class="keyword" :style="keyWordStyle">{{ label.keywordPosition.keyword }}</div>
            <!-- excel表头关键字 -->
            <div v-if="excelHeadStyle" class="keyword" :style="excelHeadStyle">{{ label.excelHeadKeyword.keyword }}</div>
        </div>
        <CopySealDialog v-model="showCopyDialog" :labelId="label.labelId"></CopySealDialog>
        <el-dialog
            width="450px"
            class="del-copy-seal-dialog"
            :visible.sync="showDelCopySealdialog"
            :modal-append-to-body="true"
            :append-to-body="true"
            :title="$t('templateCommon.tip')"
        >
            <el-button
                @click="deleteLabel"
                type="primary"
            >{{ $t('labels.delCopySeal') }}</el-button>
            <el-button
                @click="deleteAllDecorate"
                type="primary"
            >{{ $t('labels.delAllCopySeal') }}</el-button>
        </el-dialog>
    </div>
</template>
<script>
import { mapState, mapMutations, mapGetters } from 'vuex';
import CopySealDialog from 'components/copySealDialog';
import { labelIconInfo, addBtnStyle, labelInfo } from 'utils/labelStyle.js';
import addImg from 'img/label/add.png';
import SealLabel from './SealLabel';
import SignatureLabel from './SingatureLabel';
import PictureLabel from './PictureLabel';
import DateLabel from './DateLabel';
import ConfirmSealLabel from './ConfirmSealLabel';
import { labelMixin } from 'mixins/label';
import WriteLabel from 'components/labels/WriteLabel';
import BoxLabel from 'components/labels/BoxLabel';
import BizDateLabel from 'components/labels/BizDateLabel';
import QrCodeLabel from 'components/labels/QrCodeLabel';
import DropDownLabel from 'components/labels/DropDownLabel';
export default {
    components: {
        SealLabel,
        SignatureLabel,
        PictureLabel,
        DateLabel,
        ConfirmSealLabel,
        WriteLabel,
        BoxLabel,
        BizDateLabel,
        QrCodeLabel,
        DropDownLabel,
        CopySealDialog,
    },
    mixins: [labelMixin],
    props: {
        canDrag: {
            type: Boolean,
        },
        canAdd: {
            type: String,
        },
        canSendlabelEdit: {
            type: Boolean,
        },
        needCloseIcon: {
            type: Boolean,
            default: true,
        },
        isBoxSelected: {
            type: Boolean,
            default: false,
        },
        canCopySeal: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            addImg,
            dragging: false, // 默认未拖拽
            optionDragging: false, // 默认未拖拽单选框/复选框 选项
            moveMetaData: {}, // 移动过程中的修改
            // 点击还是拖拽的判断
            startTime: '',
            endTime: '',
            isClick: false,
            isDataBoxPreview: this.$route.query.isDataBoxPreview === 'true', // 是否是档案+合同预览
            showCopyDialog: false,
            showDelCopySealdialog: false,
        };
    },
    computed: {
        ...mapState('template', ['receivers', 'focusLabelOpt', 'zoom']),
        ...mapGetters('template', ['currentDoc']),
        ...mapGetters(['getIsForeignVersion']),
        defaultSealLabelWidth() {
            return labelInfo('SEAL', { jaReceiver: this.receiverIsJa }).width;
        },
        ...mapState('template', {
            isUseStatus: state => state.templateStatus === 'use',
        }),
        ...mapState({
            hybridServer: state => state.commonHeaderInfo.hybridServer,
        }),
        labelReceiver() {
            return this.receivers.find(receiver => receiver.receiverId === this.label.receiverId) || {};
        },
        receiverIsJa() {
            return (!this.labelReceiver.owningPlatform && this.getIsForeignVersion) || this.labelReceiver.owningPlatform === 'ja';
        },
        // 标签内图片
        iconType() {
            return labelIconInfo(this.label.labelType).type;
        },
        // 选项卡新增按钮定位
        addBtnStyle() {
            return addBtnStyle(this.label, this.pageWidth, this.pageHeight);
        },
        // 接收方名称
        receiverName() {
            const receiver = this.receivers.find(receiver => receiver.receiverId === this.label.receiverId) || {};
            return receiver.showName || '';
        },
        focusLabel() {
            return this.focusLabelOpt.labelId && this.currentDoc.labels.find(item => item.labelId === this.focusLabelOpt.labelId);
        },
        // 是否聚焦
        isFocus() {
            // 选中字段编辑或者字段被框选时添加聚焦状态
            return (this.focusLabel && this.label.labelId === this.focusLabel.labelId) || this.isBoxSelected;
        },
        labelButtonInd() {
            return this.focusLabelOpt.labelButtonInd;
        },
        labelAddWidth() { // 增加选择按钮时，标签需增加的宽度
            return (this.labelButtonStyle.initSplit + this.labelButtonStyle.width) / this.pageWidth;
        },
        // 横向时标签宽度需增加
        labelAddHeight() {
            return (this.labelButtonStyle.split + this.labelButtonStyle.height) / this.pageHeight;
        },
        isSenderLabel() { // 是否为发件方标签
            const SingerTypes = ['SEAL', 'SIGNATURE', 'CONFIRMATION_REQUEST_SEAL', 'DATE', 'CONFIRMATION_REQUEST_REMARK'];
            return !(SingerTypes.includes(this.label.labelType) || this.label.labelExtends?.receiverFill);
        },
        // 空白文档标签展示关键字内容
        keyWordStyle() {
            const keyword = this.label.keywordPosition && this.label.keywordPosition.keyword;
            if (this.isUseStatus || !keyword) {
                return;
            }
            const { moveX, moveY } = this.label.keywordPosition;
            const width = this.getTextWidth(keyword);
            return {
                left: `${-width - moveX * this.pageWidth}px`,
                bottom: `${-moveY * this.pageHeight}px`,
                width: `${width}px`,
            };
        },
        // excel表头关键字
        excelHeadStyle() {
            const keyword = this.label.excelHeadKeyword && this.label.excelHeadKeyword.keyword;
            if (this.isUseStatus || !keyword) {
                return;
            }
            const width = this.getTextWidth(keyword);
            return {
                left: 0,
                top: `-19px`,
                width: `${width}px`,
                backgroundColor: 'rgba(255, 247, 182, 0.8)',
            };
        },
    },
    methods: {
        // 处理驳回操作
        handelRejectLabel(item) {
            this.$emit('rejectLabel', item);
        },
        // 获取文本长度
        getTextWidth(str) {
            const dom = document.createElement('span');
            dom.style.display = 'inline-block';
            dom.style.fontSize = '12px';
            dom.textContent = str;
            document.body.appendChild(dom);
            const width = dom.clientWidth;
            document.body.removeChild(dom);
            return width + 1;// +1防止因为实际宽度有小数点，clientwidth区取整导致偏差
        },
        ...mapMutations({
            setFocusLabelOpt: 'template/setFocusLabelOpt',
        }),
        // 标签拖拽
        onDragStart(event, type, index = -1) {
            const targetClassName = event.target.className;
            if (typeof targetClassName === 'string' && targetClassName.includes('no-drag')) {
                return;
            }
            if (this.isDataBoxPreview) {
                return;
            }
            if (!this.canSendlabelEdit && this.isSenderLabel) {
                return;
            }
            if (this.labelType === 'QR_CODE') { // 查验码不支持拖动
                return;
            }
            this.startTime = new Date().getTime();
            this.focusTheLabel(index);
            if (!this.canDrag) {
                return;
            }
            this.dragging = true;
            document.addEventListener('mousemove', this.onDragMove);
            document.addEventListener('mouseup', this.onDragEnd);
            if (type === 'labelButton') { // 选项按钮选择当前，并准备移动
                this.optionDragging = true;
                const item = this.label.labelExtends.items[index];
                this.move = { // 初始位置以标签左上角为坐标
                    ex: event.x,
                    ey: event.y,
                    startLeft: item.itemX,
                    startTop: this.label.labelPosition.height - item.itemY - this.labelButtonStyle.height / this.pageHeight,
                };
            } else {
                this.move = { // 初始位置以标签左上角为坐标
                    ex: event.x,
                    ey: event.y,
                };
                this.$emit('label-start', event);
            }
        },
        // 拖拽计算
        onDragMove(event) {
            if (!this.dragging) {
                return;
            }
            if (this.optionDragging) { // 选项按钮移动
                this.optionMove(event);
            } else {
                this.$emit('label-move', event);
            }
        },
        // 选项卡移动
        optionMove(event) {
            let items = this.label.labelExtends.items;
            const labelPosition = this.label.labelPosition;
            const item = items[this.labelButtonInd];
            const labelButtonStyle = this.labelButtonStyle;

            const moveX = (event.x - this.move.ex) / this.zoom / this.pageWidth;
            const moveY = (event.y - this.move.ey) / this.zoom / this.pageHeight;

            const buttonLeft = this.move.startLeft + moveX;
            let buttonTop = this.move.startTop + moveY;
            // 边界计算
            const maxLeft = 1 - labelPosition.x - this.labelAddWidth;
            const maxTop = labelPosition.height + labelPosition.y - (labelButtonStyle.initSplit + labelButtonStyle.height) / this.pageHeight;
            const minLeft = labelButtonStyle.initSplit / this.pageWidth;
            const minTop = labelButtonStyle.initSplit / this.pageHeight;

            item.itemX = buttonLeft < minLeft ? minLeft : (buttonLeft > maxLeft ? maxLeft : buttonLeft);
            buttonTop = buttonTop < minTop ? minTop : (buttonTop > maxTop ? maxTop : buttonTop);
            // 转换为左下角坐标
            item.itemY = labelPosition.height - buttonTop - labelButtonStyle.height / this.pageHeight;
            this.$set(items, this.labelButtonInd, item);

            this.moveMetaData = this.changeLabelSize(items); // 计算移动后的标签
            labelPosition.width = this.moveMetaData.labelPosition.width;
            labelPosition.height = this.moveMetaData.labelPosition.height;
            labelPosition.y = this.moveMetaData.labelPosition.y;
            items = this.moveMetaData.labelExtends.items;
        },
        // 结束拖拽
        onDragEnd(event) {
            this.endTime = new Date().getTime();
            this.isClick = this.endTime - this.startTime < 200 && (event.x - this.move.ex < 1 && event.y - this.move.ey < 1);
            if (!this.dragging) {
                return;
            }
            this.dragging = false;
            document.removeEventListener('mousemove', this.onDragMove);
            document.removeEventListener('mouseup', this.onDragEnd);
            if (this.isClick) {
                return  this.optionDragging = false;
            }
            if (this.optionDragging) {
                this.optionDragging = false;
                this.$emit('label-button-change', this.moveMetaData); // 标签数据更新
            } else {
                this.$emit('label-end', event);
            }
        },
        deleteAllDecorate() {
            this.$emit('deleteAllDecorate');
        },
        // 删除标签
        onDelete() {
            const { labelType, labelExtends } = this.label;
            const isDelButton = ['SINGLE_BOX', 'MULTIPLE_BOX'].includes(labelType) && this.labelButtonInd !== -1 && labelExtends.items.length > 1 && this.isFocus;
            if (isDelButton) { // 删除选项按钮
                const newItems = labelExtends.items;
                const deleteItem = newItems[this.labelButtonInd];
                // 单选框，删除的那项刚好是默认值项，清空默认值
                if (labelType === 'SINGLE_BOX' && labelExtends.defaultValue === deleteItem.itemValue) {
                    labelExtends.defaultValue = '';
                } else if (labelType === 'MULTIPLE_BOX') { // 复选框删除被选中项目
                    const defaultValueArry = (labelExtends.defaultValue || '').split(',');
                    const index = defaultValueArry.findIndex(item => item === deleteItem.itemValue);
                    if (index > -1) {
                        defaultValueArry.splice(index, 1);
                    }
                    labelExtends.defaultValue = defaultValueArry.join(',');
                }
                newItems.splice(this.labelButtonInd, 1);
                const newLabelButtonInd = newItems.length > this.labelButtonInd ? this.labelButtonInd : this.labelButtonInd - 1;
                this.$emit('label-button-change', this.changeLabelSize(newItems)); // 标签数据更新
                this.focusTheLabel(newLabelButtonInd);
            } else {
                if (this.label.ifDecorate) {
                    this.showDelCopySealdialog = true;
                } else {
                    this.deleteLabel();
                }
            }
        },
        deleteLabel() {
            const labelId = this.label.labelId;
            const labels = this.currentDoc.labels;
            const index = labels.findIndex(item => item.labelId === labelId);
            this.$emit('delete', index);
        },
        // 增加选项按钮
        addOption() {
            const items = this.label.labelExtends.items;
            const labelPosition = this.label.labelPosition;
            if (items.length >= 100) {
                this.$MessageToast.info(this.$t('labels.optionLimitTip'));
                return;
            }
            const labelButtonStyle = this.labelButtonStyle;

            const vCanAdd = this.labelAddHeight  < labelPosition.y;
            if (vCanAdd) { // 竖向还可以加
                const itemX = labelPosition.width - this.labelAddWidth;
                const itemY = (labelButtonStyle.initSplit - labelButtonStyle.split - labelButtonStyle.height) / this.pageHeight;
                const newItems =  Object.assign([], items);
                newItems.push({
                    itemX,
                    itemY,
                    itemValue: this.formatItemValue(),
                });
                const metaData = this.changeLabelSize(newItems);
                labelPosition.width = metaData.labelPosition.width;
                labelPosition.height = metaData.labelPosition.height;
                labelPosition.y = metaData.labelPosition.y;
                this.label.labelExtends.items = metaData.labelExtends.items;
                this.$emit('label-button-change', this.label); // 标签数据更新
                this.focusTheLabel(this.label.labelExtends.items.length - 1);
            } else {
                this.$MessageToast.info(this.$t('labels.pageLimitTip'));
            }
        },
        // 选项改变，标签大小跟着改变
        changeLabelSize(newItems) {
            const metaData = { ...this.label, labelExtends: { ...this.label.labelExtends, items: newItems } };
            const list = [].concat(newItems);
            metaData.labelPosition.width = list.sort((a, b) => b.itemX - a.itemX)[0].itemX + this.labelAddWidth;
            const itemY = list.sort((a, b) => a.itemY - b.itemY)[0].itemY;
            const movePercont = Number(((this.labelButtonStyle.initSplit) / this.pageHeight - itemY).toFixed(4));
            if (movePercont) { // y方向移动距离
                metaData.labelPosition.height = this.label.labelPosition.height + movePercont;
                metaData.labelPosition.y = Number((this.label.labelPosition.y - movePercont).toFixed(4)); // 防止为负数
                metaData.labelExtends.items = newItems.map(item => {
                    item.itemY = item.itemY + movePercont;
                    return item;
                });
            }
            return metaData;
        },
        // 聚焦选中的标签
        focusTheLabel(labelButtonInd) {
            this.setFocusLabelOpt({
                labelId: this.label.labelId,
                labelButtonInd,
            });
        },
        // 切换二维码
        onChangeQRCode() {
            this.focusTheLabel(-1);
            this.$emit('showQRCodeList');
        },
        // 选项按钮值不能相同
        formatItemValue() {
            let sameNameLabel;
            const items = this.label.labelExtends.items;
            let count = items.length;
            do {
                count++;
                sameNameLabel = items.filter(a => {
                    return a.itemValue === this.$t('labels.optionName', { count });
                });
            } while (sameNameLabel.length > 0);

            return this.$t('labels.optionName', { count });
        },
        changeZIndex(e) {
            const labelContainer = e.target.closest('.send-label-default');
            labelContainer.style.zIndex = 0;
        },
        changeSpecialSeal(sealChange) {
            this.$emit('changeSpecialSeal', sealChange);
            this.label.specialSealInfo = {
                ...this.label.specialSealInfo,
                ...sealChange,
            };
        },
    },
};
</script>
<style lang="scss">
.label-container {
    .send-label-default {
        position: absolute;
        border-radius: 2px;
        // transform-origin: left top;
        z-index: 10;
        user-select: none;
        font-family: STSong; // CFD-23457、CFD-23482：字体、行高和后端保持一致，后端用的默认行高，这里设置18保证表现效果

        .send-label-name {
            text-align: left;
            width: 100%;
            border-radius: 2px;
            background-color: rgba($label-back-color, 0.8);
            margin-bottom: 2px;
            font-size: 12px;
            padding-left: 5px;
            box-sizing: border-box;
            white-space: nowrap;
            line-height: 24px;
            min-height: 24px;
            [dir=rtl] & {
                text-align: right;
                padding-right: 5px;
                padding-left: 0;
            }
        }
        .icon {
            width: 100%;
            height: 100%;
        }
        .send-label-wrapper {
            position: relative;
            border-radius: 2px;
            cursor: move;
            text-align: left;
            //font-family: STSong; // CFD-23457: 字体导致文本展示超出字段长度
            .describe-tooltip {
                visibility: hidden;
                position: absolute;
                z-index: 999;
                text-align: left;
                bottom: 22px;
                background-color: #FFF7B6 !important;
                opacity: 0.8;
                color: $--color-text-primary;
                border: 1px solid #999;
                font-size: 12px;
            }
            .request-describe {
                top: -24px;
                height: 20px;
            }
            &:hover .describe-tooltip {
                visibility: visible;
            }
            [dir=rtl] & {
                text-align: right !important;
            }
            .copySealBtn{
                position: absolute;
                top:100%;
                left: 0;
                width: 100%;
                cursor: auto;
                .el-button{
                    margin: 4px auto;
                    display: block;
                    cursor: pointer;
                }
            }
        }
        &.hybrid-font {
            font-family: serif; // CFD-19613:模板与签约换行不一致的问题修改
            .write-label-line{
                font-size: 16px;
            }
        }
    }
    .send-label-border{
        border-width: 2px;
        position: absolute;
        left: 0;
        z-index: 0;
        top:0;
        pointer-events: none;
        border-radius: 2px;
    }
    .send-label-index {
        position: absolute;
        top: 0;
        transform: translateX(-100%);
        padding: 0 2px;
        font-size: 12px;
        color: $--color-white;
        background: $--color-primary;
        opacity: 0.8;
    }
    .label-close-icon {
        color: white;
        font-size: 12px;
        background-color: rgba(51, 51, 51, .75);
        padding: 5px;
        border-radius: 12px;
        top: -10px;
        position: absolute;
        cursor: pointer;
        z-index: 12;
        [dir=rtl] & {
            right: auto;
            left: -12px !important;
        }
    }
    .add-btn{
        width: 28px;
        height: 16px;
        position: absolute;
    }
    .keyword{
        position: absolute;
        font-size: 12px;
    }
    .label-close-sign{
        top: 14px;
    }

    .change-code-btn{
        // display: none;
        position: absolute;
        top: -24px;
        left: 0;
        width: calc(100% - 25px);
        height: 24px;
        padding-left: 25px;
        line-height: 24px;
        font-size: 12px;
        color: $base-color;
        background: rgba(201,231,255,0.75);
        border-radius: 2px;
        transition: all 0.2s;

        &:hover{
            cursor: pointer;
        }

        i{
            position: absolute;
            top: 6px;
        }

        .el-icon-ssq-genghuanchayanma{
            left: 10px;
        }

        .el-icon-ssq-chayanmajiantou{
            right: 10px;
        }
    }

    &:hover {
        .change-code-btn{
            display: block;
        }
    }
    .zIndexIcon{
        position: absolute;
        left: 100%;
        bottom: 0;
        width: 40px;
        height: 40px;
        background: #E7F3FB;
        border-radius: 2px;
        margin-left: 4px;
        text-align: center;
        cursor: pointer;
        i {
            font-size: 24px;
            color: #127FD2;
            line-height: 40px;
        }
    }
}
.del-copy-seal-dialog{
    .el-button{
        margin: 20px auto;
        display: block;
    }
}
</style>
