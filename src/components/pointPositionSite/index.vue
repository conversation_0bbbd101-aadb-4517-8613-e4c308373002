<template>
    <div class="point-position-site fl"
        id="siteContainer"
        v-if="receivers.length"
        :class="{'step2-collapse': collapseItem.step2, 'step1-collapse': collapseItem.step1}"
    >
        <template v-if="!isWordBillDoc">
            <!-- 选择签约方 -->
            <div class="site-bar-head step1-head" @click="handleCollapseItem('step1')">
                <span class="order-num" v-if="!getIsUae">{{ $t('pointPositionSite.step1') }}</span>
                <span class="head-title">{{ $t('pointPositionSite.selectSigner') }}</span>
                <span class="collapse-icon"><i
                    :class="collapseItem.step1 ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
                ></i></span>
            </div>
            <SignersList
                :receivers="receivers"
                class="step1-body"
            ></SignersList>

            <!-- 第2步拖动签署位置 -->
            <div class="site-bar-head step2-head" @click="handleCollapseItem('step2')">
                <span class="order-num" v-if="!getIsUae">{{ $t('pointPositionSite.step2') }}</span>
                <span class="head-title">{{ $t('pointPositionSite.dragSignaturePosition') }}</span>
                <span class="collapse-icon"><i
                    :class="collapseItem.step2 ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
                ></i></span>
            </div>

            <div class="site-bar-body step2-body" :style="{height: `${signIcons.length * 32 + 15 }px`}">
                <!-- 签署字段 -->
                <ul class="site-bar-content">
                    <li v-for="item in signIcons"
                        :key="item.class"
                        @mousedown="onDragStart($event, item.type, item.class, item.name,item.receiverFill, item.fontSize, item.necessary)"
                    >
                        <i :class="item.class" :style="computeStyleByIndex(receiverIndex)"></i>
                        <span>{{ item.name }}</span>
                    </li>
                </ul>
            </div>
        </template>
        <div v-if="showField || showDecorate"
            class="step3-container"
        >
            <!-- 第3步其他配置 -->
            <div class="site-bar-head">
                <span class="order-num" v-if="!isWordBillDoc && !getIsUae">{{ $t('pointPositionSite.step3') }}</span>
                <span class="head-title">{{ $t('pointPositionSite.moreConfig') }}</span>
                <span class="sub-title">{{ $t('pointPositionSite.optional') }}</span>
            </div>
            <div class="site-bar-body field-module">
                <el-tabs class="field-tab"
                    v-if="showField"
                    v-model="activeFieldTab"
                    :class="{'full-row': isManageField, 'temp-send-field': isDocUploadInSend}"
                >
                    <p v-if="isDocUploadInSend" class="field-fill-tip">
                        {{ $t('pointPositionSite.tempFieldFillTip') }}
                    </p>
                    <el-tab-pane name="temp" v-if="!isManageField">
                        <span slot="label">
                            {{ $t('pointPositionSite.tempField') }}
                            <i class="el-icon-ssq-icon-bs-bangzhuzhongxin"
                                @click.stop="dialogTempMetaDesc.show = true"
                            ></i></span>
                        <ul class="site-bar-content">
                            <template v-for="item in contentIcons">
                                <li
                                    :key="item.class"
                                    v-if="!item.notShow"
                                    @mousedown="onDragStart($event, item.type, item.class, item.name,item.receiverFill)"
                                >
                                    <i :class="item.class" :style="computeStyleByIndex(receiverIndex)"></i>
                                    <span>{{ item.name }}</span>
                                </li>
                            </template>
                        </ul>
                    </el-tab-pane>
                    <el-tab-pane name="content">
                        <span slot="label">
                            {{ $t('pointPositionSite.businessField') }}
                            <i class="el-icon-ssq-icon-bs-bangzhuzhongxin" @click.stop="dialogMetaDesc.show = true"></i>
                        </span>

                        <div class="site-bar-content metaLabel-content biz-metaLabel-content">
                            <!-- 输入需查找的业务字段名称 -->
                            <div class="opt-row">
                                <el-input :placeholder="$t('templateCommon.inputPlaceHolder')"
                                    prefix-icon="el-icon-search"
                                    size="small"
                                    class="opt-row__search"
                                    v-model="bizNameSearchValue"
                                    @change="handleChange"
                                ></el-input>
                                <template v-if="!isManageField">
                                    <!-- 添加业务字段 -->
                                    <CommonTip class="item"
                                        v-if="bizFields.length < 500"
                                        effect="dark"
                                        :content="$t('pointPositionSite.addBusinessField')"
                                        placement="top"
                                    >
                                        <el-button slot="reference"
                                            class=" opt-row__icon-btn"
                                            icon="el-icon-ssq-jia"
                                            @click="handleAddBizField"
                                        ></el-button>
                                    </CommonTip>
                                    <!-- 管理业务字段 -->
                                    <CommonTip class="item"
                                        effect="dark"
                                        :content="$t('pointPositionSite.manageBusinessField')"
                                        placement="top"
                                    >

                                        <el-button slot="reference"
                                            class=" opt-row__icon-btn"
                                            icon="el-icon-ssq-shezhi"
                                            @click="jumpToConsole"
                                        ></el-button>
                                    </CommonTip>
                                </template>
                            </div>
                            <ul class="metaLabel-content-ul">
                                <li
                                    v-for="item in visibleFilterBizFields"
                                    :key="item.fieldId"
                                    @mousedown="onDragStart($event, item.labelDataType, fieldIconMap[item.labelDataType],
                                                            item.name, item.receiverFill, item.fontSize, item.necessary, item.labelMetaId,
                                                            item.buttons, item.dateFieldFormat, item.decimalPlace, item.alignment)"
                                >
                                    <i :class="`${fieldIconMap[item.labelDataType]}`" :style="computeStyleByIndex(receiverIndex)"></i>
                                    <span
                                        v-if="item.name.length < 10"
                                        class="metaLabel-name"
                                    >
                                        {{ item.name }}
                                    </span>
                                    <CommonTip
                                        v-else
                                        class="item"
                                        effect="dark"
                                        :content="item.name"
                                        placement="bottom"
                                        popper-class="point-position-site__metaLabel-name-popper"
                                    >
                                        <span slot="reference" class="metaLabel-name">
                                            {{ item.name }}
                                        </span>
                                    </CommonTip>
                                    <!-- 按产品意思隐藏编辑操作 -->
                                    <!--                                    <span class="metaLabel-operate-block" @mousedown.prevent.stop v-if="couldManageBizField &&!isManageField">-->
                                    <!--                                        <i class="metaLabel-operate-icon el-icon-ssq-gengduo"></i>-->
                                    <!--                                        &lt;!&ndash; 编辑 &ndash;&gt;-->
                                    <!--                                        <ul class="metaLabel-hidden-operate">-->
                                    <!--                                            <li @mousedown.prevent.stop="handleEditBizField(item)">{{ $t('pointPositionSite.edit') }}</li>-->
                                    <!--                                        </ul>-->
                                    <!--                                    </span>-->
                                </li>
                                <li class="more-btn"
                                    v-show="filterBizFields.length > 10 && !isShowMoreBizFields"
                                    @click="handleShowMoreFields"
                                >
                                    <span>{{ $t('contractItem.more') }}</span>
                                </li>
                            </ul>
                        </div>
                    </el-tab-pane>
                </el-tabs>
                <template v-if="isWordBillDoc && templateStatus === 'edit'">
                    <div class="site-bar-title decorate-title">
                        {{ $t('pointPositionSite.wordBillLabelConfig') }}
                    </div>
                    <div class="site-bar-content">
                        <ul>
                            <li v-for="(item, index) in wordBillLabels"
                                :key="item.labelId"
                            >
                                <div class="edit-label-row">
                                    <span class="name">{{ item.labelName }}</span>
                                    <span class="edit-btn" @click="handleLabelEdit(item, index)">编辑</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </template>
                <template v-if="showDecorate">
                    <div class="site-bar-title decorate-title">
                        <!-- 合同装饰 -->
                        {{ $t('pointPositionSite.decorateField') }}
                        <span>{{ $t('pointPositionSite.optional') }}</span>
                    </div>
                    <div class="site-bar-content">
                        <ul>
                            <li v-for="item in decorationIcons"
                                :key="item.class"
                                v-show="item.show"
                                @click="onAddDecorator(item)"
                                @mousedown="onDragStart($event, item.type, item.class, item.name,item.receiverFill)"
                            >

                                <div>
                                    <i :class="item.class" :style="computeStyleByIndex(receiverIndex)"></i>
                                    <AdvancedTooltip
                                        :featureId="item.featureId"
                                        :showToolTip="item.show"
                                        placement="right"
                                    >
                                        <template slot="tipContent">
                                            <span>
                                                <span>{{ item.name }}</span>
                                                <CommonTip v-if="item.isShowToolTip" class="decoration-tips" effect="dark" :content="item.toolTipContent" placement="top-start" />
                                            </span>
                                        </template>
                                    </AdvancedTooltip>
                                </div>

                            </li>
                        </ul>
                    </div>
                </template>
            </div>
        </div>
        <!-- 创建、编辑业务字段 -->
        <EditBizFieldDialog
            v-if="dialogBizField.show"
            :curRow="dialogBizField.curField"
            @update="handleRefreshFields"
            @close="handleCloseBizDialog"
            :isNewGroupPath="isGroupRole"
        >
        </EditBizFieldDialog>
        <!-- 什么是临时字段？ -->
        <el-dialog class="el-dialog-bg dialog-tempMeta-desc"
            :title="$t('pointPositionSite.whatTempField')"
            :visible.sync="dialogTempMetaDesc.show"
            size="tiny"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <p>{{ $t('pointPositionSite.whatTempTip.0') }}</p>
            <p>{{ $t('pointPositionSite.whatTempTip.1') }}</p>
            <div class="dialog-tempMeta-desc-detail">
                <p>{{ $t('pointPositionSite.addContentFieldSteps.0') }}</p>
                <img src="~img/template/config-field-step-one.png" v-if="!getIsForeignVersion" class="dialog-tempMeta-desc-img" alt="">
            </div>
            <div class="dialog-tempMeta-desc-detail">
                <p>{{ $t('pointPositionSite.addContentFieldSteps.1') }}</p>
                <img src="~img/template/config-field-step-two.png" v-if="!getIsForeignVersion" class="dialog-tempMeta-desc-img" alt="">
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="dialogTempMetaDesc.show = false">{{ $t('pointPositionSite.know') }}</el-button>
            </div>
        </el-dialog>
        <!-- 业务字段的概念描述 -->
        <el-dialog class="el-dialog-bg dialog-meta-desc"
            :title="$t('pointPositionSite.whatBusinessField')"
            :visible.sync="dialogMetaDesc.show"
            size="tiny"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <p>{{ $t('pointPositionSite.whatBusinessTip.0') }}</p>
            <p>{{ $t('pointPositionSite.whatBusinessTip.1') }}</p>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="dialogMetaDesc.show = false">{{ $t('pointPositionSite.know') }}</el-button>
            </div>
        </el-dialog>

        <SelectTargetDocsDialog
            :dialog-visible.sync="showSelectTargetDocsDialog"
            :doc-list="docList"
            :select-all="currentRidingSeal.selectAll "
            :select-document-ids="currentRidingSeal.selectDocumentIds || []"
            @confirm="addRideSeal"
        ></SelectTargetDocsDialog>
    </div>
</template>
<script>
import { computeStyleByIndex, EXTEND_TYPES, TEMPERORY_TYPES } from 'utils/labelStyle.js';
import { DATE_FORMATS, TIME_FORMATS } from 'pub-consts/const';
import EditBizFieldDialog from 'pub-businessComponents/dialog/EditField.vue';
import SelectTargetDocsDialog from 'components/selectTargetDocsDialog';
import SignersList from './SignersList';
import { mapGetters, mapState, mapMutations } from 'vuex';
import { addWaterLabel, addRideSealIntoTargetDocs, getValidBizFields, getTemplateBizFields, normalizeBizFieldOpened } from 'src/api/template/pointPosition.js';
import { factoryWatermarkText, DEFAULT_RS_HEIGHT, calcRideSeal } from 'utils/decorate.js';
import { advancedFeatureMixin } from 'pub-mixins/advancedFeature.js';
import AdvancedTooltip from 'pub-businessComponents/advanceTooltip/index.vue';
import GroupSignMTipImg from 'img/template/group-sign-m.png';
import CommonSignMTipImg from 'img/template/common-sign-m.png';
import { getGroupSignManagePrivillage } from '@/api/template';

export default {
    components: {
        EditBizFieldDialog,
        SignersList,
        SelectTargetDocsDialog,
        AdvancedTooltip,
    },
    mixins: [advancedFeatureMixin],
    props: {
        hasAttachment: {
            type: Boolean,
            default: false,
        },
        canAdd: {
            type: String,
        },
    },
    data() {
        return {
            collapseItem: {
                step1: false,
                step2: false,
            },
            activeFieldTab: '',
            showSelectTargetDocsDialog: false, // 选择骑缝章文档弹窗
            bizFields: [],
            curBizField: {},
            dialogTempMetaDesc: {
                show: false,
            },
            dialogMetaDesc: {
                show: false,
            },
            dialogBizField: {
                show: false,
                curField: null,
            },
            bizNameSearchValue: '', // 搜索业务字段的输入内容
            filterBizFields: [], // 搜索过滤后的业务字段数组
            fieldIconMap: {
                'DATE_TIME': 'el-icon-ssq-dengdaitarenqianshu',
                'BIZ_DATE': 'el-icon-ssq-riqi',
                'TEXT': 'el-icon-ssq-wenben',
                'SINGLE_BOX': 'el-icon-ssq-danxuananniu',
                'MULTIPLE_BOX': 'el-icon-ssq-fuxuankuang',
                'COMBO_BOX': 'el-icon-ssq-xialakuang',
                'NUMERIC_VALUE': 'el-icon-ssq-shuzi',
            },
            computeStyleByIndex,
            isShowMoreBizFields: false,
            isGroupRoleHasBusiManagePrivillage: false, // 集团：是否是有业务管理权限的集团角色
        };
    },
    computed: {
        ...mapGetters(['getUserPermissons', 'checkFeat', 'isGroupRole', 'getIsForeignVersion', 'getIsUae']),
        ...mapState(['commonHeaderInfo']),
        ...mapState('template', ['receivers', 'receiverIndex', 'watermarkList', 'docList', 'ridingSealList',
                                 'currentDocIndex', 'blankDocument', 'isManageField', 'fda', 'templatePermissions',
                                 'internalSignInfoVO', 'ifCrossPlatform', 'templateStatus', 'isBatchSend']),
        isDocUploadInSend() {
            // 是否是使用阶段新上传的文档
            return this.docList[this.currentDocIndex].appendedForDraft;
        },
        wordBillLabels() {
            return this.docList[this.currentDocIndex].labels.filter(label => label.wordLabel &&
                !['DYNAMIC_TABLE', 'QR_CODE'].includes(label.labelType));
        },
        visibleFilterBizFields() {
            const data = [...this.filterBizFields];
            return this.isShowMoreBizFields ? this.filterBizFields : data.splice(0, 10);
        },
        signatureName() {
            const { signType } = this.receivers[this.receiverIndex];
            let name;
            // 签名/盖章人签字/经办人签字
            switch (signType) {
                case 'SIGNATURE': name = this.$t('pointPositionSite.signature'); break;
                case 'SEAL_AND_SIGNATURE': name = this.$t('pointPositionSite.entSignature'); break;
                case 'ENTERPRISE_SIGNATURE': name = this.$t('pointPositionSite.operatorSignature'); break;
                case 'SCAN_CODE_SIGNATURE': name = this.$t('pointPositionSite.scanSignature'); break;
            }
            return name;
        },
        signIcons() {
            const { signType } = this.receivers[this.receiverIndex];
            return [
                {
                    class: 'el-icon-ssq-gongzhang',
                    type: 'SEAL',
                    name: this.$t('pointPositionSite.seal'), // 盖章
                    show: ['SEAL_AND_SIGNATURE', 'SEAL'].includes(signType),
                },
                {
                    class: 'el-icon-ssq-bi',
                    type: 'SIGNATURE',
                    name: this.signatureName,
                    show: ['SEAL_AND_SIGNATURE', 'SIGNATURE', 'ENTERPRISE_SIGNATURE', 'SCAN_CODE_SIGNATURE'].includes(signType),
                },
                {
                    class: 'el-icon-ssq-xunzhang',
                    type: 'CONFIRMATION_REQUEST_SEAL',
                    name: this.$t('pointPositionSite.confirmSeal'), // 询证盖章,
                    show: signType === 'CONFIRMATION_REQUEST_SEAL',
                },
                {
                    class: 'el-icon-ssq-beizhu',
                    type: 'CONFIRMATION_REQUEST_REMARK',
                    name: this.$t('pointPositionSite.confirmRemark'), // 备注,
                    show: signType === 'CONFIRMATION_REQUEST_SEAL',
                    receiverFill: true,
                    necessary: false,
                },
                {
                    class: 'el-icon-ssq-riqi',
                    type: 'DATE',
                    name: this.$t('pointPositionSite.signDate'), // 签署日期,
                    show: true,
                },
                {
                    class: 'el-icon-ssq-wenben',
                    type: 'TEXT',
                    name: this.$t('pointPositionSite.innerSignComment'), // '文本', 对内场景的批注
                    show: this.internalSignInfoVO.isInternalSignTemplate,
                    necessary: false,
                    receiverFill: true,
                },
            ].filter(a => a.show);
        },
        contentIcons() {
            return [
                {
                    class: 'el-icon-ssq-wenben',
                    type: 'TEXT',
                    name: this.$t('pointPositionSite.text'), // '文本',
                    receiverFill: false,
                },
                {
                    class: 'el-icon-ssq-danxuananniu',
                    type: 'SINGLE_BOX',
                    name: this.$t('pointPositionSite.singleBox'), // '单选框',
                    receiverFill: false,
                    notShow: this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha(), // 混合云1.0不支持单选框，复选框
                },
                {
                    class: 'el-icon-ssq-fuxuankuang',
                    type: 'MULTIPLE_BOX',
                    name: this.$t('pointPositionSite.multipleBox'), // '复选框',
                    receiverFill: false,
                    notShow: this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha(),
                },
                {
                    class: 'el-icon-ssq-xialakuang',
                    type: 'COMBO_BOX',
                    name: this.$t('pointPositionSite.comboBox'), // '下拉框',
                    receiverFill: false,
                    notShow: this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha(),
                },
                {
                    class: 'el-icon-ssq-riqi',
                    type: 'BIZ_DATE',
                    name: this.$t('pointPositionSite.bizDate'), // '日期',
                    receiverFill: false,
                },
                {
                    class: 'el-icon-ssq-shuzi',
                    type: 'NUMERIC_VALUE',
                    name: this.$t('pointPositionSite.number'), // '数字',
                    receiverFill: false,
                    notShow: !this.commonHeaderInfo.openNewContractTemplate,
                },
                {
                    class: 'el-icon-ssq-dengdaitarenqianshu',
                    type: 'DATE_TIME',
                    name: this.$t('localCommon.datetime'), // '时刻',
                    receiverFill: true,
                    notShow: this.getIsUae,
                },
            ];
        },
        decorationIcons() {
            const { signType, userType } = this.receivers[this.receiverIndex];
            return [
                {
                    class: 'el-icon-ssq-shuiyin', // 水印
                    type: 'WATERMARK',
                    name: this.$t('pointPositionSite.watermark'),
                    show: this.canAdd === 'all',
                    notDrag: true,
                    isShowToolTips: false, // 是否展示文字提示
                    toolTipContent: '',
                    featureId: '68',
                }, {
                    class: 'el-icon-ssq-qifengzhang', // 骑缝章
                    type: 'DECORATE_RIDING_SEAL',
                    name: this.$t('pointPositionSite.decorataRidingSeal'),
                    show: userType === 'ENTERPRISE' && signType !== 'ENTERPRISE_SIGNATURE',
                    notDrag: true,
                    isShowToolTip: false,
                    toolTipContent: this.$t('pointPositionSite.ridingSealTip'),
                    featureId: '68',
                },
                {
                    class: 'el-icon-ssq-tuchuzhanweifu', // 图片
                    type: 'PICTURE',
                    name: this.$t('pointPositionSite.picture'),
                    show: !(this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha()) && this.canAdd === 'all' &&
                        !(this.templateStatus === 'edit' && this.isWordBillDoc),
                    // 混合云1.0 不展示 临时业务字段-图片类型 入口
                    notDrag: false,
                    isShowToolTips: false,
                    toolTipContent: '',
                    featureId: '',
                },
            ];
        },
        // 非编辑阶段的个人不展示合同装饰,JJJJ项目不展示合同装饰
        showDecorate() {
            return (this.canAdd === 'all' || (this.templatePermissions.modifySignLabel &&
                this.receivers[this.receiverIndex].userType !== 'PERSON')) && !this.ifCrossPlatform;
        },
        isWordBillDoc() {
            return this.docList[this.currentDocIndex].documentType === 4;
        },
        // 跨平台合同不展示业务字段
        showField() {
            return (this.canAdd === 'all' || (this.canAdd === 'some' && this.isDocUploadInSend && !this.getIsUae)) &&
                !this.ifCrossPlatform && !(this.templateStatus === 'edit' && this.isWordBillDoc);
        },
        // 是否有管理业务字段权限
        couldManageBizField() {
            // CFD-21405：按产品意思移除多余的权限控制
            return this.getUserPermissons.sign_m || this.isGroupRoleHasBusiManagePrivillage;
        },
        toConsolePath() {
            const toGroupConsole = Number(this.commonHeaderInfo.groupVersion) === 2 &&
                this.commonHeaderInfo.hasGroupConsole;
            return toGroupConsole ? '/console/group/contract/bizField' : '/console/enterprise/contract/bizField';
        },
        currentRidingSeal() {
            const DEFAULT_DOCS_CONFIG = {
                selectDocumentIds: this.docList.map(a => a.documentId),
                selectAll: true,
            };
            return this.ridingSealList.find(item => item.receiverId ===
                this.receivers[this.receiverIndex]?.receiverId) || DEFAULT_DOCS_CONFIG;
        },
    },
    watch: {
        isManageField: {
            handler(v) {
                this.activeFieldTab = !v ? 'temp' : 'content';
            },
            immediate: true,
        },
    },
    methods: {
        ...mapMutations({
            setReceiverIndex: 'template/setReceiverIndex',
            setIsManageField: 'template/setIsManageField',
            setFocusLabelOpt: 'template/setFocusLabelOpt',
        }),
        handleShowMoreFields() {
            document.getElementById('siteContainer').scrollTop = 0; // 全折叠前，先将滚动条调整到顶部
            this.isShowMoreBizFields = true;
            this.collapseItem.step1 = true;
            this.collapseItem.step2 = true;
        },
        handleCollapseItem(key) {
            // 如果展示更多的情况下的全折叠状态
            if (this.isShowMoreBizFields && this.collapseItem.step1 && this.collapseItem.step2) {
                this.collapseItem.step1 = false;
                this.collapseItem.step2 = false;
            } else {
                this.collapseItem[key] = !this.collapseItem[key];
            }
        },
        handleChange() { // 业务字段搜索
            if (!this.bizNameSearchValue.trim()) {
                this.filterBizFields = this.bizFields;
            }
            this.filterBizFields = this.bizFields.filter(item => {
                return item.bizName.toLowerCase().includes(this.bizNameSearchValue);
            });
        },
        onDragStart(e, type, className, name, receiverFill, fontSize, necessary, labelMetaId, items = null, dateFieldFormat = '', decimalPlace = 0, alignment = 'left') {
            const filterDecorationIcons = this.decorationIcons.filter(item => item.type === type);
            // 骑缝章、水印 去掉拖拽
            if (filterDecorationIcons.length && filterDecorationIcons[0].notDrag) {
                return false;
            }
            if (EXTEND_TYPES.includes(type)) {
                let labelExtends = {};
                if (type === 'DATE_TIME') {
                    labelExtends = { dateFieldFormat: dateFieldFormat || TIME_FORMATS[0].value };
                } else if (['BIZ_DATE', 'DATE'].includes(type)) { // 日期类型内容字段要传样式参数 SAAS-14898
                    labelExtends = { dateFieldFormat: dateFieldFormat ||  DATE_FORMATS[this.getIsUae ? 1 : 0].value };
                } else if (['NUMERIC_VALUE'].includes(type)) {
                    labelExtends = { decimalPlace };
                } else if (['TEXT'].includes(type)) {
                    labelExtends = { alignment };
                    // 批注添加属性标识
                    if (name === this.$t('pointPositionSite.innerSignComment')) {
                        labelExtends = {
                            alignment,
                            subType: 'innerSign',
                        };
                        name = `${this.$t('pointPositionSite.innerSignComment')}（${this.receivers[this.receiverIndex].roleName}）`;
                    }
                } else if (this.fda.supportFDA && type === 'SIGNATURE') { // 添加FDA
                    labelExtends = this.fda;
                }
                if (TEMPERORY_TYPES.includes(type)) { // 标记使用阶段添加的临时字段，并且默认签署方填写，uae只支持发件方填写字段
                    labelExtends.appendedForDraft = this.templateStatus === 'use' ? true : undefined;
                    receiverFill = this.templateStatus === 'use' ? true : (!this.getIsUae && receiverFill);
                }

                if (this.getIsUae) {
                    labelExtends.alignment = 'right'; // uae默认右对齐
                }
                this.$emit('site-drag-start', e, type, className, name, receiverFill, fontSize, necessary,
                           labelMetaId, items, labelExtends);
            } else {
                this.$emit('site-drag-start', e, type, className, name);
            }
        },
        onAddDecorator(item) {
            const { type, featureId } = item;
            if (!this.checkFeatureCanUseInfo(featureId, false)) {
                // 高级功能未开启，提示
                return;
            }
            if (type === 'WATERMARK') {
                return this.addWatermark();
            }
            if (type === 'DECORATE_RIDING_SEAL') {
                this.showSelectTargetDocsDialog = true;
            }
        },
        // 新增或者编辑水印
        addWatermark() {
            const { receiverId, showName } = this.receivers[this.receiverIndex] || {};
            const watermarkList = this.watermarkList;
            if (watermarkList.find(item => item.receiverId === receiverId)) {
                return;
            }
            addWaterLabel({ receiverId }).then((res) => {
                watermarkList.push({
                    ...res.data,
                    watermarkText: factoryWatermarkText(this.receivers[this.receiverIndex]),
                    showName,
                    receiverId,
                });
            });
        },
        addRideSeal({ selectAll = true, selectDocumentIds = [] }) {
            const currentDecorateId = this.currentRidingSeal.decorateId;
            // 新增时如果空选，则什么都不做
            if (!currentDecorateId && !selectAll && !selectDocumentIds.length) {
                this.showSelectTargetDocsDialog = false;
                return;
            }
            const { receiverId, showName, roleName } = this.receivers[this.receiverIndex] || {};
            const pageInfo = this.docList[0].documentPages[0];
            const params = currentDecorateId ? {
                ...this.currentRidingSeal,
                selectAll,
                selectDocumentIds,
            } : {
                decorateId: '',
                receiverId,
                y: calcRideSeal(this.ridingSealList, pageInfo.height),
                height: DEFAULT_RS_HEIGHT / pageInfo.height,
                width: DEFAULT_RS_HEIGHT / pageInfo.width,
                selectAll,
                selectDocumentIds,
            };
            const loading = this.$loading();
            addRideSealIntoTargetDocs(params).then((res) => {
                // 新增的直接push，编辑则直接改值
                if (!currentDecorateId) {
                    this.ridingSealList.push({
                        ...res.data,
                        showName,
                        roleName,
                        selectAll,
                        selectDocumentIds,
                    });
                } else {
                    const currentRidingSealIndex = this.ridingSealList.findIndex(a => a.decorateId ===
                        currentDecorateId);
                    this.ridingSealList[currentRidingSealIndex].selectAll = selectAll;
                    this.ridingSealList[currentRidingSealIndex].selectDocumentIds = selectDocumentIds;
                }
                this.showSelectTargetDocsDialog = false;
            }).finally(() => {
                loading.close();
            });
        },
        jumpToConsole() {
            if (this.couldManageBizField) {
                // 如果是集团但是没有集团控制台权限，直接提示
                if (Number(this.commonHeaderInfo.groupVersion) && !this.commonHeaderInfo.hasGroupConsole) {
                    this.manageFieldAuthTip('group_edit');
                    return;
                }
                window.open(this.toConsolePath);
            } else if (!Number(this.commonHeaderInfo.groupVersion)) { // 普通企业无权限，弹窗提示
                this.manageFieldAuthTip('normal_edit');
            } else {
                this.manageFieldAuthTip('group_edit');
            }
        },
        manageFieldAuthTip(type) {
            const h = this.$createElement;
            let content = '';
            if (type === 'group_add') {
                content = [
                    h('p', {
                        attrs: {
                            class: 'tip-text',
                        },
                    }, this.$t('pointPositionSite.groupSignMJumpTip')),
                    h('p', {
                        attrs: {
                            class: 'tip-text',
                        },
                    }, this.$t('pointPositionSite.groupSignMJumpDesc')),
                    h('p', {
                        attrs: {
                            class: 'tip-desc',
                        },
                    }, this.$t('pointPositionSite.groupSignMJumpOpt1')),
                    h('img', {
                        attrs: {
                            src: GroupSignMTipImg,
                            class: 'tip-img',
                        } }),
                    h('p', {
                        attrs: {
                            class: 'tip-desc',
                        },
                    }, this.$t('pointPositionSite.groupSignMJumpOpt2')),
                    h('img', {
                        attrs: {
                            src: CommonSignMTipImg,
                            class: 'tip-img',
                        } }),
                ];
            } else {
                const TYPE_MAP = {
                    'normal_add': {
                        imgSrc: CommonSignMTipImg,
                        text: this.$t('pointPositionSite.CommonSignMOpenTip'),
                    },
                    'normal_edit': {
                        imgSrc: CommonSignMTipImg,
                        text: this.$t('pointPositionSite.CommonSignMJumTip'),
                    },
                    'group_edit': {
                        imgSrc: GroupSignMTipImg,
                        text: this.$t('pointPositionSite.groupSignMOpenTip'),
                    },
                };
                content = [
                    h('p', {
                        attrs: {
                            class: 'tip-text',
                        },
                    }, TYPE_MAP[type].text),
                    h('img', {
                        attrs: {
                            src: TYPE_MAP[type].imgSrc,
                            class: 'tip-img',
                        } }),
                ];
            }
            const message = h('div', null, content);
            this.$confirm(message, this.$t('templateCommon.tip'), {
                showCancelButton: false,
                confirmButtonText: this.$t('templateCommon.understand'),
                customClass: `field-manage-tip-dialog ${this.getIsForeignVersion ? 'ja-dialog' : ''}`, // 日文板不展示图片
            });
        },
        // 显示添加业务字段弹窗
        handleAddBizField() {
            if (!this.couldManageBizField) {
                this.manageFieldAuthTip(!Number(this.commonHeaderInfo.groupVersion) ? 'normal_add' : 'group_add');
                return;
            }
            this.dialogBizField.curField = {
                operateType: 'new',
            };
            this.dialogBizField.show = true;
        },
        // 显示编辑业务字段弹窗
        handleEditBizField(item) {
            // 编辑或删除之前首先查询是否曾经应用过模板  SAAS-5292 remove limit
            // this.$http.get(`/template-api/bizfield/is-applied/${item.fieldId}`).then(({ data }) => {
            // const hasApplied = data.result;
            this.dialogBizField.curField = Object.assign({}, item, {
                operateType: 'edit',
                // hasApplied,
            });
            this.dialogBizField.show = true;
            // });
        },
        // 刷新业务字段
        async handleRefreshFields() {
            let fields = [];
            try {
                if (this.isManageField) {
                    fields = (await getTemplateBizFields()).data.contentFields;
                } else {
                    fields =  (await getValidBizFields()).data.result;
                }
                this.mappingFieldToMata(fields);
            } catch (error) {
                console.log(error);
            }
        },
        handleLabelEdit(item) {
            this.setFocusLabelOpt({
                labelId: item.labelId,
                labelButtonInd: 0,
            });
        },
        // 关闭业务字段弹窗
        handleCloseBizDialog(status) {
            this.dialogBizField.show = false;

            if (status === true) {
                this.handleRefreshFields();
            }
        },
        // 映射业务字段值为自定义标签值，保持原来代码逻辑不动
        mappingFieldToMata(data) {
            if (!data.length) {
                return;
            }

            // 初始化 bizFields数据
            data.forEach(item => {
                item.labelMetaId = item.fieldId;
                item.labelDataType = item.bizFieldType;
                item.name = item.bizName;
                item.receiverFill = item.writeBy !== 'SENDER';
            });
            this.bizFields = [];
            this.bizFields = this.bizFields.concat(data);
            // 业务字段初始化或者更新之后，触发一下搜索事件，重新获取当前搜索的结果
            this.handleChange();
        },
    },
    async created() {
        // <!-- 编辑模板的时候，展示签署字段外的其他字段 -->
        // <!-- 使用模板的时候不展示 -->
        if (this.canAdd === 'all') {
            const isManageField = (await normalizeBizFieldOpened()).data;
            this.setIsManageField(isManageField);
            this.handleRefreshFields();
            if (this.commonHeaderInfo.hasGroupConsole) {
                getGroupSignManagePrivillage().then(({ data }) => {
                    this.isGroupRoleHasBusiManagePrivillage = (data.privileges || []).some(a => a.name ===
                        'SIGN_M');
                    console.log('sss ', data.privileges, this.isGroupRoleHasBusiManagePrivillage);
                });
            }
        }
    },
};
</script>
<style lang="scss">
    .field-manage-tip-dialog {
        width: 500px;
        .tip-text {
            line-height:22px;
            margin-bottom: 10px;
        }
        .tip-desc {
            color: $--color-text-secondary;
            height: 30px;
            line-height: 30px;
            font-size: 14px;
        }
        .tip-img {
            width: 440px;
        }
        &.ja-dialog .tip-img {
            display: none;
        }
    }
    .en-page{
        .point-position-site .site-bar-head{
            line-height: 18px;
            height: auto;
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }
    .point-position-site {
        width: 210px;
        border-right: 1px solid $--border-color-light;
        font-size: 12px;
        font-weight: normal;
        color: $--color-text-primary;
        height: 100%;
        overflow: auto;
        user-select: none;
        [dir=rtl] & {
            float: right;
            border-right: none;
            border-left: 1px solid $--border-color-light;
        }
        .site-bar-head {
            width: 210px;
            height: 38px;
            line-height: 36px;
            font-size: 16px;
            color: $--color-text-primary;
            padding-left: 15px;
            border-bottom: 1px solid $--border-color-lighter;
            background: $--color-white;
            box-sizing: border-box;
            [dir=rtl] & {
                padding-right: 15px;
                display: flex;
                width: 100%;
                justify-content: space-between;
            }
            span {
                vertical-align: middle;
            }
            &:first-child {
                // margin-top: 1px;
            }
            .order-num {
                font-size: 16px;
                color: $theme-color;
            }
            .sub-title {
                color: $--color-text-placeholder;
            }
            .collapse-icon {
                float: right;
                font-size: 14px;
                margin-right: 10px;
                cursor: pointer;
            }
        }

        .step1-body {
            transition: all ease 0.2s;
        }
        .step2-body {
            transition: all ease 0.3s;
        }

        .field-tab {
            &.temp-send-field {
                .el-tabs__nav {
                    display: none;
                }
                .field-fill-tip {
                    padding-left: 15px;
                    font-size: 14px;
                }
            }
            .el-tabs__header {
                margin-bottom: 5px;
                background-color: $--color-white;
                .el-tabs__nav {
                    width: 100%;
                }
                .el-tabs__item {
                    padding: 0 10px;
                    width: 50%;
                    text-align: center;
                    outline: none;
                    i {
                        font-size: 12px;
                        float: right;
                        margin: 14px 0 0;
                    }
                }
            }
            .el-tab-pane {
                height: 100%;
                box-sizing: border-box;
                padding-bottom: 5px;
            }
            .el-tabs__content {
                .biz-metaLabel-content {
                    overflow-y: auto;
                    .more-btn {
                        text-align: center;
                    }
                }
            }
            &.full-row {
                .el-tabs__active-bar {
                    display: none;
                }
                .el-tabs__item{
                    width: 100%;
                }
            }
        }
        &.step1-collapse {
            .step1-body {
                height: 0px;
                padding: 0;
                border-bottom: none;
            }

        }
        &.step2-collapse {
            .step2-body {
                height: 0 !important;
                overflow: hidden;
            }
        }
        &.step1-collapse.step2-collapse {
            overflow: hidden; // 全折叠时固定步骤菜单
            .step3-container {
                height: inherit;
                .field-module {
                    height: inherit;
                    .field-tab {
                        height: calc(100% - 270px);
                        .el-tabs__content {
                            height: calc(100% - 45px);
                            .biz-metaLabel-content {
                                height: 100%;
                                padding-bottom: 10px;
                            }
                        }
                    }
                }
            }

        }
        .decorate-title {
            background: $--color-white;
        }
        .opt-row {
            padding: 0 10px;
            [dir=rtl] & {
                display: flex;
                align-items: center;
            }
            &__search {
                width: 120px;
                .el-input__inner {
                    padding-left: 20px;
                    padding-right: 10px;
                }
                .el-input__prefix {
                    left: 0px;
                }

                [dir=rtl] & {
                    .el-input__inner {
                        padding-right: 20px;
                        padding-left: 10px;
                    }
                    .el-input__prefix {
                        left: auto;
                        right: 0px;
                    }
                }
            }
            &__icon-btn {
                margin-left: 3px;
                padding: 5px;
                width: 27px;
                height: 27px;
                border-radius: 4px;
                border: 1px solid $--border-color-light;
                [dir=rtl] & {
                    margin-left: 0;
                    margin-right: 3px;
                }
            }
        }
        // 字段
        // .site-bar-body {
            // height: calc(100% - 238px);
            // overflow: auto;
        // }
        .site-bar-title {
            height: 38px;
            line-height: 38px;
            font-size: 14px;
            padding-left: 15px;
            [dir=rtl] & {
                padding-right: 15px;
                padding-left: 0;
            }
            .el-icon-ssq-icon-bs-bangzhuzhongxin{
                font-size: 12px;
                color: $--color-text-regular;
                cursor: pointer;
            }
            &.decorate-title span {
                float: right;
                margin-right: 10px;
                font-size: 12px;
                color: $--color-text-secondary;
                [dir=rtl] & {
                    float: left;
                    margin-left: 10px;
                    margin-right: 0;
                }
            }
        }
        .site-bar-content {
            padding-top: 5px;
            padding-bottom: 5px;
            //border-bottom: 1px solid $border-color;
            li {
                display: block;
                height: 32px;
                line-height: 32px;
                white-space: nowrap;
                position: relative;
                &:hover {
                    background: $--color-primary-light-9;
                    cursor: pointer;
                    .metaLabel-operate-block {
                        display: inline-block;
                    }
                }
                i {
                    width: 22px;
                    height: 22px;
                    line-height: 22px;
                    text-align: center;
                    font-size: 16px;
                    border-radius: 3px;
                    margin-top: 4px;
                    margin-left: 15px;
                    margin-right: 10px;
                }
                .decoration-tips{
                    margin-left: 0;
                    font-size: 12px;
                }
                .metaLabel-name {
                    display: inline-block;
                    width: 125px;
                    overflow: hidden;
                    vertical-align: top;
                    text-overflow: ellipsis;
                }

                .metaLabel-operate-block{
                    right: 0;
                    position: absolute;
                    display: none;
                    margin-right: 12px;

                    &:hover{
                        .metaLabel-hidden-operate{
                            display: block;
                        }

                        .metaLabel-operate-icon{
                            color: $--color-primary-light-1;
                        }
                    }

                    .metaLabel-operate-icon{
                        margin-left: 0;
                        margin-right: 0;
                        background-color: transparent;
                    }

                    .metaLabel-hidden-operate{
                        display: none;
                        position: absolute;
                        left: -30px;
                        top: 29px;
                        background: $--color-white;
                        border: 1px solid $--border-color-light;
                        text-align: center;
                        box-shadow: 0px 2px 3px 1px $--border-color-light;
                        z-index: 1;

                        li{
                            width: 60px;
                            height: 36px;
                            line-height: 36px;
                            color: $--color-text-primary;
                            text-align: center;
                            font-size: 12px;

                            &:hover{
                                background: $--color-primary-light-9;
                            }
                        }
                    }
                }
                .edit-label-row {
                    padding: 0px 15px;
                    display: flex;
                    justify-content: space-between;
                    cursor: auto;
                    .name {
                        width: 125px;
                        overflow: hidden;
                        vertical-align: top;
                        text-overflow: ellipsis;
                    }
                    .edit-btn {
                        cursor: pointer;
                    }
                }
            }
        }
        .metaLabel {
            position: relative;
            .add-metaLabel {
                position: absolute;
                top: 12px;
                right: 42px;
                font-size: 14px;
                color: $--color-text-regular;
                cursor: pointer;

                &:hover{
                    color: $theme-color;
                }
            }

            .toManageLink{
                display: inline-block;
                position: absolute;
                top: 1px;
                right: 15px;
                outline: none;

                .el-icon-ssq-shezhi{
                    color: $--color-text-regular;
                    font-size: 16px;

                    &:hover{
                        color: $theme-color;
                    }
                }
            }

            .metaUploadBtn{
                margin-left: 10px;
                cursor: pointer;
                font-size: 12px;
                font-weight: normal;
                color: $theme-color;
            }
        }

        .site-bar-bizname-search {
            margin: 0 10px 10px 10px;
        }

        .box-sizing-dialog{
            .dialog-tempMeta-desc .el-dialog, .dialog-meta-desc .el-dialog{
                width: 655px;
                .el-dialog__body{
                    p{
                        color: $--color-text-primary;
                        line-height: 30px;
                    }
                }
            }
        }
    }
    .point-position-site__metaLabel-name-popper {
        width: 125px;
        line-height: 1.5;
    }
    .dialog-tempMeta-desc {
        .dialog-tempMeta-desc-detail {
            margin: 10px 0;
            p {
                font-size: 12px;
                color: $--color-text-secondary;
                margin-bottom: 5px;
            }
            img {
                width: 100%;
            }
        }
    }
    .el-icon-ssq-biaoqiannew{
        font-size: 15px !important;
        margin-left: 10px;
        color: $--color-danger;
    }
</style>
