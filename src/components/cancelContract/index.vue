<template>
    <div>
        <el-dialog
            v-loading="isLoading"
            :title="params.dialogTitle"
            :visible.sync="rejectReasonDialog"
            size="tiny"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleCancelClose"
            class="ssq-form sign-el-dialog reject-reason-dialog"
            append-to-body
        >
            <p>{{ $t('cancelContract.selectRejectReason') }}</p>
            <el-checkbox-group v-model="rejectCheckbox">
                <el-checkbox
                    v-for="item in rejectReasonList"
                    :label="item.id"
                    :key="item.id"
                    :disabled="item.reasonType === 6 && item.checkFlag"
                >
                    {{ item.reasonType !==6 ? item.refuseReason : (isOtherReasonMust ? $tc('cancelContract.refuseReasonOther',2) : $tc('cancelContract.refuseReasonOther',1)) }}
                </el-checkbox>
            </el-checkbox-group>
            <div class="reject-reason-dialog-input" v-if="otherReason.length!==0">
                <el-input
                    :maxlength="cancel.refuseMaxLength"
                    type="textarea"
                    :rows="4"
                    v-model="cancel.reason"
                    :placeholder="otherReason[0].refuseReason"
                >
                    <el-i-delete slot="icon"></el-i-delete>
                </el-input>
                <div class="refuse-description">{{ cancel.refuseMaxLength - cancel.reason.length }}</div>
            </div>
            <div class="confirm-sign-error" v-if="isMust && cancel.reason === ''">{{ $t('cancelContract.reasonWriteTip') }}</div>
            <el-button class="confirm-sign-btn" type="primary" @click="handleRejectNext">{{ $t('cancelContract.reject') }}</el-button>
        </el-dialog>
        <!-- 撤回合同对话框 -->
        <el-dialog
            :title="params.dialogTitle"
            :visible.sync="rejectConfirmDialog"
            size="tiny"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleCancelClose"
            class="el-dialog-bg doc-dialog-cancelContracts"
            append-to-body
        >
            <template v-if="!isRejectOpt">
                <div class="dialog-cancelContracts-hiddenForget errorTip">
                    <i class="el-icon-ssq-yijuqian"></i>
                    <p>
                        {{ $t('cancelContract.pwdWrong') }}
                        <router-link to="">{{ $t('cancelContract.forgetPwd') }}</router-link>
                    </p>
                </div>
                <div class="dialog-cancelContracts-hiddenFind errorTip">
                    <i class="el-icon-ssq-yijuqian"></i>
                    <p>
                        {{ $t('cancelContract.pwdLocked') }}
                        <router-link to="">{{ $t('cancelContract.retrievePwd') }}</router-link>
                        <br />
                        {{ $t('cancelContract.unlock') }}
                    </p>
                </div>
                <el-form :model="cancel" :rules="cancel.rule" label-position="right" label-width="100px">
                    <el-form-item label="" label-width="10px" v-if="params.refuseType !== 'REJECT'">
                        <el-radio-group v-model="selectedCancelReason">
                            <el-radio :label="item.key" v-for="item in cancelReason" :key="item.key">{{ item.name }}</el-radio>
                        </el-radio-group>

                        <el-input v-model="cancel.reason"
                            :maxlength="cancel.refuseMaxLength"
                            type="textarea"
                            auto-complete="off"
                            size="small"
                            :placeholder="$t('cancelContract.inputReason', {type: params.dialogTitle})"
                            :disabled="selectedCancelReason!=='other'"
                        ></el-input>
                        <div class="refuse-description">
                            <span>{{ cancel.refuseMaxLength - cancel.reason.length }}</span>
                            <p v-if="params.dialogTitle === $t('cancelContract.reject')">{{ $t('cancelContract.rejectTips') }}</p>
                            <div v-else class="cancel-tips">
                                <p>{{ $tc('cancelContract.revokeTips', 1) }}</p>
                                <p>{{ $tc('cancelContract.revokeTips', 2) }}</p>
                            </div>
                            <hr class="refuse-hr" v-if="params.refuseType !== 'REVOKE_CANCEL'">
                        </div></el-form-item>
                    <template v-if="SIGN_SECOND_CHECK === true">
                        <el-form-item :label="$t('cancelContract.signPwd')" prop="signPass">
                            <el-input v-model="cancel.signPass"
                                data-err-type="bottom"
                                v-va:cancel.signpass
                                :placeholder="$t('cancelContract.inputPwd6')"
                                :maxlength="6"
                                size="small"
                                type="password"
                                auto-complete="new-password"
                                name="cancel_signPass"
                            ></el-input>
                        </el-form-item>
                        <p class="tr">
                            <span class="highlight" @click="handleForgetSignPass">{{ $t('cancelContract.forgetPwd') }}</span>
                        </p>
                    </template>
                    <!--                    <template v-if="!isRevokeOpt">-->
                    <!--                        <el-form-item class="countdown-item" :label="sendType.type == 'E' ? $t('cancelContract.mail') : $t('cancelContract.phone')">-->
                    <!--                            <span class="phone">{{ notice }}</span>-->
                    <!--                        </el-form-item>-->
                    <!--                        <el-form-item :label="sendType.type == 'E' ? $t('cancelContract.mailVerify') : $t('cancelContract.verify')">-->
                    <!--                            <div class="verify-flex">-->
                    <!--                                <el-input-->
                    <!--                                    class="verify-input"-->
                    <!--                                    v-model="cancel.verifyCode"-->
                    <!--                                    :maxlength="6"-->
                    <!--                                    v-va:cancel.phoneVerifyCode-->
                    <!--                                    data-err-type="bottom"-->
                    <!--                                >-->
                    <!--                                </el-input>-->
                    <!--                                <CountDown class="countDown" :clickedFn="handleClickSendCode" :disabled="countDownDisabled" ref="btn" :second="60"></CountDown>-->
                    <!--                            </div>-->
                    <!--                        </el-form-item>-->
                    <!--                        <div class="switch">-->
                    <!--                            <span v-if="phoneNotice" class="voiceCodeLabel">{{ $t('cancelContract.otherNotice.1') }}</span>-->
                    <!--                            <span v-if="phoneNotice" class="highlight" @click="handleClickVoice">{{ $t('cancelContract.otherNotice.2') }}</span>-->
                    <!--                            <span v-if="mailNotice && phoneNotice">-->
                    <!--                                {{ $t('cancelContract.otherNotice.3') }}-->
                    <!--                                <span v-show="phoneNotice && sendType.type == 'E'" class="highlight" @click="handleClickMailAndPhone">{{ $t('cancelContract.otherNotice.4') }}</span>-->
                    <!--                                <span v-show="mailNotice && (sendType.type == 'S' || sendType.type == 'V') " class="highlight" @click="handleClickMailAndPhone">{{ $t('cancelContract.otherNotice.5') }}</span>-->
                    <!--                            </span>-->
                    <!--                        </div>-->
                    <!--                    </template>-->
                </el-form>
            </template>
            <div v-else>
                <p>{{ $t('cancelContract.refuseConfirmTip', {reason: reasonText}) }}</p>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="handleCancelContract"
                    :loading="signing"
                >{{ $t('cancelContract.confirm') }}</el-button>
                <el-button
                    v-if="isRejectOpt"
                    type="default"
                    @click="handleCancelClose"
                >{{ $t('cancelContract.waitAndThink') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// import CountDown from 'pub-components/countDown/index.vue';
import { mapActions, mapGetters } from 'vuex';
import elIDelete from 'pub-components/elIconfont/elIDelete/index.vue';
import openPageAfterAjax from 'pub-utils/linkOpener.js';
import { customErrorReport } from 'src/utils/sentryErrorReport';

export default {
    components: {
        // CountDown,
        elIDelete,
    },
    props: {
        params: { type: Object, default: () => {} },
        routerPrefix: { type: String },
    },
    data() {
        return {
            cancel: {
                show: true,
                signPass: '',
                reason: '',
                rule: {
                    signPass: [
                        { message: this.$t('cancelContract.inputPwd'), trigger: 'blur' },
                        { min: 6, max: 6, message: this.$t('cancelContract.inputNumber6'), trigger: 'blur' },
                    ],
                },
                refuseMaxLength: 100,
                verifyCode: '',
            },
            // sendType: {
            //     type: 'S',
            // }, // S：手机短信；E：邮件；V：语音
            notice: '',
            // phoneNotice: '',
            // mailNotice: '',
            // countDownDisabled: false,
            // verifyKey: '',
            signing: false,
            // isErrorShow: false,
            // errorMsg: '',
            forgetPassShow: false,
            rejectReasonList: [],
            rejectCheckbox: [],
            rejectReasonDialog: false,
            rejectConfirmDialog: false,
            otherReason: [],
            isMust: false,
            cancelReason: [{ name: this.$tc('cancelContract.revokeReasons', 0), key: 'content' }, { name: this.$tc('cancelContract.revokeReasons', 1), key: 'account'  }, { name: this.$tc('cancelContract.revokeReasons', 2), key: 'noNeed' }, { name: this.$tc('cancelContract.revokeOtherReason'), key: 'other' }],
            selectedCancelReason: 'other',
            isLoading: 0,

        };
    },
    computed: {
        ...mapGetters([
            'getUserAccount',
            'SIGN_SECOND_CHECK',
        ]),
        isRejectOpt() {
            return this.params.refuseType === 'REJECT';
        },
        isRevokeOpt() {
            return this.params.refuseType === 'REVOKE_CANCEL';
        },
        isOtherReasonMust() {
            const otherReasonMap = this.otherReason[0];
            if (otherReasonMap && otherReasonMap.checkFlag && otherReasonMap.reasonType === 6) {
                return true;
            }
            return false;
        },
        reasonText() {
            const splitStr = this.$i18n.locale === 'zh' ? '；' : ';';
            return this.rejectReasonResultList.map(a => a.refuseReason).join(splitStr);
        },
        rejectReasonResultList() {
            const list = this.rejectReasonList.filter(item => this.rejectCheckbox.includes(item.id)).map(item => {
                return {
                    reasonType: item.reasonType,
                    refuseReason: item.reasonType === 6 ? this.cancel.reason : item.refuseReason,
                };
            });
            return list;
        },
    },
    watch: {
        'cancel.show': {
            handler(val) {
                if (!val) {
                    return false;
                }
                // 如果是拒签，首先弹出拒签理由
                if (this.params.refuseType === 'REJECT') {
                    this.isMust = false;
                    return this.rejectReasonDialog = true;
                }
                return this.rejectConfirmDialog = true;
            },
            immediate: true,
        },
    },
    methods: {
        ...mapActions('doc', ['batchOperateContracts']),
        // send() {
        //     this.countDownDisabled = true;
        //     setTimeout(this.sended, 0);
        //     // sendVerCode
        //     this.$http.sendVerCode({ // 不传target，
        //         code: 'B008',
        //         sendType: this.sendType.type,
        //         bizTargetKey: this.$route.query.contractId, // contractId
        //     })
        //         .then((res) => {
        //             this.verifyKey = res.data.value;
        //             this.$MessageToast.success(this.$t('cancelContract.sendSucc'));
        //         })
        //         .catch(() => {
        //             this.$refs.btn.reset();
        //         });
        // },
        // sended() {
        //     this.$refs.btn.run();
        //     this.countDownDisabled = false;
        // },
        // handleClickSendCode() {
        //     this.send();
        // },
        // handleClickVoice() {
        //     if (this.$refs.btn.time > 0) {
        //         this.$MessageToast.error(this.$t('cancelContract.sendInternalErr'));
        //         return;
        //     }
        //     this.sendType = { type: 'V' };
        //     this.notice = this.phoneNotice.code;
        // },
        // handleClickMailAndPhone() {
        //     if (this.$refs.btn.time > 0) {
        //         this.$MessageToast.error(this.$t('cancelContract.sendInternalErr'));
        //         return;
        //     }
        //     if (this.sendType.type === 'E') {
        //         this.sendType = { type: 'S' };
        //         this.notice = this.phoneNotice.code;
        //     } else {
        //         this.sendType = { type: 'E' };
        //         this.notice = this.mailNotice.code;
        //     }
        // },
        handleForgetSignPass() {
            window.localStorage && window.localStorage.setItem('ForgetPassReturnSignHref', '/' +  this.$router.options.base + this.$route.fullPath);
            this.$router.push(`${this.GLOBAL.rootPathName}/resetSignPassword?userAccount=${this.getUserAccount}`);
        },
        getRefuseReason() {
            return this.isRevokeOpt ? (this.selectedCancelReason === 'other' ? this.cancel.reason : this.cancelReason.filter(item => item.key === this.selectedCancelReason)[0].name) : (this.rejectReasonResultList.map(e => e.refuseReason).join(','));
        },
        async handleCancelContract() {
            // 拒签不需要校验
            if (!this.isRejectOpt && (!this.isRevokeOpt || this.SIGN_SECOND_CHECK)) { // v-va指令存在时进行校验
                const formValid = this.$validation.submitValidate('cancel', this.cancel, this);
                if (!formValid) {
                    return false;
                }
            }

            if (!this.isRejectOpt && !this.verifyKey && !this.isRevokeOpt) { // 撤回无验证码校验
                return this.$MessageToast.error(this.$t('cancelContract.getVerifyCode'));
            }

            const res = await this.$hybrid.offlineTip({ operate: this.params.dialogTitle });
            if (!res) {
                return;
            }
            const refuseReason = this.getRefuseReason();
            const reasonType = this.params.refuseType !== 'REJECT' ? 0 : '6'; // 拒绝原因类型
            this.signing = true;
            // 批量撤回
            if (this.params.selectedType) {
                const { selectedType } = this.params;
                const batchOperateParams = {
                    isAll: selectedType === 'all', // 是否全量
                    batchOperatorType: 'REVOKE', // 批量操作类型
                    contractIds: this.params.contractIds,
                    // operateData
                    refuseReason,
                    refuseType: this.params.refuseType,
                    reasonType,
                    verifyCode: this.cancel.verifyCode,
                    verifyKey: this.verifyKey,
                    verifyWay: this.notice,
                    signPlatform: 'WEB',
                };
                this.batchOperateContracts(batchOperateParams)
                    .then((response) => {
                        this.handleRefresh();
                        if (response.data && response.data.code === '0') {
                            this.params.sendOperateHttpPoint && this.params.sendOperateHttpPoint(response, '批量撤回', true, { refuse_reason: this.getRefuseReason() });
                            openPageAfterAjax('/sign-flow/doc-manage/batch-log');
                        } else {
                            this.params.sendOperateHttpPoint && this.params.sendOperateHttpPoint(response, '批量撤回', false, { refuse_reason: this.getRefuseReason() });
                            // sentry 日志上报
                            customErrorReport({
                                errName: '全量撤回失败',
                                data: {
                                    batchOperateParams,
                                    errResponse: res.data,
                                },
                            });
                        }
                    })
                    .catch((e) => {
                        console.error(e);
                        this.params.sendOperateHttpPoint && this.params.sendOperateHttpPoint(e, '批量撤回', false, { refuse_reason: this.getRefuseReason() });
                    })
                    .finally(() => {
                        this.signing = false;
                        this.handleCancelClose();
                    });
            } else {
                const dataParams = this.isRejectOpt ? {
                    refuseType: this.params.refuseType,
                    refuseReason,
                    reasonType,
                    refuseReasons: this.rejectReasonResultList,
                } : {
                    refuseType: this.params.refuseType,
                    signPass: this.cancel.signPass,
                    refuseReason,
                    verifyCode: this.cancel.verifyCode,
                    verifyKey: this.verifyKey,
                    verifyWay: this.notice,
                    reasonType,
                    refuseReasons: this.rejectReasonResultList,
                };
                // 单份撤回
                this.$http.put(`/contract-api/contracts/${this.params.selectedLine.contractId}/break`, dataParams).then((res) => {
                    this.params.sendOperateHttpPoint && this.params.sendOperateHttpPoint(res, this.params.dialogTitle, true, { refuse_reason: this.getRefuseReason() });
                    this.signing = false;
                    this.$MessageToast.success({
                        message: this.$t('cancelContract.succ', { type: this.params.dialogTitle }),
                        iconClass: 'el-icon-ssq-qianyuewancheng',
                    });
                    this.handleRefresh();
                    this.handleCancelClose();
                })
                    .catch((err) => {
                        this.params.sendOperateHttpPoint && this.params.sendOperateHttpPoint(err, this.params.dialogTitle, false, { refuse_reason: this.getRefuseReason() });
                        this.signing = false;
                    });
            }
        },
        handleRefresh() {
            this.$emit('refreshTable');
        },
        // 关闭取消合同弹窗
        handleCancelClose() {
            this.$emit('close');
        },
        handleRejectNext() {
            if (this.rejectCheckbox.length <= 0) {
                return this.$MessageToast.error(this.$t('cancelContract.selectRejectReason'));
            }
            const isSelectOtherReason = this.rejectReasonList.filter(item => this.rejectCheckbox.includes(item.id)).some(e => e.reasonType === 6);
            if (this.otherReason[0].checkFlag && isSelectOtherReason && this.cancel.reason === '') {
                this.isMust = true;
            } else {
                this.rejectReasonDialog = false;
                this.rejectConfirmDialog = true;
            }
        },
    },
    created() {
        if (this.params.refuseType === 'REJECT') {
            this.isLoading = 1;
            this.$http.get(`/contract-api/contracts/refuse-reasons?contractId=${this.params.selectedLine.contractId}`).then(res => {
                // this.$http.get('/ents/refuse-reasons').then(res => {
                this.rejectReasonList = res.data;
                this.rejectReasonList.forEach(item => {
                    if (item.reasonType === 6) {
                        this.otherReason.push(item);
                        if (item.checkFlag) {
                            this.rejectCheckbox = [item.id];
                        }
                    }
                });
            }).finally(() => {
                this.isLoading = 0;
            });
        }
    },
    // beforeMount() {
    //     this.$http.get('/users/notifications')
    //         .then(res => {
    //             this.mailNotice = res.data.filter(item => {
    //                 return Number(item.type) === 1;
    //             })[0] || '';
    //             this.phoneNotice = res.data.filter(item => {
    //                 return Number(item.type) === 2;
    //             })[0] || '';
    //             this.notice = this.phoneNotice ? this.phoneNotice.code : this.mailNotice.code;
    //             this.sendType = this.phoneNotice ? { type: 'S' } : { type: 'E' };
    //         })
    //         .then(() => {
    //             this.$watch('sendType', function() {
    //                 if (this.$refs.btn.time === 0) {
    //                     this.send();
    //                 }
    //             });
    //         });
    // },
};
</script>

<style lang="scss">
@import './index.scss';
</style>
