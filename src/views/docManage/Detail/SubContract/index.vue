<template>
    <Layout pageModule="docDtl" class="need-base-width-page">
        <div class="doc-detail-container">
            <div class="doc-detail-content">
                <!--  合同基本信息,所有人都可以见 -->
                <BaseInfo
                    :contractBaseInfo="contractBaseInfo"
                    :buttonDisplayRule="buttonDisplayRule"
                    :ssoDocDtl="ssoDocDtl"
                    :source="source"
                    :canMove="false"
                    :canVerifyCode="false"
                    contractType="subContract"
                >
                </BaseInfo>
                <!-- 合同收件方 -->
                <Receivers
                    :source="source"
                    :contractBaseInfo="contractBaseInfo"
                    :displayAutoSign="displayAutoSign"
                    :contractId="contractId"
                    :ifSender="!!(subContractDetails || []).length"
                    @showOrder="handleShowSignOrder"
                    @popShowChange="popShowChange"
                >
                </Receivers>
                <DetailInfo
                    v-if="subContractDetails.length"
                    title=""
                    :contractBaseInfo="contractBaseInfo"
                    :subContractItem="subContractDetails[0]"
                    @updateContractDetailForSender="updateContractDetailForSender($event,index)"
                    @popShowChange="popShowChange"
                    :canEdit="false"
                    :canViewDetail="false"
                >
                </DetailInfo>
                <!-- 关联合同 -->
                <LinkContracts v-if="!contractBaseInfo.ifMultiPlatformContract" :linkedContractId="contractId" :linkedSubContractId="subContractId"></LinkContracts>
            </div>
            <!-- 合同缩略图 -->
            <MiniDoc
                :documents="contractBaseInfo.documents"
                :hybridServer="commonHeaderInfo.hybridServer"
                :hybridAccessToken="commonHeaderInfo.hybridAccessToken"
                :contractTitle="contractBaseInfo.contractTitle"
                @showPreview="handlePreview"
            ></MiniDoc>
            <!-- 签署顺序 -->
            <SignOrder
                v-if="contractBaseInfo.signers && contractBaseInfo.signers.length"
                :visible.sync="signOrderDialog.visible"
                :recipients="contractBaseInfo.signers"
                :ordered="contractBaseInfo.signOrdered"
                :sender="contractBaseInfo.sender"
                from="detail"
                ref="signOrderDialog"
            ></SignOrder>
            <!-- 私信，合同附属资料图片预览 -->
            <Gallery @close="previewImgInfo.visible = false" :visible="previewImgInfo.visible" :title="previewImgInfo.fileName" :src="previewImgInfo.previewUrl"></Gallery>
            <!-- 右侧弹框 -->
            <SlidePop v-show="popShow.show" :outsideShow.sync="popShow.show">
                <SlideModelContent
                    v-if="popShow.show"
                    slot="slide-pop-content"
                    :type="popShow.type"
                    :params="popShow.params"
                    @openPrivateLetterImg="openPrivateLetterImg"
                    @previewAttachment="previewAttachment"
                    @downloadAttachment="handleDownloadAttachment"
                    @updatePrivateMessage="updatePrivateMessage"
                >
                </SlideModelContent>
            </SlidePop>
            <Preview ref="preview"></Preview>
            <DownloadAttachmentDialog :visible.sync="downloadAttachmentDialogVisible" @confirm="downloadAttachment" :fileType="fileType"></DownloadAttachmentDialog>
        </div>
    </Layout>
</template>
<script>
import Layout from 'components/layout';
import { mapGetters, mapState } from 'vuex';
import { needToAuthInterceptPage } from 'src/utils/pageIntercept.js';
import checkContractViewNeedLogin from 'src/utils/checkContractViewNeedLogin.js';
import { docDetailCheckHybridOnline } from 'src/utils/hybridBusiness.js';
import MiniDoc from 'src/views/docManage/Detail/MinDoc/index.vue';
import BaseInfo from 'src/views/docManage/Detail/BaseInfo/index.vue';
import Receivers from 'src/views/docManage/Detail/Receivers/index.vue';
import DetailInfo from 'src/views/docManage/Detail/DetailInfo/index.vue';
import Preview from 'components/preview/index.vue';
import { getSubContract } from 'src/api/docManage/detail.js';

import { docComponentMixin } from 'src/mixins/docComponent.js';
import LinkContracts from 'components/linkContracts/index.vue';
import { getSafeBoxPermission } from 'src/api/contract';
import { joinPathnameAndQueryObj } from 'pub-utils/getQueryString';
export default {
    components: {
        Layout,
        BaseInfo,
        Receivers,
        MiniDoc,
        // OperationLogs,
        // PersonOeprationLog,
        Preview,
        DetailInfo,
        LinkContracts,
    },
    mixins: [docComponentMixin],
    data() {
        return {
            contractId: this.$route.params.contractId,
            subContractId: this.$route.params.subContractId,
            contractBaseInfo: {},
            buttonDisplayRule: {},
            entInternalDetails: [],
            personClaimDetails: [],
            source: '',
            subContractDetails: [],
            ssoDocDtl: {},
            curDialogParams: {},
            curDialog: '',
            loading: true,
            displayAutoSign: false,
        };
    },
    computed: {
        ...mapState({
            commonHeaderInfo: state => state.commonHeaderInfo,
        }),
        ...mapGetters([
            'getSsoConfig',
            'getHybridUserType',
        ]),
    },
    provide() {
        return {
            handleShowDialog: this.handleShowDialog,
        };
    },
    methods: {
        getContractInfo() {
            this.$loading();
            this.loading = true;
            return getSubContract(this.subContractId)
                .then(({ data: res }) => {
                    this.$loading().close();
                    if (res.needDoubleCheckLogin && checkContractViewNeedLogin(res?.viewVerificationCodeCheck)) {
                        location.replace(joinPathnameAndQueryObj(`${location.origin}/account-center/lg-supplement`, {
                            from: 'contract-view',
                            redirect: location.href,
                            account: this.commonHeaderInfo.platformUser.account,
                            smsOnly: res?.viewVerificationCodeCheck,
                        }));
                        return;
                    }
                    const contractInfo = res;
                    this.displayAutoSign = contractInfo.displayAutoSign || false;
                    this.$hybrid.decideContractEnv(contractInfo.contractBaseInfo.systemType, contractInfo.contractBaseInfo.deltaHybridVersion, this.contractId)
                        .then(() => {
                            this.contractBaseInfo = contractInfo.contractBaseInfo;
                            this.buttonDisplayRule = contractInfo.buttonDisplayRule;
                            this.subContractDetails = contractInfo.subContractDetails || [];
                            this.entInternalDetails = contractInfo.entInternalDetails || [];
                            this.personClaimDetails = contractInfo.personClaimDetails || [];
                            this.source = contractInfo.source;
                            this.loading = false;
                            setTimeout(() => {
                                // 进入其他混合云合同时，轮询检测所在内网的网络环境
                                // 在路由退出时清除定时器
                                this.originOnlineInterval = docDetailCheckHybridOnline(this.getHybridUserType, this.commonHeaderInfo.hybridServer);
                            }, 200);
                        });
                })
                .catch(err => {
                    this.loading = false;
                    this.$loading().close();
                    if (err.response.code === '130515') {
                        setTimeout(() => {
                            this.$router.push('/doc-manage/list');
                        }, 2500);
                    }
                });
        },
        handlePreview(doc) {
            const previewUrls = (doc.page || []).map(item => {
                return item.highQualityPreviewUrl;
            });
            this.$refs.preview.open(doc.fileName, previewUrls);
        },
        // 显示对话框
        async handleShowDialog() {
            // 子合同暂时没有
        },
        updateContractDetailForSender(params) {
            this.$set(this.subContractDetails[0], params.updateParam, params.newValue);
        },
        async init() {
            const isPageIntercept = await needToAuthInterceptPage(this.contractId);
            if (isPageIntercept) {
                return;
            }
            const { data: { data: ifHasSafeBoxPermission } } = await getSafeBoxPermission(this.contractId);
            if (!ifHasSafeBoxPermission) {
                return this.$MessageToast.info(this.$t('safeBox.noSafeBoxPermission')).then(() => {
                    this.$router.push('/doc-manage/list');
                });
            }
            this.ssoDocDtl = this.getSsoConfig.docDtl || {};
            this.getContractInfo();
        },
    },
    // 导航离开该组件
    beforeRouteLeave(to, from, next) {
        clearTimeout(this.originOnlineInterval);
        next();
    },
    beforeMount() {
        this.init();
    },
};
</script>
<style lang="scss">
.doc-detail-container{
        position: relative;
        @include base-width;
        margin: 0 auto;
        height: 100%;
        background: #F6F6F6;

        .doc-detail-content{
            box-sizing: border-box;
            position: relative;
            width: auto;
            margin-right: 182px;
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 16px 30px 0 30px;
            background: $--color-white;
            box-shadow: -6px 0px 6px -6px $--border-color-light;

            h2{
                .fl{
                    font-size: 16px;
                    color: $--color-text-primary;
                    font-weight: bold;
                }
            }

            .doc-detail-recipients,.doc-detail-interior,.doc-detail-detailInfo{
                margin-top: 10px;
            }
        }
    }

</style>
