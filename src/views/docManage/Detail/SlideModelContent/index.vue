<template>
    <div class="slide-model-content">
        <h1 class="slide-pop-head">
            {{ title }}
        </h1>
        <div class="slide-pop-body">
            <template v-if="type === 'private'">
                <div class="private-pop">
                    <p v-if="params.privateLetter && params.privateLetter.updateTime">{{ $t('docDetail.slideContentTip.updateTime') }}<CommonTip class="help-tip-icon">
                        <div slot="content">
                            <p>{{ $t('docDetail.slideContentTip.updateTimeTip') }}</p>
                        </div>
                    </CommonTip>：{{ formate(params.privateLetter.updateTime) }}</p>
                    <div class="slide-pop-text" v-if="params.privateLetter">
                        <span class="label"><!-- 内容 -->{{ $t("docDetail.slideContentTip.content") }}：</span>
                        <p class="text">{{ params.privateLetter.content }}</p>
                    </div>
                    <!-- 私信文件 -->
                    <div
                        v-for="(item, i) in (params.privateLetter && params.privateLetter.privateLetterFileList) || []"
                        :key="i"
                        class="slide-pop-pic"
                    >
                        <span class="private-operate-name">{{ item.fileName }}</span>
                        <div class="private-operate fr">
                            <span
                                v-if="item.canPreview"
                                @click="openPrivateLetterImg(item)"
                            >{{ $t('docDetail.slideContentTip.look') }}
                            </span>
                            <span
                                v-if="item.canDownload"
                                @click="downloadPrivateLetterImg(item)"
                            >{{ $t('docDetail.slideContentTip.downloadFile') }}
                            </span>
                        </div>
                    </div>
                    <template v-if="params.privateLetter && params.privateLetter.canUpdate">
                        <el-button v-if="!showPrivateMessage" @click="showPrivateMessage=true" class="update-btn" type="primary">{{ $t('docDetail.slideContentTip.updatePrivateBtn') }}</el-button>
                        <template v-if="showPrivateMessage">
                            <PrivateMessage
                                ref="privateMessage"
                                class="private-message-wrap"
                                :maxContentLength="255"
                                :titleVisible="false"
                                :saveBtnVisible="true"
                                :receiverId="params.receiverId"
                                :communicateInfo="communicateInfo"
                                @change="change"
                                type="update"
                            ></PrivateMessage>
                            <p class="btn-line"><el-button type="primary" @click="updatePrivateMessage">{{ $t('docDialog._confirm') }}</el-button><el-button @click="showPrivateMessage=false">{{ $t('docDialog._cancel') }}</el-button></p>
                        </template>
                    </template>
                </div>
            </template>
            <template v-if="type==='attachment'">
                <div
                    class="private-pop-pic"
                    v-for="(attachment, index) in params.attachmentList"
                    :key="index"
                >
                    <span class="label">{{ $t("docDetail.attachment") }}{{ index + 1 }}：</span><br />
                    <span class="text" v-if="isDownloadView(attachment)" @click="handleAttachDownload(attachment.fileLists[0])">{{ attachment.name }}</span>
                    <a
                        class="text"
                        v-else
                        href="javascript:;"
                        @click="previewAttachment(attachment)"
                    >{{ attachment.name }}</a>
                </div>
            </template>
            <!-- 补充协议 -->
            <template v-if="type==='supplements'">
                <div class="supplements-pop">
                    <div v-for="(item,index) in supplementsList" :key="index">
                        <p>{{ item.title }}</p>
                        <template v-if="item.displayableContractList.length">
                            <div v-for="(subItem,subIndex) in item.displayableContractList" :key="subIndex" class="supplements-block">
                                <el-row>
                                    <el-col :span="8">{{ $t('docDetail.slideContentTip.supplementsContractId') }}</el-col>
                                    <el-col :span="16">{{ subItem.contractId }}</el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">{{ $t('docDetail.slideContentTip.supplementsContractTitle') }}</el-col>
                                    <el-col :span="16">{{ subItem.contractTitle }}</el-col>
                                </el-row>
                            </div>
                            <span v-if="item.shouldDisplayMoreButton" class="look-more" @click="lookMore(item)">{{ $t('docDetail.slideContentTip.moreSupplementsLine') }}</span>
                        </template>
                        <div class="empty-block" v-else>
                            <img src="~img/doc/empty.png" width="200" height="100" alt="">
                            暂无内容
                        </div>

                    </div>
                </div>
            </template>
            <!-- 按照"公司内部编号"快速查找 -->
            <template v-if="type==='customNumber'">
                <CustomContractIdFiltered
                    :custom-number="params.customNumber"
                    :contract-id="params.contractId"
                    :sender-ent-id="params.senderEntId"
                ></CustomContractIdFiltered>
            </template>
        </div>
    </div>
</template>
<script>
import dayjs from 'dayjs';
import CustomContractIdFiltered from './CustomContractIdFiltered';
import linkOpener from 'pub-utils/linkOpener';
import { getSupplementsLinkTemplateId } from 'src/api/docManage/detail';
import { invokeDownloadHelper } from 'src/utils/download.js';
import { mapState } from 'vuex';
import PrivateMessage from 'components/privateMessage';
export default {
    components: {
        CustomContractIdFiltered,
        PrivateMessage,
    },
    props: {
        params: Object,
        type: String,
        templateId: String,
        contractId: String,
    },
    data() {
        return {
            showPrivateMessage: false,
            communicateInfo: {},
        };
    },
    computed: {
        ...mapState('doc', ['currentSupplementBelongTo']),
        isDownloadView() { // 是否是下载下来预览（文件格式是图片格式=>在线预览）
            return (item) => {
                const { fileName: attachmentName } = item.fileLists[0];
                const arr = attachmentName.split('.');
                const attachmentType =  arr[arr.length - 1];
                return item.fileLists && item.fileLists[0] && item.fileLists[0].downloadUrl && !['jpg', 'png', 'jpeg'].includes(attachmentType.toLowerCase());
            };
        },
        title() {
            const titleMap = {
                'private': this.$t('docDetail.slideContentTip.signNotice'),
                'attachment': this.$t('docDetail.slideContentTip.contractAncillaryInformation'),
                'supplements': this.$t('docDetail.slideContentTip.supplementsTitle', { account: this.params.participantName }),
                'customNumber': this.$t('docDetail.viewContractOfSameCode'),
            };
            return titleMap[this.type];
        },
        supplementsList() {
            return [
                {
                    title: this.$t('docDetail.slideContentTip.fromCurrentSupplements'),
                    ...this.params.supplements.ownDisplayableTemplate,
                },
                {
                    title: this.$t('docDetail.slideContentTip.fromOtherSupplements'),
                    ...this.params.supplements.displayableTemplateOtherTemplate,
                },
            ];
        },
    },
    methods: {
        formate(val) {
            return dayjs(val).format('YYYY-MM-DD HH:mm:ss');
        },
        change(data) {
            this.communicateInfo = {
                ...this.communicateInfo,
                privateLetter: data.privateLetter,
                signInstructionDocumentInfo: data.signInstructionDocumentInfo,
                signInstructionOriginDocumentInfo: data.signInstructionOriginDocumentInfo,
                signInstructionZipInfo: data.signInstructionZipInfo,
            };
        },
        updatePrivateMessage() {
            this.$http.post(`/contract-api/signer-instruction/${this.contractId}/${this.params.receiverId}/update`, this.communicateInfo).then(({ data }) => {
                this.$emit('updatePrivateMessage', data, this.params.receiverId);
                this.params.privateLetter = data;
                this.showPrivateMessage = false;
            });
        },
        async lookMore(currentItem) {
            try {
                const linkTemplateRes = await getSupplementsLinkTemplateId(this.templateId);
                const templateIds = [];// 存储当前合同的模板id 和 关联的模板 id
                const associateTemplate = linkTemplateRes.data.associateTemplate;
                // 其他模板
                if (currentItem.title ===  '来自其他模板的补充协议：') {
                    // 在其他模板补充协议中剔除当前模板id
                    templateIds.push(...(associateTemplate.associatedTemplateList || []).filter(item => item.templateId !== associateTemplate.currentTemplate.templateId).map(item => item.templateId));
                } else {
                    templateIds.push(associateTemplate.currentTemplate.templateId);
                }
                localStorage.setItem('supplementTemplateIds', JSON.stringify(templateIds));
                linkOpener(`/sign-flow/doc-manage/list?fromPurpose=supplementSearch&belongTo=${encodeURIComponent(JSON.stringify(this.currentSupplementBelongTo))}`);
            } catch (e) {
                console.error(e);
            }
        },
        openPrivateLetterImg(item) {
            this.$emit('openPrivateLetterImg', item);
        },
        downloadPrivateLetterImg(item) {
            invokeDownloadHelper(`/contract-api/contracts/detail/attachment/${this.contractId}/${this.params.receiverId}/download/${item.fileId}`);
        },
        previewAttachment(item) {
            this.$emit('previewAttachment', item);
        },
        handleAttachDownload({ downloadUrl, fileName: attachmentName }) {
            const arr = attachmentName.split('.');
            const attachmentType = '.' + arr[arr.length - 1];
            this.$emit('downloadAttachment', attachmentType, downloadUrl);
        },
        downloadCompressedFile(fileId) {
            const downloadUrl = `/contract-api/contracts/instructions-appendix/${this.contractId}/download/${fileId}`;
            invokeDownloadHelper(downloadUrl, false);
        },
    },
    mounted() {
        if (this.type === 'private' && this.params?.privateLetter?.canUpdate) {
            this.$http.get(`/contract-api/signer-instruction/${this.contractId}/${this.params.receiverId}/get-update-config`).then(({ data }) => {
                this.communicateInfo = data.communicateInfo;
            });
        }
    },
};
</script>
<style lang="scss">
.slide-model-content {
  .slide-pop-body {
    bottom: 40px !important;
    padding: 20px;
    box-sizing: border-box;
    .private-pop{
        .update-btn{
            margin: 10px auto;
            display: block;
        }
        .file-list{
            width: 100%;
            .li-item{
                width: 100%;
            }
        }
        .btn-line{
            text-align: center;
        }
    }
  }
  .slide-pop-head {
    box-sizing: border-box;
  }
  .common-font-color:hover {
    cursor: pointer;
  }
  .private-operate{
    color: $--color-primary;
  }
  .private-operate-name{
    color: $--color-text-secondary;
    display: inline-block;
    width: 135px;
    word-break: break-all;
  }
  .private-operate:hover {
    cursor: pointer;
  }
  .label {
    font-size: 12px;
    color: $--color-text-secondary;
    text-align: left;
    line-height: 22px;
  }
  .text {
    word-break: break-all;
    cursor: pointer;
    color: #127FD2;
  }
  .slide-pop-text {
    font-size: 12px;
    color: $--color-text-primary;
    padding-bottom: 10px;
    border-bottom: 1px dotted $--border-color-lighter;
  }
  .slide-pop-pic {
    padding-top: 10px;
    .no-preview{
      color: $--color-info;
      cursor: default;
    }
  }
  .supplements-pop{
      p{
            margin-bottom: 20px;
            font-size: 14px;
            color: #333333;
            line-height: 14px;
        }
        .supplements-block{
            padding-bottom: 10px;
            .el-row{
                height: 20px;
                font-size: 12px;
                color: #333333;
                line-height: 20px;
                .el-col-8{
                    color: #999999;
                }
            }

        }
    }

    .look-more {
        color: $--color-text-primary;
        line-height: 20px;
        margin-bottom: 20px;
        display: inline-block;
        font-size: 12px;
        cursor: pointer;
    }
    .empty-block{
        text-align: center;
        color: $--color-text-secondary;
        padding-bottom: 10px;
        img{
            display: block;
            margin: 0 auto;
        }
    }
}
</style>
