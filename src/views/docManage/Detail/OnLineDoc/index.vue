<template>
    <div class="doc-detail-container">
        <div class="doc-detail-content">
            <!--  合同基本信息,所有人都可以见 -->
            <BaseInfo
                :contractBaseInfo="contractBaseInfo"
                :buttonDisplayRule="buttonDisplayRule"
                :currentUserIsSenderSide="currentUserIsSenderSide"
                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                :ssoDocDtl="ssoDocDtl"
                :source="source"
                @usePaperSign="usePaperSign"
            >
            </BaseInfo>
            <!-- 合同收件方 -->
            <Receivers
                :source="source"
                :contractBaseInfo="contractBaseInfo"
                :contractId="contractBaseInfo.contractId"
                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                @showOrder="handleShowSignOrder"
                @popShowChange="popShowChange"
                :canModifyHandWritingRecognition="canModifyHandWritingRecognition"
                :displayAutoSign="displayAutoSign"
                :ifSender="!!(subContractDetails || []).length"
            >
            </Receivers>
            <template v-for="(item,index) in subContractDetails">
                <!-- 合同详细信息，发件方可以见 -->
                <DetailInfo
                    :key="index"
                    :title="item.docTitle"
                    :contractBaseInfo="contractBaseInfo"
                    :subContractItem="item"
                    :canEdit="!contractBaseInfo.ifMultiPlatformContract"
                    :sensorsTrackContractInfo="sensorsTrackContractInfo"
                    @updateContractDetailForSender="updateContractDetailForSender($event,index)"
                    @popShowChange="popShowChange"
                >
                </DetailInfo>
            </template>
            <template v-if="!contractBaseInfo.ifMultiPlatformContract">
                <!-- 审计日志（原企业内部操作日志） -->
                <OperationLogs
                    v-if="entInternalDetails.length"
                    :contractId="contractBaseInfo.contractId"
                    :sensorsTrackContractInfo="sensorsTrackContractInfo"
                    :entInternalDetails="entInternalDetails"
                >
                </OperationLogs>
                <ComparisonLog></ComparisonLog>
                <PersonOeprationLog
                    v-if="personClaimDetails.length"
                    :personClaimDetails="personClaimDetails"
                ></PersonOeprationLog>
                <!-- 关联合同 -->
                <LinkContracts :linkedContractId="contractBaseInfo.contractId" :sensorsTrackContractInfo="sensorsTrackContractInfo"></LinkContracts>
            </template>

        </div>

        <!-- 合同缩略图 -->
        <MiniDoc
            :documents="contractBaseInfo.documents"
            :contractId="contractBaseInfo.contractId"
            :hybridServer="commonHeaderInfo.hybridServer"
            :hybridAccessToken="commonHeaderInfo.hybridAccessToken"
            :contractTitle="contractBaseInfo.contractTitle"
            @showPreview="handlePreview"
        ></MiniDoc>
        <!-- 图片预览 -->
        <PreviewDoc :previewDoc="curPreviewDoc" :contractId="contractBaseInfo.contractId" ref="previewDoc"></PreviewDoc>
        <!-- 签署顺序 -->
        <SignOrder
            v-if="contractBaseInfo.signers && contractBaseInfo.signers.length"
            :visible.sync="signOrderDialog.visible"
            :recipients="contractBaseInfo.signers"
            :ordered="contractBaseInfo.signOrdered"
            :sender="contractBaseInfo.sender"
            from="detail"
            ref="signOrderDialog"
        ></SignOrder>
        <!-- 弹框处理 -->
        <div class="doc-detail-dialogs">
            <component
                :is="curDialog"
                :params="curDialogParams"
                @close="handleDialogClose"
                @refreshTable="handleCancelDone"
            >
            </component>
        </div>
        <!-- 私信，合同附属资料图片预览 -->
        <Gallery @close="previewImgInfo.visible = false" :visible="previewImgInfo.visible" :title="previewImgInfo.fileName" :src="previewImgInfo.previewUrl"></Gallery>
        <!-- 右侧弹框 -->
        <SlidePop v-show="popShow.show" :outsideShow.sync="popShow.show">
            <SlideModelContent
                v-if="popShow.show"
                slot="slide-pop-content"
                :type="popShow.type"
                :params="popShow.params"
                :templateId="contractBaseInfo.templateId"
                :contractId="contractBaseInfo.contractId"
                @openPrivateLetterImg="openPrivateLetterImg"
                @previewAttachment="previewAttachment"
                @downloadAttachment="handleDownloadAttachment"
                @updatePrivateMessage="updatePrivateMessage"
            >
            </SlideModelContent>
        </SlidePop>
        <Preview ref="preview"></Preview>
        <DownloadAttachmentDialog :visible.sync="downloadAttachmentDialogVisible" @confirm="downloadAttachment" :fileType="fileType"></DownloadAttachmentDialog>
    </div>
</template>
<script>
import PreviewDoc from 'components/previewDoc';
import SwitchSubject from 'components/switchSubject/index.vue';
import CancelContract from 'components/cancelContract/index.vue';
import { docDetailMixin } from 'src/mixins/docDetail.js';
import { docComponentMixin } from 'src/mixins/docComponent.js';
export default {
    components: {
        CancelContract,
        SwitchSubject,
        PreviewDoc,
    },
    mixins: [docDetailMixin, docComponentMixin],
    props: {
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            curPreviewDoc: {},
            detailUrl: `/contract-api/contracts/detail/v2/${this.$route.params.contractId}`,
        };
    },
    methods: {
        usePaperSign() {
            this.contractBaseInfo = {
                ...this.contractBaseInfo,
                checkIfEnablePaperSign: true,
                editPaperSignStatus: true,
            };
        },
    },
};
</script>
