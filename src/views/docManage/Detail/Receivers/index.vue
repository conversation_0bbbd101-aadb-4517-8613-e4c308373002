<template>
    <div
        class="doc-detail-recipients"
        v-if="contractBaseInfo.signers && contractBaseInfo.signers.length > 0"
    >
        <h3>
            <!-- 合同收件方 -->{{ $t("docDetail.contractRecipient") }}
            <span class="sign-order fr" @click="handleShowSignOrder" v-if="!isExcelImport">
                <i class="el-icon-ssq-xitong-zuzhijiagou"></i>&nbsp;&nbsp;<span
                    class="common-font-color"
                >{{ $t("docDetail.viewSignOrders") }}</span>
            </span>
        </h3>
        <el-table
            ref="recipientsTable"
            class="ssq-table"
            :data="contractBaseInfo.signers"
            empty-text=" "
        >
            <el-table-column :label="$t('docDetail.user')" width="394">
                <template slot-scope="scope">
                    <!-- 签约主体 -->
                    <p class="column-user-subject">{{ $t("docDetail.signSubject") }}
                        <!-- {{ contractBaseInfo.ifMultiPlatformContract ? '（'+$t(`docDetail.platformSign.${scope.row.platformFrom || zh}`)+'）':'' }} -->
                        ：</p>
                    <p class="column-user-info-con" v-business-status="scope.row.abnormalBusOperationStatus">
                        <span v-if="isScanCodeAndNoClaim(scope.row)">{{ scope.row.roleName }}&nbsp;{{ $t("docDetail.signRole") }}</span>
                        <span v-else-if="scope.row.editable">
                            {{ editableRoleSignerNameText(scope.row) }}</span>
                        <template v-else>
                            <span v-if="scope.row.signerName">{{ scope.row.signerName }}&nbsp;</span>
                            <span v-if="!scope.row.signerName || !scope.row.hasAuthenticated">{{
                                scope.row.userType === "ENTERPRISE" ? `(${$t("docDetail.entAccount")})` : `(${$t("docDetail.personAccount")})`
                            }}&nbsp;</span>

                            <template v-if="showAuthenticateStatus">
                                <span
                                    v-if="scope.row.hasAuthenticated"
                                    class="authentication-icon-box"
                                >
                                    {{ $t("docDetail.certified") }}
                                </span>
                                <span v-else class="authentication-icon-box gray">{{
                                    $t("docDetail.unCertified")
                                }}</span>
                            </template>

                        </template>
                    </p>
                </template>
            </el-table-column>
            <el-table-column
                :width="$t('lang') === 'zh' ? 160 : 200"
                :label="$t('docDetail.state')"
            >
                <template slot-scope="scope">
                    <!-- 合同状态后端单独加了参数 -->
                    <div>
                        <p>
                            {{ scope.row.operateStatus }}
                        </p>
                        <p v-if="scope.row.showSignLink && !contractBaseInfo.ifMultiPlatformContract">
                            <SignLinkPopover
                                :index="scope.$index"
                                :row="scope.row"
                                :contractId="contractId"
                                :sensorsTrackContractInfo="sensorsTrackContractInfo"
                            ></SignLinkPopover>
                        </p>
                    </div>
                </template>
            </el-table-column>
            <el-table-column width="200" :label="$t('docDetail.time')">
                <template slot-scope="scope">
                    <div v-if="scope.row.showRemind && !contractBaseInfo.ifMultiPlatformContract">
                        <a
                            href="javascript:void(0)"
                            class="common-font-color"
                            @click="handleRemind(scope.row)"
                        >{{ $t("docDetail.notice") }}</a>
                    </div>
                    <div v-else>
                        {{ scope.row.finishTime | formatedTime }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column :label="$t('docDetail.detail')">
                <template slot-scope="scope">
                    <!--签署要求-->
                    <div class="list-style-circle" v-for="(item,index) in signRequirementList(scope.row)" :key="index">
                        <span>{{ item.text }}</span>
                        <template v-if="item.type==='handWritingRecognition'">
                            <el-switch
                                v-model="scope.row.signRequirement.handWritingRecognition"
                                :disabled="!SIGN_STATUS.includes(contractBaseInfo.contractStatus) || !canModifyHandWritingRecognition ||
                                    !['BEING_CARRIED'].includes(scope.row.signStatus)"
                                :width="30"
                                @change="handleHandWriteConfigStatusChange($event, scope.row)"
                                class="enable-switch"
                            >
                            </el-switch>
                            <CommonTip class="help-tip-icon">
                                <div slot="content">
                                    <p>{{ $t('docDetail.handWritingRecognitionChangeTip1') }}</p>
                                    <p>{{ $t('docDetail.handWritingRecognitionChangeTip2') }}</p>
                                </div>
                            </CommonTip>
                        </template>
                        <div class="scan-codesign-account" v-if="item.type==='scanCodeSign' && scope.row.proxyClaimerAccounts.length">({{ scope.row.proxyClaimerAccounts.join(',') }})</div>
                    </div>
                    <!-- 签署时效  -->
                    <p class="list-style-circle" v-if="scope.row.signedAging && Number(scope.row.signedAging)">
                        <span> {{ $t('docDetail.signedAging') }} {{ scope.row.signedAging | formatedsignedAging }}</span>
                    </p>
                    <!--纸质签署-->
                    <p class="list-style-circle" v-if="scope.row.changeToPaperSign === '1'">
                        <span>{{ $t("docDetail.changeToPaperSign") }}</span>
                    </p>
                    <!--补全人-->
                    <p class="list-style-circle" v-if="scope.row.receiverType === 'EDITOR'">
                        <span>{{ $t("docDetail.completeContractInfo") }}</span>
                    </p>
                    <!-- 合同发件人可以看到全部的私信，签署人只能看到自己的私信-->
                    <p class="list-style-circle" v-if="hasPrivateLetter(scope.row)">
                        <a
                            @click="showPrivate(scope.row)"
                            href="javascript:void(0)"
                            class="common-font-color"
                        ><!-- 签约须知 -->{{
                            $t("docDetail.slideContentTip.signNotice")
                        }}</a>
                    </p>
                    <!-- 拒签理由 -->
                    <CommonTip v-if="scope.row.rejectReason" class="item" effect="dark" :content="scope.row.rejectReason" placement="top-start">
                        <p slot="reference" class="list-style-circle-reject">
                            <span>{{ $t("docDetail.reject")
                            }}{{ $t("docDetail.rejectReason") }}：{{ scope.row.rejectReason }}
                            </span>
                        </p>
                    </CommonTip>
                    <!-- 发件方填写的经办人姓名，仅发件方可见 -->
                    <p v-if="ifSender && scope.row.userType !== 'PERSON'">
                        <span>{{ $t('docDetail.inputReceiver') }}</span>
                        <CommonTip class="help-tip-icon">
                            <div slot="content">
                                <p>{{ $t('docDetail.inputReceiverTip') }}</p>
                            </div>
                        </CommonTip>
                        <span>{{ '：' + (scope.row.inputUserName || $t('docDetail.inputReceiverNotInput')) }}</span>
                    </p>
                </template>
            </el-table-column>
            <el-table-column :fixed="$i18n.locale === 'ar' ? 'left' : 'right'" width="50">
                <template slot-scope="scope">
                    <CommonTip v-if="showAutoSignIcon(scope.row.operateStatus) && !contractBaseInfo.ifMultiPlatformContract" effect="dark" :content="$t('autoSignDialog.errorTip')" placement="top">
                        <i slot="reference" class="el-icon-ssq-zidongqian" @click="showNoAutoSignDialog(scope.row.receiverId)" />
                    </CommonTip>
                </template>
            </el-table-column>
        </el-table>
        <MessageBox
            @confirm="noAutoSignDialogShow=false"
            @close="noAutoSignDialogShow=false"
            :visible.sync="noAutoSignDialogShow"
            :dialogTitle="noAutoSignTitle"
        >
            <template #content>
                <h2>{{ noAutoSignTip }}</h2>
                <ol class="auto-sign-dialog">
                    <li v-for="(item, index) in noAutoSignReason" :key="index">{{ item }}</li>
                </ol>
            </template>
        </MessageBox>
    </div>
</template>

<script>
import { formatDateToString, secondsToDay } from 'pub-utils/date.js';
import SignLinkPopover from './SignLinkPopover';
import { mapGetters } from 'vuex';
import MessageBox from 'components/messageBox/index.vue';
import _get from 'lodash/get';
import i18n from 'src/lang';
import { handleSensorsHttpEventTrack } from 'src/utils/handleSensorsHttpEventTrack.js';
import { postReceiverHandWritingRecognitionConfig } from '@/api/docManage/detail';
export default {
    components: {
        SignLinkPopover,
        MessageBox,
    },
    filters: {
        formatedTime: (val) => {
            if (val) {
                return formatDateToString({
                    date: val,
                    format: 'YYYY-MM-DD hh:mm:ss',
                });
            }
            return '';
        },
        formatedsignedAging: (val) => {
            const { day, hour, minute } =  secondsToDay(val);
            let str = '';
            if (day > 0) {
                str += `${day}${i18n.t('docDetail.day')}`;
            }
            if (hour > 0) {
                str += `${hour}${i18n.t('docDetail.hour')}`;
            }
            if (minute > 0) {
                str += `${minute}${i18n.t('docDetail.minute')}`;
            }
            return str;
        },
    },
    props: {
        sensorsTrackContractInfo: {
            type: Object,
            default: () => {},
        },
        contractBaseInfo: {
            type: Object,
            default: () => {},
        },
        source: {
            type: String,
        },
        contractId: {
            type: String,
        },
        // 展示自动签失败理由提示按钮
        displayAutoSign: {
            type: Boolean,
            default: false,
        },
        canModifyHandWritingRecognition: { // 收件方能不能改手写笔记识别配置
            type: Boolean,
            default: false,
        },
        ifSender: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            noAutoSignDialogShow: false,
            noAutoSignReason: [],
            noAutoSignTitle: '',
            noAutoSignTip: '',
            SIGN_STATUS: ['SENT', 'IN_SEND_APPROVAL', 'BEING_CARRIED'], // 待发送，待审批，签署中
        };
    },
    computed: {
        ...mapGetters(['checkFeat', 'getIsForeignVersion']),
        unCompleteStatus() {
            return this.SIGN_STATUS.includes(this.contractBaseInfo.contractStatus);
        },
        showAuthenticateStatus() {
            // SAAS-38721: 合同结束后不再展示认证状态
            return !this.getIsForeignVersion && !this.contractBaseInfo.ifMultiPlatformContract && this.unCompleteStatus;
        },
        isExcelImport() {
            return this.source === 'EXCEL_IMPORT';
        },
        isScanCodeAndNoClaim() {
            return row => {
                const conditionArr = [
                    row.signType === 'SCAN_CODE_SIGNATURE', // 扫码签署
                    // 由于后端该字段父子合同的处理逻辑不同 父合同为 WAIT_FOR_CLAIM，NOT_START，
                    // 子合同为其他状态，表示合同还没有归属人 （包含待认领，还有顺序签署未开始）
                    ['WAIT_FOR_CLAIM', 'NOT_START', '待认领', 'To be claimed', '未读未签', 'Not read or signed'].includes(row.signStatus),
                    row.userType === 'PERSON', // 个人类型
                ];
                // 如果是 扫码签署 合同没有归属人 个人类型时，该条件成立
                return conditionArr.every(condition => condition);
            };
        },
        // 是否有签约须知且可见
        hasPrivateLetter() {
            return row => {
                return  row.privateLetter?.content || row.privateLetter?.privateLetterFileList?.length || row.canUpdate;
            };
        },
        isJa() {
            return this.$i18n.locale === 'ja';
        },
        isEn() {
            return this.$i18n.locale === 'en';
        },
        editorInfo() {
            return (this.contractBaseInfo.signers || []).find(a => a.receiverType === 'EDITOR') || {};
        },
        editableRoleSignerNameText() {
            return row => {
                return this.editorInfo.showRemind ? this.$t('docDetail.completeByRole', { roleName:
                    this.editorInfo.roleName }) : `${row.signerName}${this.$t('docDetail.completedByRole', { roleName:
                    this.editorInfo.roleName })}`;
            };
        },
    },
    methods: {
        handleHandWriteConfigStatusChange(val, row) {
            if (!val) {
                row.signRequirement.handWritingRecognitionWhenSent = true;
            }
            postReceiverHandWritingRecognitionConfig(this.contractId, row.receiverId, val)
                .then(() => {
                    this.$MessageToast.success(this.$t('docDetail.modifySuccess'));
                }).catch(() => {
                    // 修改失败回退值
                    row.signRequirement.handWritingRecognition = !val;
                });
        },
        showAutoSignIcon(operateStatus) {
            return this.displayAutoSign && !this.isEn && !this.isJa && !this.getIsForeignVersion && operateStatus !== this.$t('docDetail.signed');
        },
        // 自动签未启用提示弹窗
        showNoAutoSignDialog(receiverId) {
            this.$sensors.track({
                eventName: 'Ent_ContractManageDetail_BtnClick',
                eventProperty: {
                    page_name: '合同管理详情页',
                    first_category: '合同收件方',
                    icon_name: '查看自动签失败原因',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.$http.get(`/contract-api/contracts/${this.contractId}/${receiverId}/explainCode`)
                .then((res) => {
                    const explains = _get(res, 'data.result.explains', []);
                    const canAutoSign = !explains.length;
                    this.noAutoSignTitle = canAutoSign ? this.$t('autoSignDialog.waitTip') : this.$t('autoSignDialog.errorTip');
                    this.noAutoSignTip = canAutoSign ? this.$t('autoSignDialog.wait') : this.$t('autoSignDialog.reason');
                    !canAutoSign && (this.noAutoSignReason = explains);
                    this.noAutoSignDialogShow = true;
                });
        },
        signRequirementList(row) {
            const { userType, signType, showNoNeedToSign } = row;
            const signRequirement = row.signRequirement || {};
            const signTypeText = { 'SEAL_AND_SIGNATURE': this.$t('docDetail.stampSign'), 'ENTERPRISE_SIGNATURE': this.$t('docDetail.entSign'), 'SEAL': this.$t('docDetail.stamp') };
            const list = [{
                show: signRequirement.messageAndFaceVerify, text: this.$t('docDetail.messageAndFaceVerify'), // 刷脸+验证码签署
            }, {
                show: signRequirement.faceFirst, text: this.$t('docDetail.faceFirstVerifyCodeSecond'), // 优先刷脸，备用验证码签署
            }, {
                show: signRequirement.faceVerify, text: this.$t('docDetail.faceSign'), // 优先刷脸，备用验证码签署
            }, {
                show: signRequirement.requireIdentityAssurance && userType === 'PERSON', text: this.$t('docDetail.RealNameCertificationRequired'), // 需要实名认证
            }, {
                /**
                 * @desc https://jira.bestsign.tech/browse/CFD-13038
                 * 签署方为企业时，requireIdentityAssurance值为true或者false都不展示实名文案，企业签署方都需要实名
                 * 为了兼容通过API发送的合同，选择签署方企业时 requireIdentityAssurance 值设置为false
                */
                show: !signRequirement.requireIdentityAssurance && userType === 'PERSON', text: this.$t('docDetail.RealNameCertificationNotRequired'), // 不需要实名认证
            }, {
                show: signRequirement.forceHandWrite, text: this.$t('docDetail.MustHandwrittenSignature'), // 必须手写签名
            }, {
                show: signRequirement.handWritingRecognition || signRequirement.handWritingRecognitionWhenSent, text:
                    this.$t('docDetail.handWritingRecognition'), // 开启手写笔迹识别（试用）
                type: 'handWritingRecognition',
            }, {
                show: signRequirement.handWriteNotAllowed, text: this.$t('docDetail.handWriteNotAllowed'), // 显示「不允许手写」
            }, {
                show: signRequirement.ifScanCodeSignature, text: this.$t('docDetail.scanCodeSign'), // 显示「扫码签字」
                type: 'scanCodeSign',
            }, {
                show: userType === 'ENTERPRISE', text: signTypeText[signType], // 盖章/签字/盖章并签字
            }, {
                show: signRequirement.requireEnterIdentityAssurance, text: this.$t('docDetail.requireEnterIdentityAssurance'), // 启用经办人身份核验
            }, {
                show: showNoNeedToSign, text: this.$t('docDetail.noNeedToSign'), // 已无需签署
            }, {
                show: signRequirement.videoVoiceVerify, text: this.$t('docDetail.dualRequired'),
            }];
            return list.filter(item => item.show);
        },
        // 单个提醒签署
        handleRemind(row) {
            this.$sensors.track({
                eventName: 'Ent_ContractManageDetail_BtnClick',
                eventProperty: {
                    page_name: '合同管理详情页',
                    first_category: '合同收件方',
                    icon_name: '提醒',
                    ...this.sensorsTrackContractInfo,
                },
            });
            // 当前提醒的合同是否为扫码签
            const isScanCodeSignature = this.isScanCodeAndNoClaim(row);
            if (isScanCodeSignature) {
                // 展示扫码签字的弹窗
                return this.$confirm(`<i class="iconfont el-icon-ssq-tishizhuangtai"></i>${this.$t('scanCodeRemind.detailContent')}`, this.$t('scanCodeRemind.tip'), {
                    showCancelButton: false,
                    confirmButtonText: this.$t('scanCodeRemind.confirm'),
                    customClass: 'scan-code-remind-dialog',
                    dangerouslyUseHTMLString: true,
                });
            }
            const loading = this.$loading();
            this.$http
                .post(
                    `/contract-api/contracts/${this.contractId}/notice/${row.userId}/sign/notify`,
                    { receiverId: row.receiverId },
                )
                .then(() => {
                    handleSensorsHttpEventTrack({
                        moduleName: 'ContractManage',
                        pageName: '合同管理详情页',
                        ifSuccess: true,
                        iconName: '提醒',
                        requestUrl: `/contract-api/contracts/${this.contractId}/notice/${row.userId}/sign/notify`,
                        contractBaseInfo: { first_category: '合同到收件方', ...this.sensorsTrackContractInfo },
                    });
                    this.$MessageToast.success(this.$t('docDetail.remindSucceed'));
                })
                .catch((err) => {
                    handleSensorsHttpEventTrack({
                        moduleName: 'ContractManage',
                        pageName: '合同管理详情页',
                        ifSuccess: false,
                        err,
                        iconName: '提醒',
                        requestUrl: `/contract-api/contracts/${this.contractId}/notice/${row.userId}/sign/notify`,
                        contractBaseInfo: { first_category: '合同到收件方', ...this.sensorsTrackContractInfo },
                    });
                })
                .finally(() => {
                    loading.close();
                });
        },
        handleShowSignOrder() {
            this.$sensors.track({
                eventName: 'Ent_ContractManageDetail_BtnClick',
                eventProperty: {
                    page_name: '合同管理详情页',
                    first_category: '合同收件方',
                    icon_name: '查看签署顺序',
                    ...this.sensorsTrackContractInfo,
                },
            });
            this.$emit('showOrder');
        },
        // 查看附件
        showPrivate(item) {
            this.$sensors.track({
                eventName: 'Ent_ContractManageDetail_BtnClick',
                eventProperty: {
                    page_name: '合同管理详情页',
                    first_category: '合同收件方',
                    icon_name: '签约须知',
                    ...this.sensorsTrackContractInfo,
                },
            });
            if (item.privateLetter.currentUserCanSee) {
                const popShow = {
                    show: true,
                    params: item,
                    type: 'private',
                };
                this.$emit('popShowChange', popShow);
            } else {
                this.$MessageToast.error(this.$t('docDetail.privateTip'));
            }
        },
    },
};
</script>

<style lang="scss" scoped>
    .doc-detail-recipients{
        h3{
            height: 50px;
            line-height: 50px;
            color: $--color-text-primary;
            cursor: pointer;
            font-size: 16px;
            .el-icon-ssq-xitong-zuzhijiagou{
                color: $--color-text-secondary;
                font-size: 14px;
            }
            span.common-font-color{
                font-size: 12px;
            }
            [dir=rtl] & .sign-order {
                float: left;
            }
        }
        .column-user-subject{
            color: $--color-text-secondary;
        }
        .ssq-table{
            .column-user-info{
                display: flex;
                .column-user-info-con{
                    flex:1;
                }
            }
        }
        .ssq-table.el-table.el-table--enable-row-hover{
            width: 100%;
            border-left: 1px solid $--border-color-lighter;
            border-right: 1px solid $--border-color-lighter;
            color: $--color-text-regular;
            &::v-deep.el-table__header-wrapper{
                .cell{
                     color: $--color-text-regular;
                }
            }
            &::v-deep.el-table__body-wrapper{
                overflow: unset;
            }

            &::v-deep.el-table__row{
                .cell{
                    padding: 14px 18px;
                }

                .garyLine{
                    color: $--color-text-secondary;
                }

                p span{
                    i{
                        color: $--color-text-secondary;
                    }
                }

                // .conflict-tip{
                //     position: relative;

                //     .el-icon-ssq-jijiangguoqi{
                //         font-size: 14px;
                //         color: #cb0000;
                //         cursor: pointer;
                //     }

                //     em{
                //         position: absolute;
                //         display: none;
                //         top: -30px;
                //         left: -85px;
                //         padding: 4px 14px 4px;
                //         width: auto;
                //         white-space: nowrap;
                //         line-height: 18px;
                //         z-index: 1;
                //         color: $--color-white;
                //         border-radius: 2px;
                //         background: #333;

                //         &:after{
                //             content: '';
                //             position: absolute;
                //             display: inline-block;
                //             left: 87px;
                //             bottom: -8px;
                //             border: 5px solid transparent;
                //             border-top-color: #333;
                //             z-index: 2;
                //         }
                //     }

                //     &:hover{
                //         em{
                //             display: block;
                //         }
                //     }
                // }

                .sign-link-btn{
                    margin-top: 3px;
                    height: 26px;

                    .el-icon-ssq-share_link{
                        font-size: 14px;
                        // color: #2298f1;
                        color:$--color-primary-light-1;
                    }

                    &:hover{
                        .el-icon-ssq-share_link{
                            color: $--color-white;
                        }
                    }
                }
            }
        }
        .authentication-icon-box {
            display: inline-block;
            vertical-align: middle;
            color: $--color-success;
            background: $--color-success-light;
            padding: 2px 6px;
            border-radius: 2px;
            line-height: 18px;
            &.gray {
                color: $--color-text-secondary;
                background: $--background-color-base;
            }
        }

        .list-style-circle{
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            .scan-codesign-account{
                white-space: wrap;
            }
            .enable-switch {
                margin-top: -2px;
                margin-left: 5px;
            }
            .help-tip-icon {
                margin-left: 5px;
                font-size: 14px;
                vertical-align: middle;
            }
        }
        .list-style-circle-reject{
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            :after {
                content: '';
                border: 2px solid $--border-color-light;
                border-radius: 50%;
                position: absolute;
                left: -10px;
                margin-top: -2px;
                top: 50%;
            }
        }
        .el-icon-ssq-zidongqian {
            cursor: pointer;
            &:hover{
                color: $--color-primary;
            }
        }
        ol.auto-sign-dialog {
            padding: 10px 0 0 15px;
            list-style: auto;
        }
    }
</style>
