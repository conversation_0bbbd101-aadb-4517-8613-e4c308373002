<!--合同预览页面，api使用，做了移动端适配-->
<template>
    <div class="view-doc-wrap" v-loading="isLoading" :class="{'view-doc-wrap-mobile': isMobile}">
        <div class="header" :class="isRejectSignerPage?'rejectHeader': 'commonHeader'"><slot name="header">{{ contractTitle }}</slot></div>
        <div class="wrap" v-if="docList.length && hybridContractServerConnected">
            <div class="options-container" v-if="!isMobile">
                <div class="options">
                    <i class="iconfont el-icon-ssq-fangdajing1 scale" @click="zoomBigger"></i>
                    <span></span>
                    <i class="iconfont el-icon-ssq-suoxiaojing scale" @click="zoomSmaller"></i>
                </div>
                <div class="preview-header">
                    <i class="iconfont el-icon-ssq-wenjian"></i>
                    {{ contractTitle }}
                </div>
            </div>
            <div class="documents-container">
                <div class="documents-content" @scroll="scrolling">
                    <div class="zoomContainer"
                        :style="{
                            'maxWidth': `${(zoomWidth) * 2}px`,
                            'width': `${zoomWidth}px`,
                            'height': `${zoomHeight}px`
                        }"
                    >
                        <div class="documents"
                            :class="{'reject-doc': isRejectSignerPage}"
                            :style="`transform: scale(${zoomCoefficient})`"
                        >
                            <!-- 循环渲染合同文档 -->
                            <section class="document"
                                v-for="(doc, docIndex) in docList"
                                :key="docIndex"
                            >
                                <div class="page"
                                    v-for="(page, pageIndex) in doc.page"
                                    :key="pageIndex"
                                    :style="`width: ${page.width}px`"
                                >
                                    <div class="page-content"
                                        :style="`height: ${page.height}px`"
                                    >
                                        <img class="page-img"
                                            v-if="pageIndex < 6"
                                            :width="page.width"
                                            :height="page.height"
                                            :src="page.highQualityPreviewUrl + (showLabel ? '&needMergeLabel=true': '')"
                                            alt=""
                                        />
                                        <img class="page-img"
                                            v-else
                                            alt=""
                                            :height="page.height"
                                            :width="page.width"
                                            v-lazy="page.highQualityPreviewUrl + (showLabel ? '&needMergeLabel=true': '')"
                                        />
                                    </div>
                                    <template v-if="isRejectSignerPage">
                                        <div class="riding-seals" v-if="ridingSealList.length">
                                            <div class="riding-seals-bg"></div>
                                            <RejectRidingSeal
                                                v-for="(ridingSeal, sealIndex) in ridingSealList"
                                                :key="ridingSeal.receiverId"
                                                :ridingSeal="ridingSeal"
                                                :pageHeight="page.height"
                                                :color="computeRgbColor(ridingSeal)"
                                                :sealIndex="sealIndex"
                                                :zoom="zoomCoefficient"
                                                @rejectLabel="handelRejectLabel"
                                                :pageIndex="pageIndex"
                                            >
                                            </RejectRidingSeal>
                                        </div>
                                        <RejectLabels
                                            v-for="label in rejectLabelList(page.pageNumber, page.marks)"
                                            :key="label.labelId"
                                            :pageHeight="page.height"
                                            :pageWidth="page.width"
                                            :zoom="zoomCoefficient"
                                            :color="computeRgbColor(label)"
                                            :cacheZIndex="cacheZIndex"
                                            :label="label"
                                            @rejectLabel="handelRejectLabel"
                                            @updateZIndex="handleZIndexUpdate"
                                        ></RejectLabels>
                                    </template>

                                    <!-- 页脚 -->
                                    <div class="page-pager">
                                        <span>{{ doc.fileName }}</span>
                                        <span>{{ $t('docView.totalPageTip',{num:pageIndex + 1, total:doc.pageSize}) }}<!-- 第{{pageIndex + 1}}页，共{{doc.pageSize}}页 --></span>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                <!-- 右侧缩略图栏 -->
                <div class="mini-documents" v-if="!isMobile">
                    <ul>
                        <li class="doc" v-for="(doc, docIndex) in docList" :key="docIndex">
                            <div class="doc-title" :class="{'close':docIndex !== 0 || (docIndex==0 && docList.length>1)}" @click="handleClickDocTitle">{{ doc.fileName }}</div>
                            <div class="doc-totalPage" :style="docIndex!=0 || (docIndex==0 && docList.length>1) ? 'display: none':''"><!-- 页数 -->{{ $t('docView.numOfPage') }}：{{ doc.pageSize }}<!-- 页 -->{{ $t('docView.page') }}</div>
                            <div class="doc-pages" :style="docIndex!=0 || (docIndex==0 && docList.length>1) ? 'display: none':''">
                                <ul>
                                    <li v-for="(page, pageIndex) in doc.page" :key="pageIndex" :class="scrollDocIndex==docIndex && scrollPageIndex==pageIndex ? 'current':''">
                                        <div class="doc-page">
                                            <img alt=""
                                                v-if="pageIndex < 6"
                                                :src="page.imagePreviewUrl"
                                                width="130"
                                                @click="handleClickMiniImage(docIndex, pageIndex)"
                                            >
                                            <img alt=""
                                                v-else
                                                v-lazy="page.imagePreviewUrl"
                                                width="130"
                                                @click="handleClickMiniImage(docIndex, pageIndex)"
                                            >
                                            <span class="page-num-bg"></span>
                                            <span class="page-num">{{ pageIndex+1 }}</span>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="wrap" v-else-if="!hybridContractServerConnected">
            <div class="net-error-container">
                <div class="tip-img"></div>
                <h4><!-- 无法查看合同 -->{{ $t('docView.canNotCheckContract') }}</h4>
                <p><!-- 发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器 -->{{ $t('docView.privateStoreContractTip') }}</p>
            </div>
        </div>
        <Footer class="footer"></Footer>
    </div>
</template>

<script>
import Footer from 'pub-businessComponents/footer/index.vue';
import { toggleClass, scrollToYSmooth } from 'pub-utils/dom.js';
import { isPC } from 'pub-utils/device.js';
import RejectLabels from 'components/rejectLabels';
import { rgbColorInfo } from 'const/const';
import RejectRidingSeal from 'components/rejectRidingSeal';
import cloneDeep from 'lodash/cloneDeep';
export default {
    name: 'ViewDoc',
    components: {
        Footer,
        RejectLabels,
        RejectRidingSeal,
    },
    props: {
        isRejectSignerPage: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        const w = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
        return {
            rejectLabels: [],
            title: '',
            hybridContractServerConnected: true,
            docList: [],
            contractId: this.$route.query.contractId,
            contractTitle: '',
            isLoading: 1,
            // 常量
            SPACE_HEIGHT: 20,
            // 合同展示
            zoomCoefficient: 1,
            // 合同浏览
            scrollDocIndex: 0,
            scrollPageIndex: 0,
            scrollFlag: false,
            isMobile: !isPC() || w < 768,
            showLabel: this.$route.query.showLabel,
            ridingSealList: [],
            allLabels: [],
            cacheZIndex: 10, // 缓存z-index，解决字段层级重叠无法重复切换的问题，字段默认有个z-index 10
        };
    },
    computed: {
        // 待驳回的标签列表
        rejectLabelList() {
            return (pageNumber, labels) => {
                return (labels || []).filter(label => {
                    const isTrue = label.pageNumber === pageNumber && label.status === 'WAIT_FOR_COMPOSE';
                    return isTrue;
                });
            };
        },
        // 返回所有合同图片中最大宽度值
        maxWidth() {
            return Math.max.apply(null, this.docList.map(item => item.imageMaxWidth));
        },
        // 整个合同容器宽高的计算，zoomCoefficient为整体缩放系数
        zoomWidth() {
            if (this.isRejectSignerPage && this.ridingSealList.length) {
                return (this.maxWidth + 116) * this.zoomCoefficient;
            }
            return this.maxWidth * this.zoomCoefficient;
        },
        zoomHeight() {
            return this.svgHeight * this.zoomCoefficient;
        },
        // 计算合同图片缩放系数：文档容器宽度与合同图片最大宽度比值
        suitableZoomCoefficient() {
            const diff = this.isMobile ? 0 : (this.isRejectSignerPage ? 250 : 100);
            const documentWidth = document.querySelector('.documents-content').getBoundingClientRect().width;
            return (documentWidth - diff) / this.maxWidth;
        },
        // 是否连接了混合云合同内网
        isInLan() {
            return this.$store.getters.getInLAN;
        },
    },
    methods: {
        handleZIndexUpdate() {
            this.cacheZIndex = Math.max(0, this.cacheZIndex - 1);
        },
        getAllLabels() {
            this.allLabels = [];
            this.docList.forEach((item) => {
                (item.page || []).forEach(page => {
                    (page.marks || []).forEach(mark => {
                        this.allLabels.push(mark);
                    });
                });
            });
            this.allLabels = this.allLabels.concat(this.ridingSealList);
        },
        // 合同缩放
        zoomBigger() {
            if (this.zoomCoefficient < 2 || this.zoomCoefficient < this.suitableZoomCoefficient) {
                this.zoomCoefficient += 0.2;
            }
        },
        zoomSmaller() {
            if (this.zoomCoefficient > 0.6 || this.zoomCoefficient > this.suitableZoomCoefficient) {
                this.zoomCoefficient -= 0.2;
            }
        },
        // 浏览合同相关
        // 函数节流
        scrolling(e) {
            if (this.scrollFlag) {
                return;
            }
            this.scrollFlag = true;
            setTimeout(() => {
                this.onSVGSrcoll(e);
                this.scrollFlag = false;
            }, 50);
        },
        // svg滚动
        onSVGSrcoll(e) {
            // 获取滑动到的docIndex和pageIndex
            this.docList.forEach((item, index) => {
                if (e.target.scrollTop + 20 > (this.docList[index - 1] || { totalHeight: 0 }).totalHeight && e.target.scrollTop + 20 < item.totalHeight * this.zoomCoefficient) {
                    this.scrollDocIndex = index - 1 < 0 ? 0 : index - 1;
                } else if (e.target.scrollTop + 20 > item.totalHeight * this.zoomCoefficient) {
                    this.scrollDocIndex = index;
                }
            });

            const pageScrollTop = e.target.scrollTop - this.docList[this.scrollDocIndex].totalHeight * this.zoomCoefficient;
            const pageHeight = this.docList[this.scrollDocIndex].page[0].height;
            this.scrollPageIndex = Math.floor((pageScrollTop + 20) / ((pageHeight + this.SPACE_HEIGHT) * this.zoomCoefficient));
        },
        /**
         * @param  {Array} ary 数组
         * @return {Number} 数组项height之和 + 20
         */
        sumHeight(ary) {
            return ary.length ? ary.reduce((a, b) => {
                return {
                    height: parseFloat(a.height) + parseFloat(b.height) + this.SPACE_HEIGHT,
                };
            }).height : 0;
        },
        sumAryHeight(ary, index) { // 如果一份pdf各图片高度不等，就会有bug，ruaruarua...
            const sliceAry = ary.slice(0, index);
            sliceAry.push({ height: 0 }); // todo
            const res = parseFloat(this.sumHeight(sliceAry));
            sliceAry.splice(sliceAry.length - 1, 1);
            return res;
        },
        // 编辑区交互
        handleClickDocTitle(e) {
            const miniImgDisplay = e.target.nextElementSibling.nextElementSibling.style.display;
            toggleClass(e.target, 'close');
            if (miniImgDisplay === 'none') {
                e.target.nextElementSibling.nextElementSibling.style.display = 'block';
                e.target.nextElementSibling.style.display = 'block';
            } else if (!miniImgDisplay || miniImgDisplay === 'block') {
                e.target.nextElementSibling.nextElementSibling.style.display = 'none';
                e.target.nextElementSibling.style.display = 'none';
            }
        },
        handleClickMiniImage(docIndex, pageIndex) {
            const documentsContentDom = document.querySelector('.documents-content');
            const docHeight = this.docList[docIndex].totalHeight;
            const pageHeight = this.sumAryHeight(this.docList[docIndex].page, pageIndex);
            scrollToYSmooth(documentsContentDom, (docHeight + pageHeight) * this.zoomCoefficient);
        },
        // 为文档计算并添加totalHeight属性
        initTotalHeight() {
            this.docList.forEach((item, index) => {
                let totalHeight = 0;
                this.docList.slice(0, index).forEach(item => {
                    item.page.push({ width: 0, height: 0 }); // todo
                    // 计算当前文档所有page高度之和
                    totalHeight += this.sumHeight(item.page);

                    item.page.splice(item.page.length - 1, 1);
                });
                this.docList[index].totalHeight = totalHeight;
            });
        },
        initSvgHeight() {
            const lastDoc = this.docList[this.docList.length - 1];
            this.svgHeight = lastDoc.totalHeight + this.sumHeight(lastDoc.page);
        },
        getDocs() {
            return this.$http.get(`contract-api/contracts/${this.contractId}?include=DOCUMENT,CURRENT_USER`);
        },
        getRejectSignerDocs() {
            return this.$http.get(`${signPath}/web/resign/document-info/${this.contractId}`);
        },
        handelRejectLabel(obj) {
            const tempDocList = cloneDeep(this.docList);
            tempDocList.forEach((item) => {
                (item.page || []).forEach(page => {
                    (page.marks || []).forEach(mark => {
                        if (mark.name === obj.name && mark.type === obj.type && !['SEAL', 'SIGNATURE', 'CONFIRMATION_REQUEST_REMARK', 'DECORATE_RIDING_SEAL', 'PICTURE'].includes(mark.type)) {
                            // 同名字段一起驳回
                            mark.rejectOption = obj.rejectOption;
                        }
                    });
                });
            });
            this.docList = tempDocList;

            this.getAllLabels();

            this.rejectLabels = this.allLabels.filter(item => item.rejectOption).map(item => {
                return {
                    labelId: item.labelId,
                    contractId: item.contractId,
                    receiverId: item.receiverId,
                };
            });

            this.$emit('rejectSignerLabel', this.rejectLabels);
        },
        computeRgbColor() {
            // 驳回重签暂时不区分标签颜色
            return  rgbColorInfo[0];
        },
        /**
         * @param   {object} docList 合同文档信息
         * @return  {object} 返回经过处理的合同文档信息
         * @desc    $hybrid.getContractImg 对混合云合同图片路径重定向
         */
        initDocData(docList) {
            // 过滤签约存证
            docList = docList.filter(doc => doc.fileType !== 'APPENDIX_FILE');
            return docList.map(doc => {
                return Object.assign({}, doc, {
                    page: doc.page.map((page) => {
                        // page标记，为了避免从指定位置页跳过来同一份合同存在的图片缓存问题
                        const imagePreviewUrl = this.$hybrid.getContractImg(page.imagePreviewUrl);
                        const highQualityPreviewUrl = this.$hybrid.getContractImg(page.highQualityPreviewUrl);
                        return Object.assign({}, page, {
                            imagePreviewUrl,
                            highQualityPreviewUrl,
                        });
                    }),
                });
            });
        },
        formatLabelData(docList) {
            docList.forEach((item) => {
                (item.page || []).forEach(page => {
                    (page.marks || []).forEach(mark => {
                        Object.assign(mark, {
                            labelExtends: {
                                pxFontSize: mark.style && mark.style.fontSize,
                            },
                            labelPosition: {
                                x: mark.x,
                                y: mark.y,
                                width: mark.width,
                                height: mark.height,
                                pageNumber: mark.pageNumber,
                            },
                            labelType: mark.type,
                            rejectOption: false, // 初始化所有的标签都是驳回重签页面未选中的
                        });
                    });
                });
            });
            return docList;
        },
    },
    beforeMount() {
        this.isMobile && this.$utils.changeViewport('blur');
    },
    beforeDestroy() {
        this.$utils.changeViewport('focus');
    },
    created() {
        // 驳回重签页面
        this.isRejectSignerPage && this.getRejectSignerDocs()
            .then(res => {
                const { data: { result: { resignDocumentInfoList, resignRidingInfoList } } } = res;
                this.docList = this.formatLabelData(this.initDocData(resignDocumentInfoList || []));

                this.ridingSealList = resignRidingInfoList || [];
                this.initTotalHeight();
                this.initSvgHeight();
                this.getAllLabels();
            })
            .then(() => {
                this.zoomCoefficient = this.suitableZoomCoefficient;
            })
            .finally(() => {
                this.isLoading = 0;
            });
        // 非驳回重签页面
        !this.isRejectSignerPage && this.getDocs()
            .then(res => {
                const data = res.data;
                this.contractTitle = data.contractTitle;
                return this.$hybrid.decideContractEnv(data.systemType, data.deltaHybridVersion, data.contractId)
                    .then(() => {
                        if (data.systemType === 'HYBRID_CLOUD' && !this.isInLan) {
                            this.hybridContractServerConnected = false;
                        }
                        // 确保先 decideContractEnv 再initDocData，否则混合云合同无法正常显示
                        this.docList = this.initDocData(data.documents);
                        // 先initTotalHeight，再initSvgHeight，顺序不能改变
                        this.initTotalHeight();
                        this.initSvgHeight();
                    });
            })
            .then(() => {
                this.zoomCoefficient = this.suitableZoomCoefficient;
                this.isLoading = 0;
            })
            .catch(() => {
                this.isLoading = 0;
            });
    },
};
</script>

<style lang="scss" scoped>
    .view-doc-wrap {
        user-select: none;
        position: relative;
        height: 100vh;
        font-size: 12px;
        color: $--color-text-primary;
        background-color: $--background-color-base;
        .header {
            position: fixed;
            top: 0;
            z-index: 999;
            width: 100%;
            height: 50px;
            line-height: 50px;
            font-size: 18px;
            color: $--color-white;
            background-color: $header-color;
        }
        .commonHeader {
            padding-left: 50px;
        }
        .wrap {
            position: absolute;
            top: 90px;
            bottom: 35px;
            width: 100%;
            .options-container {
                z-index: 99;
                position: fixed;
                top: 50px;
                width: 100%;
                height: 40px;
                line-height: 40px;
                background-color: $--background-color-base;
                border-bottom: 1px solid $border-color;
                .options {
                    border-right: 1px solid $border-color;
                    text-align: center;
                    margin-right: 210px;
                    i {
                        font-size: 15px;
                        color: $--color-text-regular;
                        cursor: pointer;
                        &:first-child {
                            margin-right: 5px;
                        }
                        &:last-child {
                            margin-left: 5px;
                        }
                    }
                    i.scale {
                        width: 30px;
                        height: 30px;
                        line-height: 30px;
                    }
                    span {
                        display: inline-block;
                        position: relative;
                        top: 3px;
                        height: 15px;
                        border-left: 1px solid $border-color;
                    }
                }
                .preview-header {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 193px;
                    font-size: 14px;
                    font-weight: bold;
                    color: $--color-text-primary;
                    padding-left: 17px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    i {
                        font-size: 15px;
                        color: $--color-text-regular;
                        margin-right: 8px;
                    }
                }
            }
            .documents-container {
                height: 100%;
                .documents-content {
                    position: relative;
                    height: 100%;
                    border-right: 1px solid $border-color;
                    margin-right: 210px;
                    overflow: auto;
                    .zoomContainer{
                        margin: 0 auto;
                        overflow: hidden;
                    }
                    .zoomContainer,
                    .documents,
                    .document,
                    .page,
                    .page-content{
                        position: relative;
                    }
                    .documents{
                        display: inline-block;
                        overflow: hidden;
                        transform-origin: 0 0 0;
                        .page{
                            margin: 0 auto;
                            .page-content{
                                .page-img{
                                    width: 100%;
                                }
                                .svg-label{
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    width: 100%;
                                    height: 100%;
                                }
                            }
                            .page-pager{
                                height: 20px;
                                line-height: 20px;
                                clear: both;
                                color: $--color-text-secondary;
                                font-size: 12px;
                                & span:first-child{
                                    float: left;
                                }
                                & span:last-child{
                                    float: right;
                                }
                            }
                        }
                    }
                    .reject-doc {
                        overflow: visible;
                    }
                }
                .mini-documents {
                    width: 210px;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    right: 0;
                    overflow: auto;
                    border-top: 1px solid $border-color;
                    .doc {
                        border-bottom: 1px solid $border-color;
                    }
                    .doc-title {
                        position: relative;
                        height: 18px;
                        line-height: 18px;
                        color: $--color-text-primary;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        cursor: pointer;
                        padding: 6px 40px 6px 39px;
                        transition: transform .3s;
                        // border-top: 1px solid $border-color;
                        &:hover {
                            color: $--color-primary-light-1;
                        }
                        &:after {
                            position: absolute;
                            top: 15px;
                            right: 24px;
                            content: "";
                            border-left: 2px solid $--color-text-primary;
                            border-top: 2px solid $--color-text-primary;
                            padding: 3px;
                            display: inline-block;
                            transform: rotate(45deg);
                        }
                        &.close:after {
                            top: 11px;
                            right: 24px;
                            transform: rotate(225deg);
                        }
                    }
                    .doc-totalPage {
                        height: 18px;
                        line-height: 18px;
                        color: $--color-text-secondary;
                        padding-left: 39px;
                    }
                    .doc-pages {
                        padding-top: 13px;
                        padding-left: 39px;
                        li {
                            position: relative;
                            width: 130px;
                            margin-bottom: 14px;
                            &.current .doc-page img {
                                border: 1px solid $--border-color-light;
                                outline: 1px solid $--border-color-light;
                            }
                            .doc-page {
                                cursor: pointer;
                                img {
                                    border: 1px solid $--border-color-base;
                                    display: block;
                                    &:hover {
                                        border-color: $--border-color-light;
                                    }
                                }
                                .page-num-bg {
                                    position: absolute;
                                    bottom: 1px;
                                    left: 16px;
                                    width: 0;
                                    height: 0;
                                    border-top: 18px solid transparent;
                                    border-left: 18px solid $--border-color-light;
                                    border-right: 18px solid transparent;
                                    border-bottom: 18px solid $--border-color-light;
                                    margin-left: -15px;
                                }
                                .page-num {
                                    position: absolute;
                                    bottom: 6px;
                                    left: 2px;
                                    width: 15px;
                                    height: 15px;
                                    text-align: center;
                                    color: $--color-white;
                                    font-size: 11px;
                                }
                            }
                        }
                    }
                }
            }
            .net-error-container{
                position: relative;
                margin: 0 auto;
                text-align: center;
                width: 82%;
                height: 100%;
                background-color: $--color-white;
                border: 1px solid $border-color;
                overflow: auto;
                .tip-img{
                    margin: 185px auto 0 ;
                    width: 60px;
                    height: 70px;
                    background: {
                        image: url("~img/view/net-error.png");
                        repeat: no-repeat;
                        size: 100% auto
                    }
                }
                h4{
                    margin: 15px 0 10px 0;
                    font-size: 18px;
                }
                p{
                    font-size: 14px;
                    line-height: 20px;
                }
            }
        }
        .footer {
            box-sizing: border-box;
            position: fixed;
            bottom: 0;
        }
        .riding-seals {
            position: absolute;
            box-sizing: border-box;
            padding-top: 1px;
            width: 160px;
            border: 1px dashed $theme-color;
            background: rgba(18, 127, 210, 0.05);
            top: 0;
            bottom: 0;
            right: -116px;
            z-index: 1;
            height: 100%;

            &-bg {
                margin-left: 41px;
                width: 116px;
                height: 100%;
                background: $--background-color-secondary;
                background-image: linear-gradient(270deg, rgba(255, 255, 255, 0.50) 0%, rgba(217, 217, 217, 0.50) 100%);
                background-size: 23px;
            }
        }
    }
    // 移动端样式
    .view-doc-wrap.view-doc-wrap-mobile {
        .wrap {
            top: 60px;
            bottom: 10px;
        }
        .documents-container .documents-content {
            margin-right: 0;
        }
        .header {
            padding-left: 0;
            text-align: center;
        }
    }

</style>
