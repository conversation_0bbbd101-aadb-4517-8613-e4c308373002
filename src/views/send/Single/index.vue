
<!--发起页面布局组件测试-->
<template>
    <Layout pageModule="tmpEdit" v-loading="loading">
        <template v-if="!loading && templatePermission.sendContract">
            <Header
                v-if="!isDataBoxPreview"
                slot="header"
                pageModule="tmpEdit"
                :title="$t('configTemplate.use')"
                :steps="stepModulesInfo.map(module => module.title)"
                :activeStepIndex="currentStep"
                :nextBtnText="nextBtnText"
                @to-back="toBack"
                @to-next="toNext"
            >
                <template slot="right">
                    <CheckCustoms ref="checkCustoms" :draftId="draftId" v-if="showCustomsCheckBtn" />
                    <el-button
                        v-if="approvalBtnVisible"
                        type="primary"
                        @click="toNext('approval')"
                    >{{ $t('field.approval') }}</el-button>
                    <el-button
                        class="white-btn"
                        v-if="showSavePartlyBtn"
                        type="text"
                        @click="checkIsNeedReminder"
                    >{{ $t('templateCommon.tempSave') }}
                    </el-button>

                    <!-- 自动拖章组件 -->
                    <AutoStampComponent :draftId="draftId" :templateId="originTemplateId" />

                    <el-button
                        v-show="nextVisible"
                        type="primary"
                        @click="toNext('send')"
                    >{{ nextBtnText }}</el-button>
                </template>
            </Header>
            <component
                ref="bodyContent"
                :key="stepModule"
                :is="stepModulesInfo[currentStep].comp"
                :isDynamic="isDynamic"
                :isOfdTemplate="isOfdTemplate"
            ></component>
            <TemporaryReminder
                :visible="showTemporaryReminder"
                @close="closeReminder"
            />
        </template>
    </Layout>
</template>

<script>
import Layout from 'components/layout';
import Header from 'components/sendHeader';
import PointPosition from 'components/sendPointPosition';
import TemporaryReminder from 'components/temporarySaveReminder';
import UploadContract from './Contract';
import ConfigReceiver from './Receiver';
import CheckCustoms from './CheckCustoms';
import AutoStampComponent from './AutoStampComponent';
import { getTemplateSceneConfig } from 'src/api/template/detail';
import { mapActions, mapGetters, mapState, mapMutations } from 'vuex';
import { getDraftOriginTemplateId } from '@/api/send';
export default {
    name: 'SingleSend',
    components: {
        Layout,
        Header,
        UploadContract,
        ConfigReceiver,
        PointPosition,
        CheckCustoms,
        TemporaryReminder,
        AutoStampComponent,
    },
    data() {
        return {
            draftId: this.$route.params.draftId,
            stepModule: this.$route.params.step || 'upload', // upload
            stepModulesInfo: [{
                path: 'upload',
                title: this.$t('configTemplate.configContract'),
                comp: 'UploadContract',
                sensorsTitle: '设置文档',

            }, {
                path: 'receiver',
                title: this.$t('configTemplate.configReceiver'),
                comp: 'ConfigReceiver',
                sensorsTitle: '配置签约方',
            }, {
                path: 'position',
                title: this.$t('configTemplate.pointPosition'),
                comp: 'PointPosition',
                sensorsTitle: '指定签署位置',
            }],
            currentStep: 0,
            loading: true,
            returnUrl: '',
            isNew: this.$route.query.isNew === 'true', // 是否是新创建模板
            isCancel: this.$route.query.isCancel === 'true', // 是否是作废合同
            isDynamic: this.$route.query.isDynamic === 'true',
            isOfdTemplate: this.$route.query.isOfdTemplate === 'true',
            isDataBoxPreview: this.$route.query.isDataBoxPreview === 'true', // 是否是档案+合同预览
            isWebSend: this.$route.query.sendType === 'template', // 是否web使用模板发送
            showCustomsCheckBtn: false, // 海关检测场景定制
            showTemporaryReminder: false, // 暂存提示弹窗
            originTemplateId: '',
        };
    },
    computed: {
        ...mapGetters(['getSsoConfig', 'checkFeat', 'getIsForeignVersion']),
        ...mapState({
            templatePermission: state => state.template.templatePermissions,
        }),
        ...mapState('template', {
            contractList: state => state.contractList,
        }),
        nextBtnText() {
            return this.currentStep === 2 ? (this.$route.query.sendType === 'ssoConfirm' ? '保存' : this.$t('field.send')) : this.$t('configTemplate.nextStep');
        },
        // 单点登录模板预览页（新）配置
        ssoTplPrvw() {
            return this.getSsoConfig.tplPrvw_new || {};
        },
        // 下一步按钮是否显示
        nextVisible() {
            // 判断在模板指定位置页，默认显示（单点登录控制隐藏）
            return (this.currentStep !== 2 || (!this.ssoTplPrvw.tplPrvw_new_1_send || this.ssoTplPrvw.tplPrvw_new_1_send.visible));
        },
        // 发送完成后跳转链接
        doneBtnRedirectUrl() {
            let result = '';
            const returnUrl = this.returnUrl;
            const configUrl = this.ssoTplPrvw.tplPrvw_new_dg_btn_done ? this.ssoTplPrvw.tplPrvw_new_dg_btn_done.url : '';
            const configAbsUrl = this.ssoTplPrvw.tplPrvw_new_dg_btn_done ? this.ssoTplPrvw.tplPrvw_new_dg_btn_done.absUrl : '';

            if (returnUrl) {
                result = returnUrl;
            } else if (configUrl) {
                result = configUrl;
            } else if (configAbsUrl) {
                result = configAbsUrl;
            }

            return result;
        },
        // 提交审批按钮是否显示
        approvalBtnVisible() {
            // 判断在模板预览页并且单点登录配置了显示
            return (this.currentStep === 2 && (this.ssoTplPrvw.tplPrvw_new_1_apv && this.ssoTplPrvw.tplPrvw_new_1_apv.visible));
        },
        isResendDraft() {
            const resendDraftIdList = this.$localStorage.get('resendDraftIdList');
            return resendDraftIdList && resendDraftIdList.includes(this.draftId);
        },
        showSavePartlyBtn() {
            // 乐高城开启开关，ofd、作废、重新发起、本地发起不支持，sso发送不支持
            return this.checkFeat.sendContractFromCache && !this.isCancel && !this.isOfdTemplate && !this.isResendDraft && !this.isNew &&
                this.isWebSend;
        },
    },
    watch: {
        $route: {
            handler(v) {
                this.currentStep = this.stepModulesInfo.findIndex(a => a.path === v.params.step);
            },
            immediate: true,
        },
        contractList(v) {
            if (v.length) {
                // 当文件变化时重置海关检测按钮状态
                if (this.$refs.checkCustoms) {
                    this.$refs.checkCustoms.resetStatus();
                }
            }
        },
    },
    methods: {
        ...mapActions('template', ['getTemplatePermission', 'getTemplateConfigInfo', 'getDateFormatTypesInfo', 'getInternalSignInfoVO']),
        ...mapMutations('template', ['initTemplateOptInfo']),
        async init() {
            this.initTemplateOptInfo({
                templateId: this.draftId,
                isBatchSend: false,
                templateStatus: 'use',
                isCancel: this.isCancel, // 作废合同
            });
            this.getDraftBaseTemplateId();
            this.getDateFormatTypesInfo({ isGamma: this.$hybrid.isGamma() });
            // 普通发起进来不走权限控制
            const isAuthDefault = this.isNew || this.isCancel;
            if (!isAuthDefault) {
                try {
                    await this.getTemplateConfigInfo(this.draftId);
                    await this.getTemplatePermission(this.draftId);
                } catch (e) {
                    console.log('获取模板权限异常', e);
                }
            }

            const permissionCheck = this.sendContractPermissionCheck();
            // 没有发送权限无法进入页面
            if (permissionCheck) {
                this.loading = false;
                this.getReturnUrl();
            }
            this.getInternalSignInfoVO(this.draftId);
        },
        getDraftBaseTemplateId() {
            getDraftOriginTemplateId(this.draftId).then(res => {
                if (res.data) {
                    this.originTemplateId = res.data;
                    this.getCustomsPermission(res.data); // 通过原模板id查场景定制数据
                }
            });
        },
        // 获取场景定制的权限，控制海关检测按钮显示
        getCustomsPermission(originTemplateId) {
            getTemplateSceneConfig(originTemplateId)
                .then(({ data }) => {
                    const { scenes } = data;
                    const { optionType } = scenes.find(item => item.sceneType === 'VALID_PDF_FILE_FOR_CUSTOMS');
                    this.showCustomsCheckBtn = optionType === 'OPEN';
                });
        },
        // 检查发送权限
        sendContractPermissionCheck() {
            let checkResult = true;
            if (!this.templatePermission.sendContract) {
                checkResult = false;
                this.$MessageToast.error(this.$t('configTemplate.noSendContract'));
            }
            return checkResult;
        },
        toNext(nextType) {
            if (nextType === 'send') {
                this.$sensors.track({
                    eventName: 'Ent_ContractSendDetail_BtnClick',
                    eventProperty: {
                        page_name: this.stepModulesInfo[this.currentStep].sensorsTitle,
                        template_type: this.isDynamic ? '动态模板' : '静态模板',
                        first_category: '顶部导航栏',
                        is_batch_send: false,
                        icon_name: this.currentStep === 2 ? '发送' : '下一步',
                    },
                });
            }
            if (this.checkContractNumber()) {
                this.$refs.bodyContent.toNext({ redirectUrl: this.doneBtnRedirectUrl, nextType });
            }
        },
        // 检查文档数是否超过30
        checkContractNumber() {
            if (this.currentStep === 0 && this.contractList.length > 30) {
                this.$MessageToast.error(this.$t('configTemplate.contractListAbove30'));
                return false;
            }
            return true;
        },
        toBack() {
            this.$sensors.track({
                eventName: 'Ent_ContractSendDetail_BtnClick',
                eventProperty: {
                    page_name: this.stepModulesInfo[this.currentStep].sensorsTitle,
                    template_type: this.isDynamic ? '动态模板' : '静态模板',
                    first_category: '顶部导航栏',
                    is_batch_send: false,
                    icon_name: '返回',
                },
            });
            this.$refs.bodyContent.toBack();
        },
        // 获取returnUrl
        getReturnUrl() {
            this.returnUrl =  this.$route.query.returnUrl ? this.$route.query.returnUrl : '';
        },
        delStashDraftId() {
            const draftId = this.draftId;
            const resendDraftIdList = this.$localStorage.get('resendDraftIdList');

            if  (this.isResendDraft) {
                let arr = [];
                try {
                    arr = resendDraftIdList.split(',');
                } catch (error) {
                    console.log(error);
                }

                const idIndex = arr.findIndex(function(value) {
                    return value === draftId;
                });
                arr.splice(idIndex, 1);

                this.$localStorage.set('resendDraftIdList', arr);
            }
        },
        checkIsNeedReminder() {
            this.$sensors.track({
                eventName: 'Ent_ContractSendDetail_BtnClick',
                eventProperty: {
                    page_name: this.stepModulesInfo[this.currentStep].sensorsTitle,
                    template_type: this.isDynamic ? '动态模板' : '静态模板',
                    first_category: '顶部导航栏',
                    is_batch_send: false,
                    icon_name: '暂存并退出',
                },
            });
            const notReminderSave = window.localStorage.getItem('notReminderSave');
            // 如果没有不再提醒就展示弹窗,日本版不展示弹框
            if (notReminderSave === 'true' || this.getIsForeignVersion) {
                this.toNext('savePartly');
            } else {
                this.showTemporaryReminder = true;
            }
        },
        closeReminder(notReminder) {
            this.showTemporaryReminder = false;
            window.localStorage.setItem('notReminderSave', notReminder);
            this.toNext('savePartly');
        },
    },
    created() {
        this.$http.addBizPoint('SEND_CONTRACT', this.draftId, true);
        this.init();
    },
    beforeDestroy() {
        this.delStashDraftId();
    },
};
</script>

<style lang="scss">
.white-btn {
    color: $--color-white;
}
</style>
