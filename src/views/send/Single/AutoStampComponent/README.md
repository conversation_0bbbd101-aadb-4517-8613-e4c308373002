# 自动拖章组件 (AutoStampComponent)

## 功能概述

自动拖章组件是一个用于在合同发送流程中提供自动拖章功能的Vue组件。该组件只有在满足特定条件时才会显示，并提供了完整的任务提交、进度监控和状态反馈功能。

## 显示条件

组件只有在同时满足以下两个条件时才会显示：

1. **功能权限检查**: `features` 数组中包含 `'244'`
2. **配置状态检查**: AIAgent组件已经提交过配置（通过API检查拖章规则配置状态）

## 主要功能

### 1. 任务提交
- 点击"自动拖章"按钮后，调用 `/template-api/draft/{draftId}/start-stamp-recommendation-task` 接口
- 获取任务ID (taskId) 用于后续状态查询

### 2. 状态轮询
- 每2秒调用 `/template-api/draft/{draftId}/task/{taskId}/stamp-recommendation-task-status` 接口
- 监控任务状态：`IN_PROGRESS`（进行中）、`COMPLETED`（已完成）、`FAIL`（失败）

### 3. 进度显示
- 显示模拟进度条（从0%开始，最多到90%）
- 任务完成后进度条显示100%

### 4. 结果处理
- **成功**: 显示成功消息，关闭弹框，通知父组件刷新文档视图
- **失败**: 显示错误消息，关闭弹框，重置状态

## 组件属性 (Props)

| 属性名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| draftId | String | 是 | 草稿ID，用于API调用 |
| templateId | String | 是 | 模板ID，用于检查配置状态 |

## 组件事件 (Events)

| 事件名 | 说明 | 参数 |
|--------|------|------|
| taskComplete | 任务完成时触发 | 无 |

## API接口

### 1. 检查配置状态
```javascript
GET /template-api/templates/{templateId}/stamp-recommendation/rule
```

### 2. 提交拖章任务
```javascript
POST /template-api/draft/{draftId}/start-stamp-recommendation-task
Response: { result: "taskId" }
```

### 3. 查询任务状态
```javascript
GET /template-api/draft/{draftId}/task/{taskId}/stamp-recommendation-task-status
Response: { result: "status" } // IN_PROGRESS, COMPLETED, FAIL
```

## 使用示例

```vue
<template>
    <AutoStampComponent 
        :draftId="draftId" 
        :templateId="templateId" 
        @taskComplete="handleTaskComplete"
    />
</template>

<script>
import AutoStampComponent from './AutoStampComponent';

export default {
    components: {
        AutoStampComponent
    },
    data() {
        return {
            draftId: 'your-draft-id',
            templateId: 'your-template-id'
        };
    },
    methods: {
        handleTaskComplete() {
            // 处理任务完成后的逻辑，如刷新文档视图
            this.refreshDocumentView();
        }
    }
};
</script>
```

## 注意事项

1. 组件会在挂载时自动检查配置状态
2. 任务提交失败时会自动关闭弹框并显示错误消息
3. 组件销毁时会自动清理所有定时器
4. 进度条显示的是模拟进度，真实进度由任务状态决定
5. 任务完成后会通知父组件刷新文档视图

## 测试

组件包含完整的单元测试，覆盖以下场景：
- 显示条件检查
- 任务提交流程
- 状态轮询
- 错误处理
- 用户交互
