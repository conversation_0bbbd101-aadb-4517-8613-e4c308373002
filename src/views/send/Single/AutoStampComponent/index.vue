<template>
    <div class="auto-stamp-component">
        <!-- 自动拖章按钮 -->
        <el-button
            type="primary"
            @click="handleAutoStamp"
        >
            自动拖章
        </el-button>

        <!-- 自动拖章进度弹框 -->
        <el-dialog
            :visible.sync="dialogVisible"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            custom-class="auto-stamp-dialog"
            width="500px"
            :append-to-body="true"
        >
            <div class="auto-stamp-content">
                <div class="stamp-info">
                    <p class="stamp-title">
                        拖章Agent正依据《xxx》《yyy》内容为签署人指定盖章处。拖章完成后，请验收结果。若符合您的预期，请点击"发送"按钮，正式发出此份合同。
                    </p>
                </div>
                
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-text">进度条56%</div>
                        <div class="progress-container">
                            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                        </div>
                    </div>
                </div>

                <div class="dialog-footer">
                    <el-button 
                        type="primary" 
                        @click="handleCancel"
                        class="cancel-btn"
                    >
                        取消自动拖章
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'AutoStampComponent',
    data() {
        return {
            dialogVisible: false,
            progressPercentage: 56,
            timer: null,
        };
    },
    methods: {
        handleAutoStamp() {
            this.dialogVisible = true;
            this.startProgress();
        },
        
        handleCancel() {
            this.dialogVisible = false;
            this.stopProgress();
            this.progressPercentage = 56; // 重置进度
        },
        
        startProgress() {
            // 模拟进度更新
            this.timer = setInterval(() => {
                if (this.progressPercentage < 100) {
                    this.progressPercentage += Math.random() * 5;
                    if (this.progressPercentage > 100) {
                        this.progressPercentage = 100;
                    }
                } else {
                    this.stopProgress();
                    // 进度完成后可以添加完成逻辑
                    this.handleComplete();
                }
            }, 1000);
        },
        
        stopProgress() {
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        
        handleComplete() {
            // 拖章完成后的处理逻辑
            this.$message.success('自动拖章完成');
            this.dialogVisible = false;
        },
    },
    
    beforeDestroy() {
        this.stopProgress();
    },
};
</script>

<style lang="scss" scoped>
.auto-stamp-component {
    display: inline-block;
    margin: 0 8px;
}

.auto-stamp-content {
    padding: 20px 0;
    
    .stamp-info {
        margin-bottom: 30px;
        
        .stamp-title {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            text-align: left;
        }
    }
    
    .progress-section {
        margin-bottom: 30px;
        
        .progress-bar {
            .progress-text {
                font-size: 14px;
                color: #333;
                margin-bottom: 15px;
                text-align: center;
            }
            
            .progress-container {
                width: 100%;
                height: 8px;
                background-color: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;
                
                .progress-fill {
                    height: 100%;
                    background-color: #409eff;
                    border-radius: 4px;
                    transition: width 0.3s ease;
                }
            }
        }
    }
    
    .dialog-footer {
        text-align: center;
        
        .cancel-btn {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;
            padding: 12px 30px;
            font-size: 14px;
            border-radius: 4px;
            
            &:hover {
                background-color: #66b1ff;
                border-color: #66b1ff;
            }
        }
    }
}
</style>

<style lang="scss">
.auto-stamp-dialog {
    .el-dialog__header {
        display: none;
    }
    
    .el-dialog__body {
        padding: 30px 40px;
    }
}
</style>
