<template>
    <div class="point-position-page">
        <PointPositionSite v-if="canAdd !== 'none'" :canAdd="canAdd" @site-drag-start="onDragStart"></PointPositionSite>
        <PointPositionDoc
            :canAdd="canAdd"
            :canDrag="canDrag"
            :canSendlabelEdit="true"
            :templateId="templateId"
            :iconDragStatus="dragStatus"
            :float="float"
            :holderTriggerList="holderTriggerList"
            @save-meta="onMetaSave"
            @up-same-label="upSameLabel"
            @change-doc="changeDoc"
        ></PointPositionDoc>
        <PointPositionMiniDoc
            :documents="docList"
            @change-page="changePage"
            @change-doc="changeDoc"
            @next-sign-position="nextSignPosition"
        />
        <!-- 编辑区 -->
        <LabelEdit @save-meta="onMetaSave" :canChangeReceiver="true"></LabelEdit>
        <!-- 鼠标跟随icon -->
        <div id="flying-icon"
            class="flying-icon"
            v-show="float.show"
            :class="float.class"
        >
        </div>
        <!-- 保存前错误提醒dialog -->
        <LabelLackTip
            :tipObject="tipObject"
            :show.sync="dialogTip"
            @confirm="saveTemplate(true)"
            :title="$t('pointPosition.saveTemplateTip')"
            :confirmButtonText="$t('pointPosition.save')"
            :canChangeDoc="true"
            @change-doc-by-id="changeDocById"
        ></LabelLackTip>
        <ConfigGuide key="position-guide"
            :visible.sync="showUserGuideDialog"
            :isFinal="isFinalSave"
            :originTemplateId="originTemplateId"
            guidePage="POSITION"
        ></ConfigGuide>
        <SendedEditGuide :visible.sync="sendedEditGuideVisible"
            :editor-name="sendedEditor"
        ></SendedEditGuide>
    </div>
</template>
<script>
import PointPositionSite from 'components/pointPositionSite';
import PointPositionDoc from 'components/pointPositionDoc';
import PointPositionMiniDoc from 'components/pointPositionMiniDoc';
import LabelEdit from 'components/labelEdit';
import LabelLackTip from 'components/labelLackTip';
import SendedEditGuide from 'components/sendedEditGuide';
import { getDocumentLabels, saveTemplate, validTemplate, fdaOpened } from 'src/api/template/pointPosition.js';
import { initWatermark, initRidingSeal } from 'utils/decorate.js';
import { pointPositionMixin } from 'src/mixins/pointPosition.js';
import { mapGetters, mapMutations, mapState, mapActions } from 'vuex';
import get from 'lodash/get';
import ConfigGuide from 'views/template/Config/ConfigGuide';
import { goReturnUrl } from 'pub-utils/business/returnUrl.js';

export default {
    components: {
        ConfigGuide,
        PointPositionSite,
        PointPositionDoc,
        LabelEdit,
        LabelLackTip,
        PointPositionMiniDoc,
        SendedEditGuide,
    },
    mixins: [pointPositionMixin],
    data() {
        return {
            canAdd: 'all', // 'all','some','none'
            canDrag: true,
            // operateMarkId: this.$route.query.operateMarkId, // 从档案柜发送模板跳过来会有此参数
            // templateMatchId: this.$route.query.templateMatchId,
            tipObject: {
                receivers: [],
                documents: [],
                errorMsg: [],
            },
            sendedEditGuideVisible: false,
            sendedEditor: '',
            dialogTip: false,
            holderTriggerList: [], // 智能解析坐标
            showUserGuideDialog: false, // 是否展示快捷配置引导
            isFinalSave: false, // 是否是最后一步保存
            returnUrl: ['null', ''].includes(this.$route.query.returnUrl) ? null : this.$route.query.returnUrl,
            isSavingTemplate: false, // 是否正在保存
            enterTime: 0,
            isDynamic: this.$route.query.isDynamic === 'true',
        };
    },
    computed: {
        ...mapState('template', {
            templateId: state => state.templateId,
            isCancel: state => state.templateIsCancel,
            isJumpFromUserGuide: state => state.isJumpFromUserGuide,
            originTemplateId: state => state.originTemplateId,
        }),
        ...mapGetters(['getSsoConfig']),
    },
    methods: {
        ...mapActions('template', ['getInternalSignInfoVO', 'findCrossPlatform']),
        ...mapMutations('template', ['setBlankDocument', 'setTemplateId', 'setIsJumpFromUserGuide', 'setFdaConfig']),
        // 保存模板前校验
        async toNext() {
            try {
                const { data } = await validTemplate();

                if (data.editorRoleName) {
                    this.sendedEditor = data.editorRoleName;
                    this.sendedEditGuideVisible = true;
                    return;
                }
                const hasSignTypeError = data.signTypeError.needSeal.length ||  data.signTypeError.needSignature.length || data.signTypeError.needSealSignature.length;
                if (data.receivers.length || data.documents.length || data.errorMsg.length || hasSignTypeError || data.allEmptyReceivers.length) {
                    this.dialogTip = true;
                    this.tipObject = data;
                } else {
                    this.saveTemplate();
                }
            } catch (error) {
                console.log(error);
            }
        },
        getMarksType() {
            const marksType = [];
            const mapObject =  {
                'SEAL': '盖章',
                'SIGNATURE': '签字',
                'CONFIRMATION_REQUEST_SEAL': '',
                'DATE': '签署日期',
                'TEXT': '文本',
                'TEXT_NUMERIC': '数字',
                'NUMERIC_VALUE': '数值',
                'CONFIRMATION_REQUEST_REMARK': '不符合章备注',
                'SINGLE_BOX': '单选框',
                'MULTIPLE_BOX': '复选框',
                'PICTURE': '图片',
                'BIZ_DATE': '日期',
                'DATE_TIME': '时刻',
                'COMBO_BOX': '下拉框',
                'QR_CODE': '二维码',
            };
            this.docList.map(doc => {
                doc.labels.map(label => {
                    marksType.push(mapObject[label.labelType]);
                });
            });
            return Array.from(new Set(marksType));
        },
        // getErrorList() {
        //     const errorList = [];
        //     const receivers = this.receivers;
        //     const docList = this.docList;
        //     // 先判断下是否有签署人没有印章和签名
        //     receivers.forEach((receiver) => {
        //         const { receiverId, showName, userType, signType } = receiver;
        //         const fileList = [];
        //         docList.forEach((doc, docIndex) => {
        //             const docLabels = doc.labels;
        //             let hasMarked = this.isMarked(docLabels || [], receiverId, ['SEAL', 'SIGNATURE']);
        //             let hasPartedMarked = false; // 完成了盖章或签字
        //             if (signType === 'SEAL_AND_SIGNATURE') {
        //                 const hasMarkedSeal = this.isMarked(docLabels || [], receiverId, ['SEAL']);
        //                 const hasMarkedSignature = this.isMarked(docLabels || [], receiverId, ['SIGNATURE']);
        //                 hasMarked =  hasMarkedSeal && hasMarkedSignature; // 拖了盖章和签字
        //                 hasPartedMarked = (hasMarkedSeal || hasMarkedSignature); // 拖了章
        //             }
        //             if (!hasMarked) {
        //                 fileList.push({
        //                     fileName: doc.fileName,
        //                     docIndex,
        //                     hasPartedMarked,
        //                 });
        //             }
        //         });
        //         if (fileList.length) {
        //             errorList.push({
        //                 receiverId,
        //                 userName: showName,
        //                 userType,
        //                 fileList,
        //                 signType,
        //             });
        //         }
        //     });
        //     return errorList;
        // },
        // isMarked(labels, receiverId, types) {
        //     return  labels.some(label => {
        //         return (label.receiverId === receiverId) && types.includes(label.labelType);
        //     });
        // },
        // 保存模板
        saveTemplate(isFromLackTip) {
            // 防止保存接口多次调用
            if (this.isSavingTemplate || this.showUserGuideDialog) {
                return;
            }
            if (isFromLackTip) {
                this.dialogTip = false;
            }
            const loadingInstance = this.$loading();
            this.isSavingTemplate = true;
            // 二次确认弹窗
            return saveTemplate()
                .then(() => {
                    loadingInstance.close();
                    this.isSavingTemplate = false;
                    this.$MessageToast.success(this.$t('pointPosition.saveSuc')).then(() => {
                        this.$sensors.track({
                            eventName: 'Ent_TemplateCreate_Result',
                            eventProperty: {
                                page_name: '指定签署位置',
                                template_type: this.isDynamic ? '动态模板' : '静态模板',
                                first_category: '顶部导航栏',
                                module_list: this.getMarksType(),
                                is_success: true,
                                icon_name: '保存模板',
                            },
                        });
                        // 单点登录模板编辑页配置
                        const ssoTmpEditSaveToUrl = get(this.getSsoConfig, 'tmpEdit.tmpEdit_1_save.url', '');
                        if (ssoTmpEditSaveToUrl) {
                            return this.$router.push(ssoTmpEditSaveToUrl);
                        }
                        /* 如果客户定义了跳转地址，则首先跳转 */
                        if (this.returnUrl) {
                            goReturnUrl(this.returnUrl);
                            return;
                        }
                        // 作废合同保存后到作废tab页
                        if (this.isCancel) {
                            const invalidStatementOriginal = JSON.parse(this.$cookie.get('invalidStatementOriginal'));
                            return this.$router.push(`/template/${invalidStatementOriginal.templateId}/list/invalidStatement?templateName=${invalidStatementOriginal.templateName}`);
                        }
                        this.showUserGuideDialog = true;
                        this.isFinalSave = true;
                    });
                }).catch(({ response, config, message }) => {
                    this.$sensors.track({
                        eventName: 'Ent_TemplateCreate_Result',
                        eventProperty: {
                            page_name: '指定签署位置',
                            template_type: this.isDynamic ? '动态模板' : '静态模板',
                            first_category: '顶部导航栏',
                            module_list: this.getMarksType(),
                            is_success: false,
                            request_url: config.url,
                            fail_reason: response?.data?.message || message,
                            fail_error_code: response?.data?.code,
                            fail_http_code: response?.status,
                            icon_name: '保存模板',
                        },
                    });
                    loadingInstance.close();
                    this.isSavingTemplate = false;
                    // 编辑模板时，父模板已经被删除，则转到列表页
                    if (response.data?.code === '160008') {
                        this.$router.push('/template/list');
                    }
                });
        },
        // ai 分析签名位置
        analyzePositions() {
            this.$http.post(`${tempPath}/templates/${this.templateId}/ai-analysis-positions`)
                .then(res => {
                    this.holderTriggerList = this.initTriggersData(res.data.documents);
                });
        },
        // 初始化智能坐标
        initTriggersData(data) {
            const triggers = [];
            let sum = 0;
            // 格式化成想要的数据格式
            data.forEach((doc) => {
                doc.pages.forEach((page) => {
                    page.positions.forEach((pos) => {
                        triggers.push({
                            ...pos,
                            documentId: doc.documentId,
                            pageNum: page.pageNum,
                            triggerId: sum,
                        });

                        sum++;
                    });
                });
            });

            return triggers;
        },
    },
    mounted() {
        this.enterTime = new Date().getTime();
        this.getInternalSignInfoVO(this.$route.params.templateId); // 获取对内文件信息
        this.setTemplateStatus('edit');
        this.setTemplateId(this.$route.params.templateId);
        this.findCrossPlatform(this.$route.params.templateId); // 判断是否跨平台合同
        // this.analyzePositions();
        const loadingInstance = this.$loading();
        Promise.all([getDocumentLabels(), fdaOpened()])
            .then(([{ data: documentData }, { data: fdaData }]) => {
                // 数据的receiver包含补全人，补全人不用展示在签署方列表里
                this.setReceivers(documentData.receivers.filter(receiver => receiver.receiverType !== 'EDITOR'));
                this.setEditors(documentData.receivers.filter(receiver => receiver.receiverType === 'EDITOR'));
                this.setDocList(this.initDoc(documentData.documents));
                this.setWatermarkList(initWatermark(documentData.decorate.watermarks, documentData.receivers));
                this.setRidingSealList(initRidingSeal(documentData.decorate.ridingSeals, documentData.receivers));
                this.setFdaConfig(fdaData);
            }).finally(() => {
                loadingInstance.close();
                this.isFinalSave = false;
                this.showUserGuideDialog = this.isJumpFromUserGuide; // 根据store中的标记决定是否直接展示引导弹窗
                this.setIsJumpFromUserGuide(false);
            });
        this.$sensors.track({
            eventName: 'Ent_TemplateCreate_PageView',
            eventProperty: {
                page_name: '指定签署位置',
                template_type: '静态模板',
            },
        });
    },
    beforeDestroy() {
        this.$sensors.track({
            eventName:
                'Ent_TemplateCreate_PageLeave',
            eventProperty: {
                page_name: '指定签署位置',
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
};
</script>
<style lang="scss">
.point-position-page{
    height: 100%;
    position: relative;
    overflow-x: hidden;
    .flying-icon {
        z-index: 9999;
        pointer-events: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 28px;
        height: 28px;
        line-height: 27px;
        text-align: center;
        font-size: 18px;
        background-color: $--color-white;
        border-radius: 4px;
        box-shadow:1px 1px 13px $--border-color-base, -1px 1px 13px $--border-color-base;
    }
}

</style>
