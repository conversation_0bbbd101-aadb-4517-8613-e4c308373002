import applyPermission from '../index.vue';
import { initWrapper } from 'src/testUtils';
import flushPromises from 'flush-promises';
jest.mock('src/api/sign/applyPermission.js', () => {
    return {
        getAdminInfo: jest.fn().mockResolvedValue({ data: { empName: 'empName', account: 'account' } }),
        getNoIndustryPackage: jest.fn().mockResolvedValue({ data: true }),
        postApplyRight: jest.fn().mockResolvedValue({ data: 'applyId' }),
        getDefaultRight: jest.fn().mockResolvedValue({ data: { value: { CLAIM: 0, SIGN: 2 } } }),
        getAllEnt: jest.fn().mockResolvedValue({ data: [{ entName: '企业名称', empName: 'empName' }] }),
        getIfSpecialSealRequired: jest.fn().mockResolvedValue({ data: { data: { ifSpecialSealRequired: false } } }),
    };
});
jest.mock('pub-mixins/checkBizLine.js', () => {
    return {
        bizLineMixin: {
            data() {
                return {
                    bizLineList: [{ entId: '1' }],
                    selectedLineEntId: null,
                };
            },
            methods: {
                checkHasBizLine: jest.fn().mockResolvedValue(true),
            },
        },
    };
});
jest.mock('const/const', () => ({
    CONTRACT_ALIAS_MAP: {
        'DOC': 'consts.contractAlias.doc',
        'LETTER': 'consts.contractAlias.letter',
        'PROOF': 'consts.contractAlias.proof',
        'AGREEMENT': 'consts.contractAlias.agreement',
        'SERVICE_REPORT': 'consts.contractAlias.service_report',
    },
}));
jest.mock('@/api/docManage/detail', () => ({
    getContractAlias: () => Promise.resolve({
        data: {
            displayedTextCode: 'DOC',
        },
    }),
}));
const mockPushFun = jest.fn();
function createWrapper(pageType = 'admin') {
    console.log('wwwww', pageType);
    window.sessionStorage.setItem('applyPermission', JSON.stringify({
        receiverEntName: '企业名称',
    }));
    return initWrapper(applyPermission,  {
        state: {
            commonHeaderInfo: {
                platformUser: {
                    account: '123',
                },
            },
        },
    }, {
        mocks: {
            $route: {
                params: {
                    pageType: pageType,
                },
                query: {
                    contractId: '123',
                },
            },
            $router: {
                push: mockPushFun,
            },
            $MessageToast: {
                error: jest.fn(),
            },
        },
        mixins: [bizLineMixin],
    });
}
import { bizLineMixin } from 'pub-mixins/checkBizLine.js';
describe('转交管理员/转他人认证/权限申请', () => {
    test('更多权限选择', () => {
        const wrapper = createWrapper();
        wrapper.vm.handleConfirmPermission({ CLAIM: 1, SIGN: 1 });
        expect(wrapper.vm.applyPrivilege).toEqual({
            CLAIM: 1,
            SIGN: 1,
        });
    });
    test('点击确定按钮，通知发送', async() => {
        const wrapper = createWrapper();
        wrapper.vm.receiverAccount = '***********';
        await flushPromises();
        wrapper.vm.handleNotice();
        await flushPromises();
        expect(mockPushFun).toHaveBeenCalledWith('/sign/apply-permission-result/admin?receiverAccount=***********&contractId=123');
    });
    test('点击确定按钮', async() => {
        let wrapper = createWrapper();
        wrapper.vm.receiverAccount = '***********';
        wrapper.vm.handleNotice('other');
        await flushPromises();
        expect(mockPushFun).toHaveBeenCalledWith('/sign/apply-permission-result/admin?receiverAccount=***********&contractId=123');

        mockPushFun.mockClear();
        wrapper = createWrapper('right');
        wrapper.vm.senderName = 'test';
        wrapper.vm.handleNotice();
        await flushPromises();
        expect(mockPushFun).toHaveBeenCalledWith('/sign/apply-permission-result/right?entAdminName=&entAdminAccount=&contractId=123');
    });
    test('申请成为主管理员', () => {
        const wrapper = createWrapper();
        wrapper.vm.applyToBeAdmin();
        expect(wrapper.vm.applyToBeAdminDialogShow).toBeTruthy();
    });
    test('页面销毁', () => {
        const wrapper = createWrapper();
        wrapper.destroy();
    });
    test('点击更多权限', () => {
        const wrapper = createWrapper();
        wrapper.vm.handleClickMoreRight();
        expect(wrapper.vm.morePermissionDialogShow).toBeTruthy();
    });
});
