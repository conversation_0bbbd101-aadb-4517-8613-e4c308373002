{"templateDetail.basic.docComId": "文档组合编号：", "templateDetail.basic.docComCreater": "文档组合创建人：", "templateDetail.basic.docComCreateTime": "文档组合创建时间：", "templateDetail.basic.docComRemark": "文档组合备注：", "templateDetail.basic.batchSendContract": "批量发送合同", "templateDetail.basic.singleSendContract": "单独发送合同", "templateDetail.basic.edit": "编辑", "templateDetail.basic.doc": "文档：", "templateDetail.basic.sender": "合同发件方：", "templateDetail.basic.senderAccount": "合同发件人：", "templateDetail.basic.receiver": "合同收件方：", "templateDetail.basic.receiverAccount": "合同收件人：", "templateDetail.basic.contractEndTime": "合同到期时间：", "templateDetail.basic.contractType": "合同类型：", "templateDetail.basic.entNo": "公司内部编号：", "templateDetail.basic.area": "所属区域：", "templateDetail.basic.senderField": "待发件方填写字段：", "templateDetail.basic.signerField": "待签署方填写字段：", "templateDetail.basic.contractInfo": "合同信息", "templateDetail.basic.pageIndex": "页数：{page}页", "templateDetail.basic.invalidStatementName": "与原模板保持一致", "templateDetail.msg.editSuccess": "修改成功", "templateDetail.msg.editFail": "修改失败", "templateDetail.msg.whiteDocument": "文档内容为空", "templatePermission.tempMgmt": "模板管理", "templatePermission.permissionMine": "我的授权", "templatePermission.permissionAll": "全部授权", "templatePermission.new": "新建", "templatePermission.search.composeName": "权限方案名称", "templatePermission.search.auth": "权限项", "templatePermission.search.name": "姓名", "templatePermission.search.account": "账号", "templatePermission.search.role": "角色", "templatePermission.table.permissionFederationName": "权限方案名称", "templatePermission.table.createPermissionFederation": "新建权限方案", "templatePermission.table.btnTooltips": "权限方案允许您建立一系列权限控制，并将权限方案应用到其他模板中。获得权限的人员才能对模板执行相关操作。", "templatePermission.table.editPermission": "编辑权限", "templatePermission.table.auth": "权限项", "templatePermission.table.authTo": "授权给", "templatePermission.table.operate": "操作", "templatePermission.table.authToEmployes": "被授权人员：", "templatePermission.table.authToRoles": "被授权角色：", "templatePermission.table.operateAuthTo": "授权", "templatePermission.dialog.authToRoles": "授权角色", "templatePermission.dialog.selectEnt": "选择企业", "templatePermission.dialog.selectRole": "选择角色", "templatePermission.dialog.selectedRole": "已选角色", "templatePermission.dialog.authToEmployes": "授权人员", "templatePermission.dialog.selectEmployes": "选择人员", "templatePermission.dialog.selectedEmployes": "已选人员", "templatePermission.dialog.recoverEmployesPermission": "同时收回这些人员已获得的其他权限", "templatePermission.dialog.recoverRolesPermission": "同时收回这些角色已获得的其他权限", "templatePermission.dialog.all": "全选", "templatePermission.slide.editComposeName": "权限方案名称", "templatePermission.slide.editPermission": "编辑权限", "templatePermission.slide.save": "保存", "templatePermission.slide.tip": "修改权限方案不影响已按原方案分配给成员的权限，他们仍保留原权限。", "templatePermission.slide.nameNotEmpty": "权限方案名称不能为空", "templatePermission.slide.checkNotEmpty": "权限勾选不能为空", "templatePermission.msg.editSuccess": "编辑完成", "templatePermission.msg.editFail": "编辑失败", "templatePermission.msg.createSuccess": "新建成功", "templatePermission.msg.createFail": "新建失败", "templatePermission.permissonAuthChoose.title": "选择要收回的权限", "templatePermission.permissonMember": "成员/角色权限", "templatePermission.mainAdminRole": "Main administrator", "templatePermission.employeeRole": "Employee", "addressBook.searchAll": "全选", "addReceiver.orderSignLabel": "Последовательное подписание ", "addReceiver.contactAddress": "Контактная адресная книга", "addReceiver.signOrder": "Порядок подписания ", "addReceiver.account": "Аккаунт ", "addReceiver.accountPlaceholder": "Телефон/электронная почта (требуется)", "addReceiver.accountReceptionCollection": "前台代收", "addReceiver.accountReceptionCollectionTip1": "不知道对方具体账号或对方没有账号，", "addReceiver.accountReceptionCollectionTip2": "请选择前台代收", "addReceiver.signSubjectPerson": "Тема подписи: Физическое лицо", "addReceiver.nameTips": "Ф. И. О.  (выборочное заполнение)", "addReceiver.requiredNameTips": "姓名（必填，用于签约身份核对）", "addReceiver.entOperatorNameTips": "Ф. И. О.  (выборочное заполнение)", "addReceiver.needAuth": "Нужно аутентифицироваться", "addReceiver.signSubjectEnt": "Тема подписи: Юридическое лицо ", "addReceiver.entNameTips": "Наименование компании (Выборочное заполнение)", "addReceiver.operator": "Исполнитель", "addReceiver.sign": "Подписать ", "addReceiver.more": "Больше", "addReceiver.messageAndFaceVerify": "刷脸+验证码校验", "addReceiver.messageAndFaceVerifyTips": "该用户需完成刷脸和验证码校验后，才能签署合同。刷脸签署前用户需完成实名认证，只支持大陆居民使用", "addReceiver.faceFirst": "Приоритетная чистка, резервное копирование кода подтверждения", "addReceiver.faceFirstTips": "При подписании система по умолчанию выполняет проверку лица. Когда число раз, когда кисть не проходит, достигает верхнего предела дня, она автоматически переключается на проверку кода проверки", "addReceiver.mustFace": "Обязательно подпишите с распознаванием лица ", "addReceiver.handWriteNotAllowed": "Рукописные подписи не допускаются", "addReceiver.mustHandWrite": "Обязательно подпишите рукой ", "addReceiver.fillIDNumber": "паспортные данные ", "addReceiver.fillNoticeCall": "номер телефона для уведомления ", "addReceiver.fillNoticeCallTips": "Введите номер телефона для уведомления ", "addReceiver.addNotice": "Добавить личное сообщение", "addReceiver.attachTips": "Требования к приложению", "addReceiver.faceSign": "Обязательно подпишите с распознаванием лица ", "addReceiver.faceSignTips": "Данный пользователь должен пройти аутентификацию лица, чтобы завершить подписание ", "addReceiver.handWriteNotAllowedTips": "Пользователь может выбрать только подпись, которая была установлена, или использовать подпись шрифта по умолчанию для завершения подписи", "addReceiver.handWriteTips": "Пользователю нужна рукописная подпись для завершения подписи", "addReceiver.idNumberTips": "Используется для подписи проверки личности", "addReceiver.verifyBefore": "Подтвердите личность перед просмотром файлов", "addReceiver.verify": "Подтвердить личность", "addReceiver.verifyTips": "максимум 20 букв", "addReceiver.verifyTips2": "Вы должны предоставить эту информацию для проверки данному пользователю", "addReceiver.sendToThirdPlatform": "Отправить платформе третьей стороне", "addReceiver.platFormName": "Название платформы", "addReceiver.fillThirdPlatFormName": "Введите название третьей платформы", "addReceiver.attach": "Прикрепленный файл ", "addReceiver.attachName": "Название приложении", "addReceiver.exampleID": "например: фото паспорта", "addReceiver.attachInfo": "Инструкция приложения", "addReceiver.attachInfoTips": "например:  загрузите фото паспорта", "addReceiver.addAttachRequire": "Добавить требования к вложению", "addReceiver.addSignEnt": "Добавить подпись компании ", "addReceiver.addSignPerson": "Добавить подпись физического лица ", "addReceiver.addCC": "添加抄送方", "addReceiver.addCCEnt": "抄送给企业成员", "addReceiver.addCCPerson": "抄送给个人用户", "addReceiver.addCCUser": "添加抄送人", "addReceiver.addSignUser": "添加签字人", "addReceiver.selectContact": "Выбрать контактное лицо", "addReceiver.save": "Сохранить", "addReceiver.searchVerify": "Проверка запроса", "addReceiver.fillImageContentTips": "Пожалуйста, заполните содержание изображения", "addReceiver.ok": "Подтвердить ", "addReceiver.findContact": "Найти следующих подписывающих сторон с договора", "addReceiver.signer": "Под<PERSON>и<PERSON><PERSON><PERSON>т ", "addReceiver.signerTips": "Маленькое примечание: после выбора подписанта, платформа поможет вам определить место подписи и  печати", "addReceiver.add": "Добавить ", "addReceiver.notAdd": "Не добавляйте", "addReceiver.cc": "Отправить копию ", "addReceiver.notNeedAuth": "Не требует аутентификаций", "addReceiver.extracting": "Извлечение…", "addReceiver.autoFill": "Автоматически заполнить данные подписанта", "addReceiver.failExtracting": "не получено подписавшей стороной", "addReceiver.idNumberForVerifyErr": "Неверный формат, пожалуйста, введите правильное данные паспорта", "addReceiver.noAccountErr": "Аккаунт не может быть пустым", "addReceiver.noUserNameErr": "姓名不能为空", "addReceiver.noIDNumberErr": "身份证号码不能为空", "addReceiver.accountFormatErr": "Неверный формат, пожалуйста, введите правильный номер телефона или E-mail.", "addReceiver.enterpriseNameErr": "Неверный формат, введите правильное название компании", "addReceiver.userNameFormatErr": "Неверный формат, пожалуйста, введите ваши правильное Ф.И.О.", "addReceiver.riskCues": "Предупреждение о риске", "addReceiver.riskCuesMsg": "Если подписавшая сторона подписывается не с настоящим именем, вам необходимо будет предоставить паспорт подписавшей стороны в случае возникновения спора. Чтобы избежать риска, выберите нужное настоящее имя.", "addReceiver.confirmBtnText": "Выбрать нужные данные", "addReceiver.cancelBtnText": "Выбрать не нужные данные", "addReceiver.attachLengthErr": "Вы можете добавить до 50 запросов на вложение только одному подписанту", "addReceiver.collapse": "Сложить", "addReceiver.expand": "Разверните", "addReceiver.delete": "Удалить", "addReceiver.saySomething": "Ком<PERSON><PERSON>н<PERSON><PERSON><PERSON><PERSON> (скажите что-нибудь)", "addReceiver.addImage": "Добавить фото", "addReceiver.addImageTips": "поддерживает pdf、word、excel、jpg、png, формат, максимум можно загрузить 10 шт.", "addReceiver.addSourceFile": "添加源文件", "addReceiver.addSourceFileTips": "支持word， excel、pdf，不可在线预览必须下载后才能查看，但下载后仍为word或excel，方便继续编辑。不超过{num}份。", "addReceiver.keepSourceFiles": "保持源文件", "addReceiver.keepSourceTip": "若选择保持源文件，则下载的文件格式与上传的格式保持一致，但无法在线预览，需下载后才能查看。", "addReceiver.itemRequire.1": "选项", "addReceiver.itemRequire.2": "必填", "addReceiver.give": "дайте", "addReceiver.fileMax": "количество отдачи превысило верхний предел", "addReceiver.needStamp": "企业签字要求签署方式为个人签字+企业盖章，该签约企业需含有签署方式为盖章的签约角色。", "addReceiver.role": "角色：", "addReceiver.skip": "知道了", "addReceiver.toSetting": "去设置", "batchImport.optTip.0": "请先", "batchImport.optTip.1": "，填写完成后，直接导入。（图片，附件，文档都需填入文件名+后缀，如xxx.jpg、xxx.doc）", "batchImport.optDecTip": "Excel中需要用到的图片、合同附件等文件，请压缩后点击【上传文件压缩包】上传，系统会按文件名称自动将文件匹配", "batchImport.excelTemplate": "Excel模板", "batchImport.downloadExcelTemplate": "下载Excel模板", "batchImport.contentFileImportSuccess": "以下文件内容和签署人已批量导入成功", "batchImport.batchImport": "批量导入", "batchImport.reBatchImport": "重新批量导入", "batchImport.zipImport": "上传文件压缩包", "batchImport.zipReimport": "重新上传文件压缩包", "batchImport.importView": "导入查看", "batchImport.documentsImport": "上传文档压缩包", "batchImport.documentsReimport": "重新上传文档压缩包", "batchImport.blankDecTip": "Excel中需要用到的文档，请压缩后点击【上传文档压缩包】上传，系统会按文件名称自动将文档匹配", "receiverItem.limitFaceConfigTip": "你的合同单价过低，该功能不可用，请联系上上签协商", "receiverItem.mutexMsg": "已设置“{msg}”，请先删除“{msg}”的设置后再选择", "receiverItem.batchImported": "已批量导入", "receiverItem.batchNotImported": "待批量导入", "receiverItem.batchCheckbox": "使用时可批量添加", "receiverItem.importedNum": "成功导入{batchNum}个签约方", "receiverItem.checkTooltip": "查看效果示意图", "receiverItem.caseDlgTitle": "勾选使用批量导入的效果示意图", "receiverItem.clickView": "点击查看", "receiverItem.entName": "公司名称", "receiverItem.entNameTips": "提示：签约方的企业名称完全一致才能签署", "receiverItem.userName": "经办人", "receiverItem.userNamePlace": "姓名（{necessary}）", "receiverItem.userAccount": "接收手机/邮箱", "receiverItem.userAccountDemand": "（最多支持10个，可用分号隔开）", "receiverItem.proxy": "对方前台代收", "receiverItem.addressBookTooltip": "联系人地址簿", "receiverItem.proxyTips": "不知道对方具体账号或对方没有账号，请选择前台代收", "receiverItem.dai": "代", "receiverItem.name": "姓名", "receiverItem.IDCard": "身份证号", "receiverItem.IDCardPlace": "用于签约身份核对", "receiverItem.addressBook": "联系人地址簿", "receiverItem.role": "签约角色", "receiverItem.rolePlace": "如员工/经销商", "receiverItem.byAddressBook": "由您的\"联系人地址薄\"带出", "receiverItem.error.userAccountLessThan": "接收手机/邮箱不能超过{num}个", "receiverItem.error.userAccountNotRepeat": "接收手机/邮箱不能重复", "receiverItem.error.entNameLessThan": "企业名称不能超过{num}个字符", "receiverItem.error.signerInContract": "该签署人加入合同中", "receiverItem.error.signerNotInContract": "该签署人不加入合同中", "receiverItem.invite": "邀请您的合作伙伴", "receiverItemHeader.contractDownloadControl": "合同下载码", "receiverItemHeader.signerPerson": "签约个人", "receiverItemHeader.signerEnt": "签约企业", "receiverItemHeader.needAuth": "需要实名", "receiverItemHeader.notNeedAuth": "不需要实名", "receiverItemHeader.needAuthEnt": "经办人需要实名", "receiverItemHeader.notNeedAuthEnt": "经办人不需要实名", "receiverItemHeader.sign": "签字", "receiverItemHeader.entSign": "企业签字", "receiverItemHeader.stamp": "盖章", "receiverItemHeader.stampSign": "盖章并签字", "receiverItemHeader.requestSeal": "业务核对章", "receiverItemHeader.cc": "抄送", "receiverItemHeader.signCheck": "签署校验", "receiverItemHeader.messageAndFaceVerify": "刷脸+验证码校验", "receiverItemHeader.faceFirst": "优先刷脸，备用验证码签署", "receiverItemHeader.faceMust": "必须刷脸签署", "receiverItemHeader.noHand": "使用上上签系统签名", "receiverItemHeader.mustHand": "必须手写签名", "receiverItemHeader.notify": "签约须知", "receiverItemHeader.handwritingRec": "开启笔迹识别", "receiverItemHeader.readAll": "阅读完毕再签署", "receiverItemHeader.dataCollect": "采集材料", "receiverItemHeader.attachDoc": "添加合同附属资料", "receiverItemHeader.mainDoc": "提交签约主体资料", "receiverItemHeader.attachDocTips": "提交的资料用于帮助您追踪合同履约状态，判断业务执行是否正常。设置后，该签署人必须按要求提交", "receiverItemHeader.mainDocTips": "提交的资料用于帮助您查验签约方的主体资质，判断是否可以与其开始或继续开展业务。如果签约方已提交过的相同资料可以不再重复提交", "receiverItemHeader.other": "其他", "receiverItemHeader.notifyOff": "关闭短信/邮件通知", "receiverItemHeader.notifyForeign": "使用外文通知", "receiverItemHeader.notifyForeignTips": "外文通知不支持短信，仅支持邮箱", "receiverItemHeader.pay": "成为付费方", "receiverItemHeader.ddl": "签署时效", "receiverItemHeader.encryptionSign": "使用加密签署", "receiverItemHeader.twoFactorAuthentication": "开启二要素认证", "receiverItemExtends.twoFactorAuthenticationTips": "该签署方需要完成二要素认证才能签署", "receiverItemExtends.encryptionSignTips": "使用加密签署（当前签署人需要通过该密码，才可以完成合同的签署。）", "receiverItemExtends.encryptionSignCode": "加密签署码", "receiverItemExtends.pleaseInput": "请输入4-8位数字", "receiverItemExtends.contractDownloadControl": "启用下载码", "receiverItemExtends.contractDownloadControlTips": "签约方在下载合同时，需填写下载码，发件方可在合同详情页中查看下载码。", "receiverItemExtends.messageAndFaceVerify": "刷脸+验证码校验", "receiverItemExtends.messageAndFaceVerifyTips": "该用户需完成刷脸和验证码校验后，才能签署合同。刷脸签署前用户需完成实名认证，只支持大陆居民使用", "receiverItemExtends.faceFirst": "优先刷脸，备用验证码签署", "receiverItemExtends.faceFirstTips": "签署时系统优先推荐刷脸校验，刷脸校验不通过则改用验证码校验", "receiverItemExtends.faceMust": "刷脸签署", "receiverItemExtends.faceMustTips": "该用户需要通过刷脸校验才能完成签署（刷脸签署暂只支持大陆居民使用）", "receiverItemExtends.noHand": "使用上上签系统签名", "receiverItemExtends.noHandTips": "该用户只能选择上上签系统签名进行签署", "receiverItemExtends.mustHand": "必须手写签名", "receiverItemExtends.mustHandTips": "该用户需要手写签名才能完成签署", "receiverItemExtends.useScanCodeClaim": "启用扫码认领", "receiverItemExtends.scanCodeClaimTip": "针对前台代收合同，勾选后，签约方只能扫查验码才能认领，其他方式均不能认领合同。查验码可在合同发出后，通过查验码接口或在合同详情页下载查验码，然后自行通知到对应认领人。（收件方可在合同详情或合同内容页看到查验码，自行扫码完成认领）。", "receiverItemExtends.notifyLabel.1": "签约须知", "receiverItemExtends.notifyLabel.2": "（限255字）", "receiverItemExtends.handwritingRec": "开启笔迹识别", "receiverItemExtends.handwritingRecTips": "该用户手写的姓名将与发件人指定的或实名信息中的姓名进行比对，比对一致才可完成签署", "receiverItemExtends.readAll": "阅读完毕再签署", "receiverItemExtends.readAllTips": "阅读完毕再签署", "receiverItemExtends.attachDoc": "添加合同附属资料", "receiverItemExtends.mainDoc": "签约主体资料", "receiverItemExtends.notifyOff": "关闭短信通知", "receiverItemExtends.notifyOffTips": "启用后，该签约方不接收签约通知（关闭状态下默认发送）", "receiverItemExtends.notifyForeign": "使用外文通知", "receiverItemExtends.notifyForeignTips": "给所有签约方发送外文通知，", "receiverItemExtends.pay": "成为付费方", "receiverItemExtends.payTips": "成为付费方", "receiverItemExtends.dataName": "资料名称", "receiverItemExtends.remarks": "备注", "receiverItemExtends.itemRequire.1": "选项", "receiverItemExtends.itemRequire.2": "必填", "receiverItemExtends.addressLine": "收集地址的详细程度(必填)", "receiverItemExtends.require": "必填项", "receiverItemExtends.notRequire": "选填项", "receiverItemExtends.addressCheckbox.province": "省", "receiverItemExtends.addressCheckbox.city": "市", "receiverItemExtends.addressCheckbox.area": "区", "receiverItemExtends.addressCheckbox.detail": "详细地址（如街道、门牌号等）", "receiverItemExtends.storeTypeList.0": "文字资料", "receiverItemExtends.storeTypeList.1": "图片资料", "receiverItemExtends.storeTypeList.2": "单选资料", "receiverItemExtends.storeTypeList.3": "多选资料", "receiverItemExtends.storeTypeList.4": "PDF资料", "receiverItemExtends.storeTypeList.5": "日期资料", "receiverItemExtends.storeTypeList.6": "地址资料", "receiverItemExtends.storeTypeList.10": "表单资料", "receiverItemExtends.boxContent.dataBox": "档案柜", "receiverItemExtends.boxContent.basicDataCollection": "基础资料采集", "receiverItemExtends.boxContent.customDataCollection": "自定义资料采集", "receiverItemExtends.boxContent.personalRealName": "个人实名认证", "receiverItemExtends.boxContent.selectRequire": "(必选项)", "receiverItemExtends.boxContent.applyForAuthorization": "申请获取授权", "receiverItemExtends.boxContent.entAuthorizationList.entAuth": "企业实名认证", "receiverItemExtends.boxContent.entAuthorizationList.seal": "企业印章（用于代理签署）", "receiverItemExtends.boxContent.entAuthorizationList.send": "合同代发", "receiverItemExtends.notify.title": "启用后，该签约方不接收签约通知（关闭状态下默认发送）", "receiverItemExtends.notify.explain.1": "不给固定签约方发，可变签约方不受影响", "receiverItemExtends.notify.explain.2": "不给所有签约方发", "receiverItemExtends.attach.dataName": "资料名称", "receiverItemExtends.attach.eg": "例：身份证照片", "receiverItemExtends.attach.remake": "备注", "receiverItemExtends.attach.egIDCard": "例：请上传本人的身份证照片（选填）", "receiverItemExtends.attach.addData": "添加资料", "receiverItemExtends.attach.error.dataNotEmpty": "资料名称不能为空", "receiverItemExtends.attach.error.attachNameNotSame": "合同附属资料名称不能相同", "receiverItemExtends.attach.collapse": "收起", "receiverItemExtends.attach.expand": "展开", "receiverItemExtends.attach.delete": "删除", "receiverItemExtends.auth.field.name": "经办人姓名", "receiverItemExtends.auth.field.id": "经办人身份证号", "receiverItemExtends.auth.placeholder.name": "姓名一致才能代表企业签署（选填）", "receiverItemExtends.auth.placeholder.id": "身份证一致才能代表企业签署（选填）", "receiverItemExtends.auth.tips.entSignature": "此经办人仅需要使用个人签名（对应个人CA证书）完成签署，无需盖章。但仍需为该企业额外指定盖章人", "receiverItemExtends.auth.tips.stampSignature": "使用企业印章签署时，需同时添加个人签名完成签署。签署前需要完成个人实名认证", "receiverItemExtends.auth.checkboxLabel.onlyStamp": "启用经办人身份核验。启用后，经办人需要实名签署", "receiverItemExtends.auth.checkboxLabel.withSignature": "启用经办人身份核验（企业签字本身是需要经办人实名的，但勾选并填写姓名或身份证后，才会在签署时校验姓名或身份证是否完全一致）", "receiverItemExtends.ddl": "签署时效", "receiverItemExtends.ddlDesc.0": "收到合同后，必须在", "receiverItemExtends.ddlDesc.1": "天", "receiverItemExtends.ddlDesc.2": "小时", "receiverItemExtends.ddlDesc.3": "分钟内，完成签署，否则合同状态会变为“逾期未签”。开启签署时效的签署人，系统不再自动推送即将截止签约的提醒。", "receiverItemExtends.scanSign.tip": "发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景", "receiverItemExtends.scanSign.notValidateSignVerificationCode": "只在登录时校验验证码，无需签署校验", "editCompose.edit": "修改文档组合", "editCompose.name": "组合名称", "editCompose.id": "组合编号", "editCompose.comment": "文档备注", "editCompose.selectDoc": "选择合同：", "editCompose.currentCompose": "当前文档组合：", "editCompose.confirm": "确定", "editCompose.errorTip.nameNeed": "请输入文档组合名称", "editCompose.errorTip.idNeed": "请输入文档组合编号", "editCompose.errorTip.selectedNeed": "请至少选择2项", "templateList.manager": "管理人", "templateList.managerTip": "拥有分配权限的人员", "templateList.createTemp": "创建模板", "templateList.createDynamicTemp": "创建动态模板", "templateList.dynamicTemplateEntry": "动态模板入口", "templateList.myTemp": "我的模板", "templateList.allTemp": "所有模板", "templateList.myCreateTemp": "我创建的", "templateList.grantedTemp": "授权给我的", "templateList.tempName": "模板名称", "templateList.tempMark": "模板备注", "templateList.tempId": "模板编号", "templateList.templateCategory": "模板类型", "templateList.dynamicTemplate": "动态模板", "templateList.staticTemplate": "普通模板", "templateList.createUser": "创建人", "templateList.creatingCompany": "创建企业", "templateList.tempPermission": "模板权限", "templateList.permissionMgmt": "权限管理", "templateList.enableStatus": "启用状态", "templateList.enabled": "启用中", "templateList.disabled": "停用中", "templateList.batchPermission": "批量授权", "templateList.permissionSuccess": "授权成功", "templateList.selectPermission": "请选择一个权限组", "templateList.selectFederation": "选择权限方案", "templateList.selectRole": "选择角色", "templateList.selectUser": "选择人员", "templateList.enableTemp": "启用模板", "templateList.disableTemp": "停用模板", "templateList.enableConfirmText": "启用后，被授权使用人将可以使用该模板，确定启用吗？", "templateList.disableConfirmText": "停用后该模板将无法被使用，确定停用吗？", "templateList.enableSuccess": "启用成功", "templateList.disableSuccess": "停用成功", "templateList.deleteTemp": "删除模板", "templateList.deleteTempConfirmText": "删除模板后， 将无法恢复， 确定删除模板吗？", "templateList.editInfoTip": "该模板仍在编辑中，如需使用请联系模板创建人", "templateList.enable": "启用", "templateList.disable": "停用", "templateList.createUserPlaceholder": "输入须包含名称开头部分", "field.approval": "发送前审批", "field.send": "发送", "field.contractDispatchApply": "申请发送合同", "field.contractNeedYouSign": "该文件需要您签署", "field.ifSignRightNow": "是否马上签署", "field.signRightNow": "马上签署", "field.signLater": "稍后签署", "field.signaturePositionErr": "请为每个签署方指定签署位置", "field.sendSucceed": "发送成功", "field.confirm": "确定", "field.cancel": "取消", "field.qrCodeTips": "签署后扫码，即可查看签署详情、验证签名有效性及该合同是否被篡改", "field.pagesField": "第{currentPage}页，共{totalPages}页", "field.suitableWidth": "适合宽度", "field.signCheck": "签名查验", "field.locateSignaturePosition": "定位签署位置", "field.append": "添加", "field.privateLetter": "私信", "field.signNeedKnow": "签约须知", "field.maximum5M": "请上传小于5M的文档", "field.uploadServerFailure": "上传到服务器失败", "field.uploadFailure": "上传失败", "contractItem.emptyContract": "文档待上传", "contractItem.uploadDoc": "上传合同", "contractItem.replaceDoc": "替换文档", "contractItem.addAttachment": "添加合同附件", "contractItem.uploading": "正在上传中...", "contractItem.pageCount": "{num}页", "contractItem.more": "更多...", "contractItem.configAttachment": "配置附件字段", "contractItem.federationTip": "使用模板发送多文档合同，假设文档有a,b,c，场景1经常需要发送a,b，场景2经常需要发送a,b,c，那么可以提前将文档进行组合，发送时直接选择对应组合即可。", "contractItem.federations": "模板文档组合", "contractItem.interanlSignTip": "当前模板已设置为“仅用于对内文件签字场景”，不应被用于正式合同（如劳动合同等）。", "localCommon.download": "下载", "localCommon.cancel": "取消", "localCommon.confirm": "确认", "localCommon.toSelect": "请选择", "localCommon.seal": "盖章", "localCommon.signature": "签名", "localCommon.signDate": "签署日期", "localCommon.text": "文本", "localCommon.date": "日期", "localCommon.qrCode": "二维码", "localCommon.number": "数字", "localCommon.dynamicTable": "动态表格", "localCommon.terms": "合同条款", "localCommon.checkBox": "复选框", "localCommon.radioBox": "单选框", "localCommon.image": "图片", "localCommon.confirmSeal": "业务核对章", "localCommon.confirmRemark": "不符合章的备注", "localCommon.optional": "选填", "localCommon.require": "必填", "localCommon.tip": "提示", "localCommon.comboBox": "下拉框", "localCommon.yes": "yes", "localCommon.no": "no", "labels.signTime": "Дата подписания", "labels.optionLimitTip": "选择器个数已达上限", "labels.pageLimitTip": "超出页面边界，无法添加", "labels.optionName": "备选项{count}", "customLabelEdit.labelName": "名称", "customLabelEdit.require": "必填", "customLabelEdit.format": "格式", "customLabelEdit.equalWidth": "列表等宽", "customLabelEdit.adjustWidth": "自适应宽度", "customLabelEdit.contentFiller": "内容填写人", "customLabelEdit.senderOnly": "该内容仅发件人可以填写", "customLabelEdit.sender": "发件人", "customLabelEdit.senderTip": "选择“发件人”，则此合同内容在合同发出前由发件人填写", "customLabelEdit.signer": "签署人", "customLabelEdit.signerTip": "选择“签署人”，则此合同内容在签署人签署时填写", "customLabelEdit.labelSize": "显示尺寸", "customLabelEdit.labelWidth": "宽度", "customLabelEdit.labelWidthPlaceHolder": "请输入宽度", "customLabelEdit.labelHeight": "高度", "customLabelEdit.labelHeightPlaceHolder": "请输入高度", "customLabelEdit.autoSystemFill": "系统自动填写", "customLabelEdit.alternativeItem": "备选项", "customLabelEdit.labelFontSize": "字号", "customLabelEdit.labelFontSizePlaceHolder": "请选择字号", "customLabelEdit.labelAlign": "对齐方式", "customLabelEdit.labelDescribe": "填写说明", "customLabelEdit.labelDescribeTip": "选填，不超过20个字符", "customLabelEdit.labelRequire": "填写要求", "customLabelEdit.labelRequireTip": "必填，不填不能发送或签署", "customLabelEdit.confirm": "确定", "customLabelEdit.cancel": "取消", "customLabelEdit.defaultValue": "设置默认值", "customLabelEdit.selectDateDefaultValue": "选择日期", "customLabelEdit.messageTip.nameError": "请输入名称", "customLabelEdit.messageTip.itemError": "请输入备选项", "customLabelEdit.messageTip.itemSameError": "备选项不能相同", "customLabelEdit.messageTip.itemRegError": "备选项名字只能为中文、英文、数字的组合", "customLabelEdit.messageTip.widthError": "宽度请输入大于等于28的值", "customLabelEdit.messageTip.widthMaxError": "宽度请输入小于{width}的值", "customLabelEdit.messageTip.heightError": "高度请输入大于等于20的值", "customLabelEdit.messageTip.heightMaxError": "高度请输入小于{height}的值", "customLabelEdit.messageTip.wordNumError": "请填写字段内容字数", "customLabelEdit.messageTip.overCountLimit": "最多支持500个备选项", "customLabelEdit.defaultValueTip": "勾选前方选择框将其设置为默认值", "customLabelEdit.addOption": "添加选项", "customLabelEdit.batchAddOption": "批量添加选项", "customLabelEdit.selectTermType": "请选择条款类别", "customLabelEdit.wordNum": "内容字数", "customLabelEdit.wordNumTip": "用于计算字段内容的预留宽度，不限制字段内容字数，默认为5，超出界面部分会被截断", "customLabelEdit.location": "坐标位置", "customLabelEdit.xLocation": "距左边", "customLabelEdit.yLocation": "距顶部", "labelEdit.receiver": "接收方", "labelEdit.info.0": "请注意：", "labelEdit.info.1": "1）如果模板中多个位置添加了同一个业务字段，填写人只需填写一次，并保存为同一个值；", "labelEdit.info.2": "2）属性设置会同步更新当前模板内所有同名业务字段。", "labelEdit.info.3": "注：签署人只有选择不符合章后才需要填写", "labelEdit.keywordPosition": "字段坐标位置", "labelEdit.keywordMatch": "按关键字匹配(支持手动调整位置)", "labelEdit.keyword": "合同中的关键字", "labelEdit.keywordPlaceHolder": "如“盖章处”", "labelEdit.keywordNum": "第几处关键字", "labelEdit.keywordNumPlaceHolder": "50以内数字", "labelEdit.nameError": "请填写名称！", "labelEdit.keywordMove": "移动偏移量（相对纸张大小）", "labelEdit.keywordMoveX": "横向移动", "labelEdit.keywordMoveY": "纵向移动", "labelEdit.autoPosition": "自动定位", "labelEdit.excelHeaderPosition.title": "Excel表头定位", "labelEdit.excelHeaderPosition.keyword": "表头关键字", "labelEdit.excelHeaderPosition.keywordPlaceHolder": "如实收数量", "labelEdit.excelHeaderPosition.keywordNum": "第几处关键字", "labelEdit.excelHeaderPosition.referenceCol": "参照列列名", "labelEdit.excelHeaderPosition.referenceColPlaceHolder": "如货物名称", "labelEdit.excelHeaderPosition.headerKeyword": "Excel表头关键字", "labelEdit.excelHeaderPosition.result": "效果如下：", "labelEdit.excelHeaderPosition.headerKeywordTipsList.0": "1.传入excel表单（如收货单）时，系统会自动在表头关键字下每个单元格中设置字段。", "labelEdit.excelHeaderPosition.headerKeywordTipsList.1": "2.字段：名称默认自增，如设定字段名为实收，则后续自增字段名分别为实收_1，实收_2，实收_3，...", "labelEdit.excelHeaderPosition.headerKeywordTipsList.2": "3.第几处关键字：若文档中含有多个相同关键字，则定位至第N个关键字（其他位置关键字不设置字段）。", "labelEdit.excelHeaderPosition.setReferenceCol": " 设置参照列", "labelEdit.excelHeaderPosition.setReferenceColTips.0": "1. 必填，否则该功能不生效。", "labelEdit.excelHeaderPosition.setReferenceColTips.1": "2. 填入关键字，则字段与该列数据对齐，遇到空行则自动终止。", "labelEdit.excelHeaderPosition.setReferenceColTips.2": "3. 参照列最多支持100行，超出不做处理。", "pointPositionDoc.pageTip": "第{pageNum}页共{pageSize}页", "pointPositionDoc.nextDoc": "进入下一份文档", "pointPositionDoc.checkboxName": "备选项{count}", "pointPositionDoc.confirmSeal": "符合章", "pointPositionDoc.notConfirmSeal": "不符合章", "pointPositionDoc.deleteTip": "删除成功", "pointPositionDoc.viewHighDpiImg": "查看高清图片|查看原图", "pointPositionSite.step1": "Шаг 1：", "pointPositionSite.selectSigner": "Выбрать подписанта", "pointPositionSite.step2": "Шаг 2：", "pointPositionSite.multipleSigners": "此签约方已开启多人签字功能", "pointPositionSite.dragSignaturePosition": "Перетащить место подписи", "pointPositionSite.signField": "Место подписи", "pointPositionSite.tempField": "临时字段", "pointPositionSite.businessField": "业务字段", "pointPositionSite.addBusinessField": "添加业务字段", "pointPositionSite.manageBusinessField": "管理业务字段", "pointPositionSite.searchBusinessField": "输入需查找的业务字段名称", "pointPositionSite.edit": "编辑", "pointPositionSite.decorateField": "合同装饰", "pointPositionSite.optional": "（选填）", "pointPositionSite.whatTempField": "什么是临时字段？", "pointPositionSite.whatTempTip.0": "临时字段可用于设置模板变量， 设置后只在该模板生效， 无法在其他模板重复使用。", "pointPositionSite.whatTempTip.1": "通过以下配置可以使临时字段被搜索：", "pointPositionSite.know": "知道了", "pointPositionSite.whatBusinessField": "什么是业务字段（合同内容字段）？", "pointPositionSite.whatBusinessTip.0": "合同内容字段可用于设置模板变量，设置后企业成员均可在设置模板时重复使用。", "pointPositionSite.whatBusinessTip.1": "发起模板后在合同内容字段内填入的合同内容可搭配合同管理的“列表配置”功能支持查看和搜索。", "pointPositionSite.seal": "Поставить печать", "pointPositionSite.entSignature": "盖章人签字", "pointPositionSite.operatorSignature": "经办人签字", "pointPositionSite.signature": "Подписать", "pointPositionSite.confirmSeal": "业务核对章", "pointPositionSite.confirmRemark": "不符合章的备注", "pointPositionSite.signDate": "Дата подписания", "pointPositionSite.text": "文本", "pointPositionSite.singleBox": "单选框", "pointPositionSite.multipleBox": "复选框", "pointPositionSite.comboBox": "下拉框", "pointPositionSite.watermark": "水印", "pointPositionSite.decorataRidingSeal": "骑缝章", "pointPositionSite.picture": "图片", "pointPositionSite.innerSignComment": "批注", "pointPositionSite.singlePageRidingSealTip": "单页文档无法添加骑缝章", "pointPositionMiniDoc.document": "文档", "pointPositionMiniDoc.documentsLength": "共{documentsLength}份", "pointPositionMiniDoc.pager": "页码", "pointPositionMiniDoc.page": "页", "pointPosition.saveTemplateTip": "提醒", "pointPosition.save": "继续保存", "pointPosition.send": "继续发送", "pointPosition.hasSameLabelTip": "已存在不同类型的同名字段", "pointPosition.needChangeNameTip": "已存在同名字段，请修改名称", "pointPosition.synLabel": "已将{num}个同名字段属性更新为当前字段属性值", "pointPosition.saveSuc": "保存成功", "pointPosition.isToPermissions": "为规范使用者的操作行为，建议为模板使用者设置权限（发送合同前能否修改合同的权限）", "pointPosition.remind": "提示", "pointPosition.goSet": "去设置", "pointPosition.notGoSet": "暂不设置", "pointPosition.nowSignText": "该文件需要您签署，是否马上签署?", "pointPosition.nowSignTip": "提示", "pointPosition.nowSign": "马上签署", "pointPosition.laterSign": "稍后签署", "pointPosition.contractDispatchApply": "申请发送合同", "pointPosition.riskTips": "年审风险提示", "pointPosition.riskTipsCancel": "取消发送", "pointPosition.riskTipsConfirm": "全部发送", "pointPosition.realNameAnnualVerify": "实名年审", "pointPosition.realNameAnnualVerifyRecords": "年审记录", "pointPosition.customInfoAnnualVerify": "自定义资料年审", "pointPosition.viewAnnualVerifyINfo": "查看资料详情", "pointPosition.entName": "企业名称", "pointPosition.operation": "操作", "pointPosition.annualVerifyTime": "年审时间", "pointPosition.annualVerifyStatus": "年审状态", "pointPosition.annualVerifyCondition": "年审状况", "pointPosition.noDataTips": "无", "pointPosition.noRealNameAnnualVerify": "未年审", "pointPosition.realNameAnnualVerifying": "年审中", "pointPosition.realNameAnnualVerified": "已年审", "pointPosition.noCustomInfoAnnualVerify": "未年审", "pointPosition.customInfoAnnualVerifyingNo": "年审中（未提交）", "pointPosition.customInfoAnnualVerifyingYes": "年审中（已提交）", "pointPosition.customInfoAnnualVerified": "已年审", "labelLackTip.document": "文档", "labelLackTip.signer": "签署人", "labelLackTip.lackLabelTips": "没有在以下文件中指定签署位置，将不参与以下合同的签署，也不会接收到这些合同。", "labelLackTip.allLackLabelTips": "在所有合同文件中都未指定签署位置，合同无法发送。请调整签署位置或删除该签署人。", "labelLackTip.goEdit": "去修改", "labelLackTip.operationTip": "了解如何实现只看不签？", "labelLackTip.caseTip.1": "方案一：将文件作为“签约须知”，添加给 xxx签约角色。", "labelLackTip.caseTip.2": "方案二：将xxx签约角色账号再次添加为抄送人，将文件抄送给该账号。建议您关闭该抄送人的消息通知。", "labelLackTip.caseTip.3": "方案三：xxx签约角色的账号作为发件人、审批人，或获得查看企业合同权限，那么他是可以查看这些文件。", "signCharge.deductPublicNotice": "Если количество копий частного договора недостаточно, договор будет вычтен", "signCharge.charge": "Расчет: ", "signCharge.units": "{num}份", "signCharge.contractToPrivate": "корпоративный контракт", "signCharge.contractToPublic": "частный контракт", "signCharge.costTips.1": "Для публичного договора: подписант(не включая отправителя) договор с корпоративным аккаунтом", "signCharge.costTips.2": "Для частного договора: подписант(не включая отправителя) договор без корпоративного аккаунта", "signCharge.costTips.3": "количество тарификации рассчитывается исходя из количество копий файла.", "signCharge.costTips.4": "количество тарификации=количество файлов×партия введенных количество пользователей", "signCharge.confirm": "Подтвердить ", "signCharge.cancel": "Отменить", "signCharge.toCharge": "Пополнить баланс", "signCharge.contractNeedCharge.1": "Количество доступных контрактов недостаточна, и не может быть отправлено", "signCharge.contractNeedCharge.2": "Количество доступных контрактов недостаточна, пожалуйста, свяжитесь с главным администратором, чтобы пополнить»", "signCharge.contractNeedCharge.3": "专用套餐余额不足，请点击下方充值按钮完成充值", "signCharge.lackVerCode": "Пожалуйста, сначала введите код подтверждения", "signCharge.verCodeInputErr": "Пожалуйста, сначала получите код подтверждения", "sendPrepare.selectTemplate": "选择模板", "sendPrepare.selectTemplateFileTip": "请先选择模板文件", "sendPrepare.batchImportExcel": "使用excel批量发送", "sendPrepare.sendContract": "单独发合同", "sendPrepare.allTemplate": "全部模板", "sendPrepare.emptyContract": "空白合同", "selectSender.addReceiver": "请添加签约方", "selectSender.currentSender": "当前发件方：", "selectSender.toBeDetermined": "【待确定】", "selectSender.tips.0": "该模板已获得代发授权，发出的每份合同以已授权的签约方企业名义展示。", "selectSender.tips.1": "若同份合同出现多个已授权的签约方企业，则不作为代发合同。", "descriptionFields.newField": "新增字段", "descriptionFields.title": "字段配置", "descriptionFields.syncDoc": "同步到模板的其他文档", "descriptionFields.placeholder": "请输入字段", "descriptionFields.cancel": "取消", "descriptionFields.confirm": "确定", "descriptionFields.saveSuc": "保存成功", "getSeal.selectSeal": "选择印章", "getSeal.chooseApplyPerson": "选择申请人", "getSeal.getSealBtn": "获取印章", "getSeal.nowApplySealList": "您正在请求以下印章", "getSeal.chooseApplyPersonToDeal": "请选择申请人，您的申请以及合同将会转交给所选人来处理", "getSeal.chooseApplyPersonToMandate": "请选择印章人，所选人收到通知、审核通过后，您将获得该印章的使用权限，届时可以使用该印章来盖章并签署合同", "getSeal.cancel": "取消", "getSeal.confirm": "确定", "getSeal.sealApplySentPleaseWait": "印章分配申请已发送，请等待审核通过。或者您可以选择其他盖章方式", "getSeal.entNoSeal": "您的企业尚未上传印章", "getSeal.contactGroupAdminToDistributeSeal": "请联系集团管理员分配印章", "getSeal.getSealTip": "你需要先获得企业印章才能查看此合同", "authIntercept.title": "要求您以：", "authIntercept.name": "姓名为：", "authIntercept.id": "身份证号为：", "authIntercept.descNoAuth1": "请确认以上身份信息为您本人，并以此进行实名认证。", "authIntercept.descNoAuth2": "实名认证通过后，可查看并签署合同。", "authIntercept.descNoSame1": " 的身份签署合同", "authIntercept.descNoSame2": "这与您当前登录的账号已完成的实名信息不符。", "authIntercept.tips": "注：身份信息完全一致才能签署合同", "authIntercept.goOn": "是我本人，开始认证", "authIntercept.goMore": "去补充认证", "authIntercept.authTip": "进行实名认证。", "authIntercept.viewAndSign": "完成认证后即可查看和签署合同", "authIntercept.tips2": "注：企业名称完全一致才能查看和签署合同。", "authIntercept.requestOtherAnth": "请求他人认证", "authIntercept.goAuth": "去实名认证", "authIntercept.requestSomeoneList": "请求以下人员完成实名认证：", "authIntercept.ent": "企业", "authIntercept.entName": "企业名称", "authIntercept.account": "账号", "authIntercept.accountPH": "手机或邮箱", "authIntercept.send": "发送", "authIntercept.lackEntName": "请填写企业名称", "authIntercept.errAccount": "请填写正确的邮箱或手机号", "authIntercept.successfulSent": "发送成功", "authIntercept.me": "我", "authIntercept.myself": "本人，", "authIntercept.reAuthBtnTip": "我是当前手机号的实际使用者，", "authIntercept.reAuthBtnContent": "重新实名后，该账号的原实名会被驳回，请确认。", "authIntercept.cancel": "取消", "authIntercept.confirmOk": "确认", "authIntercept.goHome": "返回合同列表页>>", "authIntercept.authInfo": "检测到您当前账号的实名身份为 ", "authIntercept.in": "于", "authIntercept.finishAuth": "完成实名，用于合规签署合同", "authIntercept.ask": "是否继续以当前账号签署？", "authIntercept.reAuthBtnText": "是的，我要用本账号重新实名签署", "authIntercept.changePhoneText": "不是，联系发件方更改签署手机号", "authIntercept.changePhoneTip1": "应发件方要求，请联系", "authIntercept.changePhoneTip2": "，更换签署信息(手机号/姓名)，并指定由您签署。", "applyJoinEnt.beenAuthenticated": "已被实名", "applyJoinEnt.assignedIdentity": "发件方填写的签约主体为：", "applyJoinEnt.entBeenAuthenticated": "该企业已被实名，主管理员信息如下：", "applyJoinEnt.entAdminName": "管理员姓名：", "applyJoinEnt.entAdminAccount": "管理员账号：", "applyJoinEnt.applyToBeAdmin": "我要申诉成为主管理员", "applyJoinEnt.contactToJoin": "联系管理员加入企业", "applyJoinEnt.applicant": "申请人", "applyJoinEnt.inputYourName": "请输入您的姓名", "applyJoinEnt.account": "账号", "applyJoinEnt.send": "发送", "applyJoinEnt.contract": "合同", "applyJoinEnt.sendWishToJoin": "您可以通过账号申诉成为管理员，也可以向管理员发送加入企业的申请", "applyJoinEnt.applyToJoin": "您还未加入该企业，无法查看或签署该{alias}，是否要申请加入？", "applyJoinEnt.sentSuccessful": "发送成功", "receiverItemDisclaimer.title": "功能使用提示", "receiverItemDisclaimer.desc.0": "根据《电子签名法》相关规定，合法有效的电子合同，必须要保证签约主体能够随时调取查用，禁止签约方下载、查看的做法将违反了《电子签名法》的要求。", "receiverItemDisclaimer.desc.1": "请确保签约人可以查看、下载已经签署的协议。", "receiverItemDisclaimer.desc.2": "我已阅读并同意以上内容", "receiverItemDisclaimer.confirm": "继续", "batchOrAllOperateContract.yes": "是", "batchOrAllOperateContract.no": "否", "batchOrAllOperateContract.reject": "驳回", "batchOrAllOperateContract.agree": "同意", "batchOrAllOperateContract.cancel": "取消", "batchOrAllOperateContract.confirm": "确定", "batchOrAllOperateContract.continue": "继续", "batchOrAllOperateContract.batch": "批量", "batchOrAllOperateContract.nowBatch": "此次批量", "batchOrAllOperateContract.contractRange": "的合同范围是：", "batchOrAllOperateContract.operate": "操作", "batchOrAllOperateContract.allSelect": "共选中", "batchOrAllOperateContract.changeStatusTip": "合同，非“逾期未签”合同状态的合同不会被操作。“逾期未签”的合同的状态将被修改为“签署中”，合同签约截止时间为7天后，是否向未签署合同的账号推送签约提醒?", "batchOrAllOperateContract.changeStatusSupplement": "为签署人设置的“签署时效”将不再生效", "batchOrAllOperateContract.approvalTip": "份合同，其中不需要您审批的合同不会被操作。请选择审批结果", "batchOrAllOperateContract.batchOperateResultPage": "批量操作结果页", "batchOrAllOperateContract.toHomePage": "回到首页", "batchOrAllOperateContract.refresh": "刷新页面", "batchOrAllOperateContract.logTips": "合同数量越多，批量操作所需时间越长，请耐心等待。（可从合同管理页的“批量操作记录”进入此页面）", "batchOrAllOperateContract.operateName": "操作名称", "batchOrAllOperateContract.operateTime": "操作时间", "batchOrAllOperateContract.operateResult": "操作结果", "batchOrAllOperateContract.operateLog": "操作日志", "batchOrAllOperateContract.remark": "备注", "batchOrAllOperateContract.batchChangeStatus": "批量逾期后延期", "batchOrAllOperateContract.batchSetTag": "批量设置标签", "batchOrAllOperateContract.batchArchive": "批量归档", "batchOrAllOperateContract.batchRemind": "批量提醒", "batchOrAllOperateContract.batchRevoke": "批量撤回", "batchOrAllOperateContract.batchExport": "批量导出明细", "batchOrAllOperateContract.batchExportDownload": "下载", "batchOrAllOperateContract.view": "查看", "batchOrAllOperateContract.batchExportFail": "导出失败", "batchOrAllOperateContract.batchTransfer": "批量转交", "batchOrAllOperateContract.batchApproval": "批量审批", "batchOrAllOperateContract.batchModifyLife": "批量修改合同到期日", "batchOrAllOperateContract.batchModifyTip": "合同到期时间≠签约截止时间，若设置签约提醒，请另外设置签约截止时间。", "batchOrAllOperateContract.notStart": "执行中", "batchOrAllOperateContract.doing": "执行中", "batchOrAllOperateContract.discontinue": "已终止", "batchOrAllOperateContract.viewProgress": "查看进度", "batchOrAllOperateContract.operateProgress": "操作进度", "batchOrAllOperateContract.tip": "提示", "batchOrAllOperateContract.discontinueOperate": "终止批量任务", "batchOrAllOperateContract.confirmDiscontinue": "终止任务后，未被处理的合同需要你重新进行操作。", "batchOrAllOperateContract.operateSuccess": "操作成功", "batchOrAllOperateContract.taskTerminated": "任务已被终止", "batchOrAllOperateContract.done": "已完成", "batchOrAllOperateContract.detail": "共需操作{totalCount}份合同，已操作{nowCount}份合同，数据最终以操作日志为准。", "batchOrAllOperateContract.moreBatch": "更多批量操作", "batchOrAllOperateContract.batchLog": "批量操作记录", "batchOrAllOperateContract.feedback": "协议信息管理问卷反馈", "batchOrAllOperateContract.tagTip": "某企业创建的标签只能设置到该企业发送的合同中，不能设置到其他企业发送的合同。", "batchOrAllOperateContract.changeTip": "继续操作则可将“逾期未签”的合同的状态修改为“签署中”，以便重启合同签署。", "batchOrAllOperateContract.setOption1": "仅设置勾选中的合同", "batchOrAllOperateContract.changeOption1": "仅修改勾选中的合同", "batchOrAllOperateContract.setOption2": "设置列表中所有合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.changeOption2": "修改列表中所有合同，含列表当前页未勾选中的合同以及列表其他页的合同。借助归档文件夹或搜索功能，您可以调整批量操作的合同范围。", "batchOrAllOperateContract.batchExportTip": "不超过1万条合同明细。开通更高版本可一次性导出更多合同明细 | 不超过5万条合同明细", "rejectSigner.tipTitle": "驳回重签提示", "rejectSigner.tipContent": "请先勾选需要驳回重签的签署字段（签名或盖章或签署人填写的字段），再点击“驳回重签”按钮。", "rejectSigner.noTip": "不再提示", "rejectSigner.iKnow": "我知道了", "rejectSigner.rejectBtn": "驳回重签", "rejectSigner.noOptionTip": "请先选择需要驳回重签的签署字段（签名或盖章或签署人填写的字段），再点击“驳回重签”按钮。", "rejectSigner.writeMustKnow": "填写重签须知", "rejectSigner.confirm": "确定", "rejectSigner.cancel": "取消", "rejectSigner.success": "驳回成功", "rejectSigner.fail": "正在重签的用户不能驳回重签，请等待用户签完后再操作。", "rejectSigner.mustKnowPlaceHolder": "选填，限255个字", "rejectSigner.mustKnowTip": "请完善签约须知，帮助签署人顺利重签", "rejectSigner.placeTop": "将重叠的顶层置底", "counterDialog.longTime": "正在发送合同，全部发送完成，大约还需要", "counterDialog.close": "我知道了", "counterDialog.minutes": "分钟", "autoSignDialog.waitTip": "提示", "autoSignDialog.errorTip": "自动签失败原因", "autoSignDialog.title": "自动签失败原因提示", "autoSignDialog.reason": "因不满足以下条件，您不能使用自动签署功能签署此合同：", "autoSignDialog.wait": "正在准备自动签。", "tagManage.deleteDone": "删除成功", "tagManage.setDone": "设置成功", "tagManage.noEditPermission": "您没有配置履约提醒的权限", "tagManage.addLabel": "新增标签", "tagManage.delLabel": "移除标签", "tagManage.slotTip1": "· 配置规则对当前文件夹中的合同生效，不影响企业控制台-标签管理中的配置；", "tagManage.slotTip2": "· 文件夹与文件夹之间的配置规则也是互不影响的。", "tagManage.afterDelLblTip": "移除后，已在履约文件夹合同上的标签将消失", "tagManage.delLblAA": "移除标签“{name}”", "tagManage.confirm": "Подтвердить", "tagManage.cancel": "Отменить", "tagManage.noTagTip": "企业控制台-标签管理中尚未设置标签", "tagManage.addTag": "添加标签", "settingDownloadFileName.settingTitle": "设置合同下载名称", "settingDownloadFileName.defaultName": "系统默认文件名", "settingDownloadFileName.titleAndId": "(合同标题+合同ID)", "settingDownloadFileName.defineName": "自定义文件名", "settingDownloadFileName.defineNameTip": "（最多支持选择5个）", "settingDownloadFileName.limitNumTip": "请至少选择一个自定义文件名", "settingDownloadFileName.noInputValueTip": "请输入自定义文件名", "settingDownloadFileName.hasIllegalCharacter": "自定义文件名中含有非法字符", "settingDownloadFileName.inputPlaceholder": "请输入文件名称", "settingDownloadFileName.contractTitle": "合同标题", "settingDownloadFileName.contractId": "合同ID", "settingDownloadFileName.receiver": "接收方", "settingDownloadFileName.personOrEntName": "个人/企业名称", "settingDownloadFileName.hasSelectedCase": "已选组合：", "settingDownloadFileName.noRemindBtn": "登录期间不再提示", "settingDownloadFileName.tip": "注：采用私有存储方式的合同定义的文件名称将无法生效", "transferContract.transferContract": "移交", "transferContract.contractTransfer": "移交", "transferContract.tip": "提示", "transferContract.confirmTip": "移交后，合同将不再由您持有，您的合同管理列表中无法搜索到该份合同。确定移交吗?", "transferContract.accountNameTip": "接收人姓名与账号不匹配，请确保接收人信息真实准确。", "transferContract.noJoinEntTip": "该接收人账号未加入企业，移交后对方有可能无法完成签署，是否确定移交。", "transferContract.success": "移交成功", "transferContract.resultInfo": "您已将合同移交给{receiverName}（{receiverAccount}），他将收到合同签署通知。", "transferContract.verifyCodeTip1": "您可通过合同查验码或立即跳转小程序。", "transferContract.verifyCodeTip2": "查看合同签署进度。查看后，「上上签查合同」小程序将为您保留查验记录。", "transferContract.downloadVerifyCode": "下载合同查验码", "transferContract.receiverAccount": "接收人账号", "transferContract.receiverName": "接收人姓名", "transferContract.receiverAccountPlaceholder": "请输入接收人手机号或邮箱", "transferContract.accountErrorTip": "请输入正确的账号", "transferContract.receiverNamePlaceholder": "请输入接收人真实姓名", "transferContract.transferNamePlaceholder": "请输入您的姓名", "transferContract.transferName": "移交人姓名", "transferContract.transferAccount": "移交人账号", "transferContract.transferTip": "请注意接收人账号务必填写正确，如若移交错误，需要联系企业主管理员将合同重新移交给正确签署人。", "transferContract.adminAccount": "账号：{entAdminAccount}", "transferContract.adminName": "企业昵称：{entAdminName}", "transferContract.admin": "主管理员", "transferContract.confirmTransfer": "确认移交", "transferContract.pleaseInputReceiverInfo": "请填写接收人信息：", "transferContract.pleaseInputTransferInfo": "请填写移交人信息：", "transferContract.name": "姓名：", "transferContract.account": "账号：", "transferContract.transfer": "移交", "transferContract.cancel": "取消", "lang": "ru", "docContentTableCol.noReceiver": "Получатели не добавлены", "docSlider.initSign": "Начать подписание", "docSlider.otherSource": "Проверить другой источник файла", "docSlider.otherSourceTip": "после нажатии на интерфесе платформы можно проверить отправленные договора, сохраненные файлы и  договор BestSign V2", "docSlider.fastOperate": "Быстрая операция", "docSlider.operateStatus.signing": "Подписывается…", "docSlider.operateStatus.needMeOperate": "Нужно мне выполнить", "docSlider.operateStatus.inApproval": "В процессе рассмотрения..", "docSlider.operateStatus.needOthersSign": "Подпись с другой стороны", "docSlider.operateStatus.closing": "Подписывание скоро завершится", "docSlider.operateStatus.signComplete": "Подписание завершено", "docSlider.operateStatus.closed": "Уже прекращен подпись договора", "docSlider.allDocs": "Все документы", "docSlider.allDocsType.inbox": "Входящий", "docSlider.allDocsType.outbox": "Исходящий", "docSlider.allDocsType.closing": "Срок действия контракта истекает", "docSlider.allDocsType.closed": "Истёк срок действия контракта", "docSlider.allDocsType.draft": "Черновик", "docSlider.folder": "Папка", "docSlider.newFolder": "Новая папка", "docSlider.rename": "Переименовать", "docSlider.delete": "Удалить", "docSlider.enterpriseFolder": "Файл предприятии (тип договора)", "docSlider.enterpriseFilingContract": "Архив договора предприятия", "docSlider.noAuthority": "Нет право доступа к управления договора в новом формате", "docSlider.noAuthorityTips.info": "开通后，您将体验”企业归档合同”管理的功能，有助于企业高效的管理合同。", "docSlider.noAuthorityTips.toDetail": "详情请看", "docSlider.noAuthorityTips.cancel": "取 消", "docSlider.noAuthorityTips.open": "开通", "docSlider.assignAuthority": "просьба свяжитесьс администратором пусть распределит право", "docSlider.msgBox.tips": "Напоминать", "docSlider.msgBox.info": "На странице недавно открытого окна проверьте «Другие исходные файлы» ", "docSlider.msgBox.confirm": "Понятно", "docSlider.addFolder": "Добавить файл", "docSlider.switchEntFail": "Не удалось сменить договор корпоративного архива, повторите попытку позже!", "docSlider.openFail": "Не удалось открыть", "docSlider.openLater": "Не удалость открыть, повторите позже!", "docSlider.notPublicUser": "Этот пользователь не является публичным пользователем", "docSlider.notEntUser": "Этот пользователь не является корпоративным пользователем", "docSlider.cantReadEntFolder": "Этот пользователь не может просматривать корпоративную папку (тип контракта)", "docSlider.stick": "stick", "docContentTop.searchTitlePlaceholder": "Название договора\\ Имя отправителя\\ Названия компании отправителя", "docContentTop.moreSearch": "Больше поиска", "docContentTop.output": "Экспорт", "docContentTop.import": "导入", "docContentTop.listConfig": "Конфигурация списка", "docContentTop.filterList.signStatus": "Статус договора", "docContentTop.filterList.archiveFolders": "Папка", "docContentTop.filterList.contractType": "Ти<PERSON> контракта", "docContentTop.filterList.sharedByMe": "Мой репост", "docContentTop.filterList.sharedToMe": "Репост отправлен", "docContentTop.filterList.allFiles": "Все папки", "docContentTop.companyTree": "Организационная структура", "docContentTop.unmovedContract": "Не перемещенный договор", "docContentTop.contractLabelList.labor": "Договор труда", "docContentTop.contractLabelList.borrow": "Договор кредита", "docContentTop.contractLabelList.legal": "Юридический договор", "docContentTop.contractLabelList.loan": "Договор депозита", "docContentTop.contractLabelList.transfer": "Договор передачи", "docContentTop.search.contractNum": "Номер контракта", "docContentTop.search.contractNumPlaceholder": "Введите номер договора", "docContentTop.search.sender": "Отправитель", "docContentTop.search.senderPlaceholder": "Название отправителя  или аккаунт", "docContentTop.search.receiver": "Получатель", "docContentTop.search.receiverPlaceholder": "Название получателя или аккаунт", "docContentTop.search.sendTime": "Начало подписания договора", "docContentTop.search.signDeadline": "Крайний срок подписания", "docContentTop.search.timeStartPlaceholder": "Выбрать время начала", "docContentTop.search.timeEndPlaceholder": "Выбрать время окончания", "docContentTop.search.source": "Источник платформы", "docContentTop.search.ssq": "BestSign", "docContentTop.search.search": "Поиск", "docContentTop.searchMsg.contractNum": "Введите правильный номер договора", "docContentTop.searchMsg.sender": "Введите правильное имя отправителя или аккаунт", "docContentTop.searchMsg.receiver": "Введите правильное имя получателя или аккаунт", "docContentTop.all": "Полностью", "docContentTop.signStatus.needMeApproval": "Ожидать моего утверждения", "docContentTop.signStatus.needMeSign": "Ожидать моего подписи", "docContentTop.signStatus.inApproval": "В процессе рассмотрения..", "docContentTop.signStatus.needOthersSign": "Подпись с другой стороны", "docContentTop.signStatus.signComplete": "Подписание завершено", "docContentTop.signStatus.signOverdue": "Просроченный подпись", "docContentTop.signStatus.rejected": "Отказано в подписи", "docContentTop.signStatus.revoked": "Отозвано", "docContentTop.searchAll": "Выбрать все", "docContentTop.confirm": "Подтвердить", "docContentTop.reset": "Обновить", "docContentTop.selectRange": "Выбрать диапазон даты", "docContentTop.datePicker.weekend": "На прошлой неделе", "docContentTop.datePicker.month": "В прошлом месяце", "docContentTop.datePicker.month3": "Последние три месяца", "docContentTop.popover.listStatus": "Статус списка", "docContentTop.popover.reset": "Восстановить исходное состояние", "docContentTop.popover.showLabel": "Поле отображения", "docContentTop.popover.showLabelOperate": "Перетащите порядок корректировки, нажмите × удалить", "docContentTop.popover.most": "Максимум 50", "docContentTop.popover.hideLabel": "Скрытая поле ввода", "docContentTop.popover.hideLabelOperate": "(перетащить повыше, чтобы добавить)", "docContentTop.popover.confirm": "Подтвердить", "docContentTop.popover.cancel": "Отменить", "docContentTable.revokeContractIdsTip": "合同已被撤销，无法下载。", "docContentTable.selectedContracts": "уже выбрано{num}файл", "docContentTable.operationTips": "请先确定业务线后再继续操作", "docContentTable.batchBtn.sign": "Пакетная подпись", "docContentTable.batchBtn.approval": "Пакетное утверждение", "docContentTable.batchBtn.remind": "Пакетное уведомление", "docContentTable.batchBtn.revoke": "Пакетная отмена", "docContentTable.batchBtn.download": "Пакетная загрузка", "docContentTable.batchBtn.delete": "Массовое удаление", "docContentTable.batchBtn.move": "Пакетное перемещение", "docContentTable.operate": "Операция", "docContentTable.searchNull": "Подобного рода договора не найден ", "docContentTable.toDetail": "Перейти к деталям", "docContentTable.download": "Скачать", "docContentTable.move": "Переместить", "docContentTable.reSend": "Снова отправить", "docContentTable.moveCancel": "Отмени<PERSON>ь ход", "docContentTable.remind": "Напоминать", "docContentTable.signer": "Под<PERSON>и<PERSON><PERSON><PERSON>т", "docContentTable.status": "Статус", "docContentTable.sendDate": "Дата отправки", "docContentTable.deadline": "Срок подписания", "docContentTable.contractStatus.needMeSign": "Нужно мне подписать", "docContentTable.contractStatus.needMeApproval": "Нужно мне утвердить", "docContentTable.contractStatus.inApproval": "В процессе рассмотрения..", "docContentTable.contractStatus.needOthersSign": "Подпись с другой стороны", "docContentTable.contractStatus.signComplete": "Подписание завершено", "docContentTable.contractStatus.draft": "Черновик", "docContentTable.contractStatus.signOverdue": "Просроченный подпись", "docContentTable.contractStatus.rejected": "Отказано в подписи", "docContentTable.contractStatus.revoked": "Отозвано", "docContentTable.contractStatus.beRejected": "Отказано в подписи", "docContentTable.contractStatus.deadline": "Cрок", "docContentTable.contractStatus.invalid": "已作废", "docContentTable.signStatus": "Статус договора", "docContentTable.catchMap.download": "Скачать", "docContentTable.catchMap.reject": "Отказ в подписи", "docContentTable.catchMap.revoke": "Отменять", "docContentTable.catchMap.delete": "Удалить", "docContentTable.catchMap.cantOperate": "невозможно {operate} с договором", "docContentTable.catchMap.hybridNetHeader": "Корпоративный отправитель использует личное хранилище договора, но текущая сеть не может подключиться к серверу хранилища договора отправителя.", "docContentTable.catchMap.hybridNetMsg": "Рекомендуем: Проверьте, была ли сеть подключена к  отправителю, повторите попытку позже. ", "docContentTable.catchMap.checkNet": "Пожалуйста, проверьте, подключена ли к внутренней сети. ", "docContentTable.confirm": "Подтвердить", "docContentTable.cancel": "Отменить", "docContentTable.continue": "Продолжить загрузку", "docContentTable.next": "Следующий", "docContentTable.delete": "Удалить", "docContentTable.searchAll": "Выбрать все", "docContentTable.nullToSign": "файл не подписан", "docContentTable.nullToApproval": "файл не может быть утвержден", "docContentTable.nullToRemind": "файл без напоминания", "docContentTable.nullToRevoke": "файл не может быть отозван", "docContentTable.sign": "Подписать", "docContentTable.approval": "утверждение", "docContentTable.invalid": "作废", "docContentTable.changeSigner": "更换签署人", "docContentTable.remindSucc": "Успешное напоминание ", "docContentTable.remindFail": "Нижеперечисленные {errorSum} партия договоров напоминание не удалось: ", "docContentTable.notice": "Напоминать", "docContentTable.revoke": "Отменять", "docContentTable.batchDownloadTip.msg1": "Отправитель следующего договора использует личное хранилище договора, но текущая сеть не может подключиться к серверу хранилища договора отправителя.", "docContentTable.batchDownloadTip.msg2": " Название контракта, который не может быть загружен, выглядит следующим образом", "docContentTable.deleteSucc": "Удалено успешно", "docContentTable.giveAuthor": "просьба свяжитесьс администратором пусть распределит право", "docContentTable.view": "Просмотреть", "docContentTable.usePaperSign": "启用纸质签", "docContentTable.modifyExpires": "修改合同到期日", "noBizLineDoc.title": "未指定业务线（或接收人尚未加入企业）的合同", "noBizLineDoc.confirm": "确认", "noBizLineDoc.cancel": "取消", "noBizLineDoc.noBizLineTip": "您有{number}份未指定业务线（或接收人尚未加入企业）的合同，请点此前往处理", "docDialog.notice": "Напоминать", "docDialog.ok": "Понятно", "docDialog.plsInputShortcutName": "请填写快捷入口名称", "docDialog.plsChooseLable": "请选择合同标签", "docDialog.plsChooseSignStatus": "请选择签署状态", "docDialog.plsInputRoleTip": "请填写业务角色", "docDialog.finish": "完成", "docDialog.search": "查询", "docDialog.meetAllConditiionTip": "同时满足以上条件的合同可以在快捷入口", "docDialog.plsChooseLblName": "请选择标签名称", "docDialog.ifLableContain": "如果合同标签包含", "docDialog.defineAsLable": "按标签定义", "docDialog.addCondition": "新增条件", "docDialog.plsInputRole": "请输入业务角色", "docDialog.signStatusIsTip": "且其“签署状态”为", "docDialog.roleEquilTip": "如果“签约角色”等于", "docDialog.codition": "条件", "docDialog.defineRoleTip": "您需要在合同中提前定义签署人的“签约角色”，如HR、经销商等", "docDialog.defineByStatus": "按签署状态定义", "docDialog.back": "返回", "docDialog.chooseIdentity": "选择身份", "docDialog.plsSelectTime": "请选择一个时间", "docDialog.noMoreThan365dayTip": "天（不超过365天）", "docDialog.noMoreThan365day": "不超过365天", "docDialog.customize": "自定义", "docDialog.beforeNumDay": "前{num}天", "docDialog.belowContractOperteFail": "以下合同{tip}，操作失败：", "docDialog.plsInput": "请输入", "docDialog.archive": "归档", "docDialog.goSign": "去签署", "docDialog.num": "份", "docDialog.otherNeedSignTip": "其他需要签署的合同", "docDialog.selectedContracts": "已选中{num}份", "docDialog.needClaimContract": "需要认领签署的合同", "docDialog.batchContainClaimHint": "批量签署的合同中包含了待认领合同，请确认这些合同是否应该由您来签署！", "docDialog.batchDowLoadHint": "您选中的合同需要分批下载", "docDialog.labelNotAddTips": "标签{num}不能贴到以下合同上", "docDialog.batchDownloadTips": "Выбранный вами договор хранится на другом сервере хранения, и вам необходимо загрузить его партиями. Пожалуйста, нажмите кнопку ниже, чтобы завершить загрузку контракта.", "docDialog.batchDownload": "Пакетная загрузка", "docDialog.saver": "Contract store", "docDialog.ssq": "BestSign", "docDialog.confirm": "Подтвердить", "docDialog.cancel": "Отменить", "docDialog.delete": "Удалить", "docDialog.deleteTips": "Файлы в этой папке не будут удалены вместе", "docDialog.deleteConfirm": "вы хотите удалить данный файл?", "docDialog.delLabel": "Удалить ярлык", "docDialog.folderPlaceholder": "Пожалуйста, введите наименование файла", "docDialog.addFolder": "Добавить файл", "docDialog.addLabel": "Добавить метку", "docDialog.dividedLabel": "Несколько ярлыков, разделенных и использованных возвратом в корзину", "docDialog.myLabels": "Моя метка", "docDialog.label": "Яр<PERSON>ык{num}", "docDialog.max10": "Добавить до 10 меток", "docDialog.length10": "Название метки не должно превышать 10 букв", "docDialog.labelExist": "Метка уже сохранена", "docDialog.selectMember": "Выберите участника", "docDialog.batchBtn.sign": "Пакетная подпись", "docDialog.batchBtn.approval": "Пакетное утверждение", "docDialog.batchBtn.remind": "Пакетное уведомление", "docDialog.batchBtn.revoke": "Пакетная отмена", "docDialog.batchSignTips": "Контракты, требующие подписи и не требующие заполнения, могут быть подписаны партиями.", "docDialog.supportBatch": "Нижеследующий контракт поддерживает пакет {type}", "docDialog.remindSucc": "Успешное напоминание ", "docDialog.remindFail": "Нижеперечисленные {errorSum} партия договоров напоминание не удалось: ", "docDialog.remind": "Напоминать", "docDialog._confirm": "Подтвердить", "docDialog._cancel": "Отменить", "docDialog.revoke": "Отменять", "docDialog.exportDetail": "Детали экспортного контракта", "docDialog.lessThan365": "Интервал времени отправки не может превышать 365 дней", "docDialog.narrowRange": "Количество контрактов превышает {maxNum}, пожалуйста, сократите временной диапазон.", "docDialog.lessThan2000": "Однократно не экспортируйте более {maxNum} документов ", "docDialog.sendTime": "Начало подписания договора", "docDialog.signDeadline": "Крайний срок подписания", "docDialog.timeStartPlaceholder": "Выберите время начала", "docDialog.to": "по", "docDialog.timeEndPlaceholder": " Выбрать время окончания", "docDialog.signStatus": "Статус договора", "docDialog.plsSelect": "Пожалуйста, выберите", "docDialog.noFileExport": " Файл временно не экспортируется ", "docDialog.downloading": "Загружается, пожалуйста ожидайте…", "docDialog.move": "Переместить", "docDialog.folder": "Папка", "docDialog.noFolder": "Файлов пока нет", "docDialog.fileSucc": "Успешно архивировано", "docBatch.agree": "同意", "docBatch.reject": "驳回", "docBatch.approve": "审批", "docBatch.approving": "审批中...", "docBatch.approved": "审批成功", "docBatch.approveAgree": "审批结果：同意", "docBatch.approveReject": "审批结果：驳回", "docBatch.approveSuggest": "审批意见", "docBatch.canNotInput": "可不填", "docBatch.confirm1": "确 定", "docBatch.cancel1": "取 消", "docBatch.inputSignPsw": "请输入签约密码", "docBatch.inputSixDigitalNum": "请输入6位数字", "docBatch.batchApproveSuccess": "批量审批成功", "docBatch.belowContractApporveFail": "以下 {length} 份合同批量审批失败", "docBatch.tips": "提示", "docBatch.confirm": "确定", "docBatch.signVerify": "签约校验", "docBatch.signPswLockedTip": "签约密码已被锁定，3小时后自动解锁，你也可以通过", "docBatch.findBackPsw": "找回密码", "docBatch.toUnlockNow": "来立即解锁", "docBatch.signPswCanEnterTip": "签约密码错误,你还可以输入2/1次,是否", "docBatch.forgetPsw": "忘记密码", "docBatch.verifyFormatError": "验证码格式错误", "docBatch.networkTimeoutTip": "网络超时，请刷新页面查看合同签署情况", "docBatch.batchSignSuccess": "批量签署成功", "docBatch.belowContractSignFail": "以下 {length} 份合同批量签署失败", "docBatch.signWithNameTip": "当前正在以{name}的名义签署合同", "docBatch.useElectronicSeal": "使用电子印章", "docBatch.noWantUseTheSealTip": "不想使用该印章？实名认证后可更换印章", "docBatch.toAuth": "去实名认证", "docBatch.useSignature": "使用签名", "docBatch.signatureFinishTip": "签名已完成，你也可以去实名，让自己的签名更有法律保障", "docBatch.toUseSignatureTip": "请点击左侧“签名处”进行签名, 您也可以先实名，让自己的签名更有法律保障", "docBatch.batchSign": "批量签署", "docBatch.batchApprove": "批量审批", "docBatch.ssqNotReviewDiffTip": "上上签不对合同的当前版本与生效版本之间的内容差异进行审核，使用批量签署功能即代表您认可并同意签署以下合同的生效版本", "docBatch.chooseElectronicSeal": "选择印章", "docBatch.fileNumTip": "{num}份文件", "docBatch.totalFileNumTip": "共{num}份文件", "docBatch.chooseDefaultSignature": "选择默认签名", "docBatch.more": "更多", "docBatch.sign": "签署", "docBatch.file": "文件", "docBatch.signature": "签名", "docBatch.dataErrorTip": "数据出错，请重试！", "docBatch.noCanSignContract": "无可签署合同", "docBatch.noCanApproveContract": "无可审批合同", "docBatch.noReceiver": "未添加任何收件人", "docBatch.label": "标签", "docView.totalPageTip": "第{num}页，共{total}页", "docView.numOfPage": "页数", "docView.page": "页", "docView.canNotCheckContract": "无法查看合同", "docView.privateStoreContractTip": "发件方企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器", "docExport.exportRecord": "导出记录", "docExport.refresh": "刷新", "docExport.every1000ContractWaitingTime": "每1000条合同明细的等待时间约为半分钟", "docExport.fileName": "文件名", "docExport.createTime": "创建时间", "docExport.chooseTimePeriod": "选择时间段", "docExport.to": "至", "docExport.contractStatus": "合同状态", "docExport.operate": "操作", "docExport.download": "下载", "docExport.expired": "已过期", "docExport.waitExport": "等待导出", "docDetail.crossPlatformSign": "跨平台签署", "docDetail.platformSign.ja": "日本签约方", "docDetail.platformSign.zh": "中国签约方", "docDetail.signRole": "（签约角色）", "docDetail.scanCodeSign": "扫码签字", "docDetail.other": "其他", "docDetail.noRead": "未读", "docDetail.read": "已读", "docDetail.messageAndFaceVerify": "刷脸+验证码签署", "docDetail.back": "Назад", "docDetail.has": "已作废", "docDetail.now": "正在被作废:", "docDetail.for": "正在作废", "docDetail.doneInvalid": "完成作废", "docDetail.contractInfo": "Информация о контракте", "docDetail.basicInfo": "Основную информацию", "docDetail.contractNum": "Номер ", "docDetail.sender": "Отправитель", "docDetail.personAccount": "Личный кабинет ", "docDetail.entAccount": "Аккаунт компаний", "docDetail.operator": "Исполнитель", "docDetail.signStartTime": "Отправить время подписания договора", "docDetail.signDeadline": "Крайний срок подписания", "docDetail.contractExpireDate": "Окончание срока действия договора", "docDetail.none": "Ни один ", "docDetail.edit": "Редактировать ", "docDetail.settings": "Установка ", "docDetail.clear": "Clear", "docDetail.from": "Ресурс ", "docDetail.folder": "Папка", "docDetail.contractType": "Ти<PERSON> контракта", "docDetail.contractTypeBackup": "合同类型（备用）", "docDetail.reason": "Причина ", "docDetail.sign": "Подписать", "docDetail.approval": "утверждение", "docDetail.viewAttach": "Просмотреть страницу ", "docDetail.downloadContract": "Скачать договор", "docDetail.downloadAttach": "Скачать страницу ", "docDetail.print": "Распечатать ", "docDetail.certificatedTooltip": "Контракт и связанные с ним доказательства  были задокументированы в судебной цепочке Интернет-суда Ханчжоу.", "docDetail.needMeSign": "Нужно мне подписать ", "docDetail.needMeApproval": "Нужно мне утвердить", "docDetail.inApproval": "В процессе рассмотрения..", "docDetail.needOthersSign": "Подпись с другой стороны", "docDetail.signComplete": "Подписание завершено", "docDetail.signOverdue": "Просроченный подпись ", "docDetail.rejected": "Отказано в подписи", "docDetail.revoked": "Отозвано", "docDetail.contractCompleteTime": "Время подписания завершено", "docDetail.contractEndTime": "Время окончания подписи", "docDetail.reject": "Отказ в подписи", "docDetail.revoke": "Отменять", "docDetail.download": "Скачать", "docDetail.viewSignOrders": "Посмотреть порядок подписания", "docDetail.viewApprovalProcess": "Проверить процесс утверждения", "docDetail.completed": "Завершено ", "docDetail.cc": "Отправить копию", "docDetail.ccer": "Отправляющий", "docDetail.signer": "Под<PERSON>и<PERSON><PERSON><PERSON>т", "docDetail.signSubject": "Тема подписи", "docDetail.signSubjectTooltip": "Отправитель должен заполнить подписывающую сторону", "docDetail.user": "Пользователь", "docDetail.IDNumber": "Номер паспорта ", "docDetail.state": "Статус", "docDetail.time": "Время", "docDetail.notice": "Напоминать", "docDetail.detail": "Детально", "docDetail.RealNameCertificationRequired": "Требуется аутентификация личности", "docDetail.RealNameCertificationNotRequired": "Не требуется аутентификаций", "docDetail.MustHandwrittenSignature": "Обязательно подпишите рукой", "docDetail.handWritingRecognition": "手写笔迹识别", "docDetail.privateMessage": "Личное письмо ", "docDetail.attachment": "Прикрепленный файл ", "docDetail.rejectReason": "Причина ", "docDetail.notSigned": "Не подписано", "docDetail.notViewed": "Не проверено ", "docDetail.viewed": "Проверено ", "docDetail.signed": "Подписано", "docDetail.viewedNotSigned": "Прочтен но не подписан", "docDetail.notApproval": "Не утвержден ", "docDetail.remindSucceed": "Напоминание отправлено ", "docDetail.reviewDetails": "Детальное утверждение", "docDetail.close": "Закрыть", "docDetail.entInnerOperateDetail": "Детали процесса работы внутреннего отдела предприятии", "docDetail.approve": "同意", "docDetail.disapprove": "Отклонить", "docDetail.applySeal": "Заявка на испльзование печати", "docDetail.applied": "Зарегистрировано", "docDetail.apply": "Подавать заявление", "docDetail.toOtherSign": "Переслать другому человеку для подписи", "docDetail.handOver": "Передать", "docDetail.approvalOpinions": "Резолюция", "docDetail.useSeal": "Приложить печать", "docDetail.signature": "Подписать", "docDetail.use": "Использовать", "docDetail.date": "Дата", "docDetail.fill": "Заполнить", "docDetail.times": "Раз", "docDetail.place": "Место-пункт", "docDetail.contractDetail": "Детали контракта", "docDetail.viewMore": "Посмотреть больше", "docDetail.collapse": "Сложить", "docDetail.signLink": "Подписать ссылку", "docDetail.saveQRCode": "Сохранить штрих-код или скопировать ссылку, поделиться со стороной  подписанта", "docDetail.saveWXCode": "保存程序码或复制链接，分享给签署方", "docDetail.changeQRCode": "切换二维码及链接", "docDetail.changeWXCode": "切换小程序码及链接", "docDetail.signQRCode": "Подписать ссылку штрих-кода", "docDetail.showlinkDec": "", "docDetail.copyUrl": "复制链接", "docDetail.copy": "Копия", "docDetail.copySucc": "Копия завершена", "docDetail.copyFail": "Копия не удалась", "docDetail.certified": "сертифицировано", "docDetail.unCertified": "неподтвержденный", "docDetail.claimed": "已认领", "docDetail.unSort": "не отсортированный", "docDetail.signPage": "Количество страниц: {page} страницы", "docDetail.handWriteNotAllowed": "Рукописные подписи не допускаются", "docDetail.entSign": "签字", "docDetail.stamp": "盖章", "docDetail.stampSign": "盖章并签字", "docDetail.requireEnterIdentityAssurance": "启用经办人身份核验", "docDetail.noNeedToSign": "已无需签署", "docDetail.requestSeal": "业务核对章", "docDetail.requestSealAgree": "符合章", "docDetail.requestSealRefuse": "不符合章", "docDetail.requestSealRemark": "不符合章的备注", "docDetail.viewContentInfo": "查看合同内容字段", "docDetail.contractContentInfo": "合同内容字段", "docDetail.contentDate": "日期", "docDetail.combobox": "下拉框", "docDetail.number": "数字", "docDetail.text": "文本", "docDetail.notFilled": "此处未填写", "docDetail.radio": "单选", "docDetail.checkbox": "多选", "docDetail.filledBySender": "由发件方填写", "docDetail.filledBy": "由{writer}填写", "docDetail.empty": "结果为空", "docDetail.changeToPaperSign": "已改用纸质签署", "docDetail.changeContractStatusForPaperSign": "修改状态", "docDetail.changeContractStatus": "逾期后延期", "docDetail.changeContractStatusConfirm.title": "逾期后延期", "docDetail.changeContractStatusConfirm.tip.0": "将合同状态签署中修改为“签约完成”，未签署签约方将被标注为“改用纸质签”。", "docDetail.changeContractStatusConfirm.tip.1": "合同状态变更后不可还原，请在收到盖章的纸质原件后再点击确定。 ", "docDetail.changeContractStatusConfirm.confirm": "确定", "docDetail.changeContractStatusConfirm.cancel": "取消", "docDetail.hasUsePaperSign": "(已启用纸质签)", "docDetail.signedAging": "签署时效", "docDetail.day": "天", "docDetail.hour": "小时", "docDetail.minute": "分", "docDetail.accountChangeTip": "该用户账号由“{previous}”变更为“{current}”", "docDetail.byEncryptionSign": "通过加密签署方式签署", "docDetail.password": "密码", "docDetail.byTwoFactorSign": "通过二要素校验签署", "chooseEntFolder.title": "选择企业履约管理系统", "chooseEntFolder.desc": "请选择需要进入的企业履约管理系统：", "syncEntFolder.title": "同步至履约文件夹", "syncEntFolder.createFolder": "新建文件夹", "exportContract.title": "导出范围", "exportContract.content1": "导出的范围为当前搜索结果中的全部合同的明细（明细字段与合同列表的表头一致）。", "exportContract.content2": "导出结果中是否要包含多文档合同中的各个子合同?", "exportContract.radioText.0": "是", "exportContract.radioText.1": "否", "exportContract.cancel": "取消", "exportContract.export": "开始导出", "scanCodeRemind.tip": "提示", "scanCodeRemind.confirm": "我知道了", "scanCodeRemind.content": "该合同含有扫码签字的签署方，请到该合同的详情页下载查验码后自行发送进行通知。", "scanCodeRemind.detailContent": "该签约主体是扫码签字的签署方，请您下载合同查验码后自行发送进行通知。", "crossplatformList.notice": "您有{number}份跨平台签署的合同，请点此前往处理", "crossplatformList.breadcrumb": "跨平台签署的合同", "crossplatformList.viewDetail": "查看详情", "consts.shortcutMap.myFolders": "我的履约文件夹", "consts.shortcutMap.allFolders": "所有履约文件夹", "consts.contractStatus.all": "Все статусы", "consts.contractStatus.needMeSign": "Ожидать моего подписи", "consts.contractStatus.needMeApproval": "Ожидать моего утверждения", "consts.contractStatus.inApproval": "В процессе рассмотрения..", "consts.contractStatus.needOthersSign": "Подпись с другой стороны", "consts.contractStatus.signComplete": "Подписание завершено", "consts.contractStatus.signOverdue": "Просроченный подпись", "consts.contractStatus.rejected": "Отказано в подписи", "consts.contractStatus.revoked": "Отозвано", "consts.contractStatus.invalid": "已作废", "consts.rejectReasonList.signOperateReason": "对签署操作/校验操作有疑问，需要进一步沟通", "consts.rejectReasonList.termReason": "对合同条款/内容有疑议，需要进一步沟通", "consts.rejectReasonList.explainReason": "对合同内容不知情，请提前告知", "consts.rejectReasonList.otherReason": "其他（请填写理由）", "consts.templatePermissionMap.sendContract": "发送合同", "consts.templatePermissionMap.modifyDocument": "调整文档", "consts.templatePermissionMap.modifyReceiver": "调整签约方", "consts.templatePermissionMap.addCCReceiver": "添加抄送方", "consts.templatePermissionMap.modifySignRequirement": "调整签署要求", "consts.templatePermissionMap.dragSignLabel": "移动签署字段（含签署位置）", "consts.templatePermissionMap.modifySignLabel": "新增/删除签署字段（含签署位置）", "consts.templatePermissionMap.editable": "编辑模板", "consts.templatePermissionMap.templateDuplicate": "复制模板", "consts.templatePermissionMap.modifyDocumentFederation": "配置文档组合", "consts.templatePermissionMap.invalidStatement": "设置作废申明", "consts.templatePermissionMap.grantManage": "权限分配", "consts.templatePermissionMap.editCustomScene": "场景定制", "consts.templatePermissionMap.setDefaultValue": "设置默认值", "consts.templatePermissionMap.editSupplyAgree": "补充协议", "consts.templatePermissionDesc.useTmp.name": "使用模板的权限", "consts.templatePermissionDesc.useTmp.tabName": "使用模板", "consts.templatePermissionDesc.useTmp.sendContract": "允许使用当前模板发送合同。在此权限的基础上才能授予其他使用模板权限", "consts.templatePermissionDesc.useTmp.modifyDocument": "使用模板发送合同时允许上传新的合同文档或合同附件，也允许在使用时临时删除模板中某个已上传的文档", "consts.templatePermissionDesc.useTmp.modifyReceiver": "使用模板发送合同时允许修改签约方的账号、名称，新增或删除签约方（需要配合“新增签署字段”权限一起使用）", "consts.templatePermissionDesc.useTmp.modifySignRequirement": "使用模板发送合同时允许在使用时修改签署要求", "consts.templatePermissionDesc.useTmp.dragSignLabel": "使用模板发送合同时，允许调整已设置的盖章处、签名处、业务字段的位置", "consts.templatePermissionDesc.useTmp.modifySignLabel": "使用模板发送合同时，允许为签约方新增盖章处、签名处", "consts.templatePermissionDesc.useTmp.setDefaultValue": "发送合同时，可以为签署方写入字段的初始值", "consts.templatePermissionDesc.useTmp.addCCReceiver": "使用模板发送合同时仅允许添加被抄送者的账号", "consts.templatePermissionDesc.manageTmp.name": "管理模板的权限", "consts.templatePermissionDesc.manageTmp.tabName": "管理模板", "consts.templatePermissionDesc.manageTmp.editable": "允许在合同模板列表页点击“编辑”按钮编辑模板，也支持删除模板、启用/停用模板", "consts.templatePermissionDesc.manageTmp.templateDuplicate": "允许在合同模板列表页点击“复制”按钮复制模板，动态模板暂不支持", "consts.templatePermissionDesc.manageTmp.modifyDocumentFederation": "允许在模板文档列表页新增、删除、修改文档组合", "consts.templatePermissionDesc.manageTmp.modifySceneConfig": "允许在模板详情页配置场景定制项", "consts.templatePermissionDesc.manageTmp.invalidStatement": "如何废除签署完成的模板合同", "consts.templatePermissionDesc.manageTmp.editSupplyAgree": "设置当前模板的关联模板，可让关联模板发送给相同签约方的合同，成为当前模板的合同的补充协议", "consts.templatePermissionDesc.manageGrant.name": "管理权限的权限", "consts.templatePermissionDesc.manageGrant.tabName": "管理权限", "consts.templatePermissionDesc.manageGrant.grantManage": "允许在合同模板列表页点击“授权成员”或“授权角色”将模板授权给其他人", "consts.entFolderPermissionMap.contractBorrow": "合同借阅", "consts.entFolderPermissionMap.borrowApproval": "修改借阅审批人", "consts.entFolderPermissionMap.viewList": "查看列表明细", "consts.entFolderPermissionMap.downloadList": "导出列表明细", "consts.entFolderPermissionMap.viewContract": "查看履约详情页", "consts.entFolderPermissionMap.downloadContract": "下载合同", "consts.entFolderPermissionMap.invalidContract": "作废合同", "consts.entFolderPermissionMap.resendContract": "重新发送合同", "consts.entFolderPermissionMap.setTag": "设置标签", "consts.entFolderPermissionMap.distriButtonPermission": "转交及权限分配", "consts.entFolderPermissionMap.syncHronize": "个人文件夹同步", "consts.entFolderPermissionMap.remove": "删除合同", "consts.entFolderPermissionMap.group": "合同分组", "consts.entFolderPermissionMap.edit": "修改文件夹", "consts.entFolderPermissionMap.folderManageMaterial": "管理履约材料", "consts.entFolderPermissionMap.fulfillMaterialTip": "可在履约详情页设置标签，可上传或删除履约过程中产生的文件", "hybridBusiness.isCheckingNet": "Проверка гибридной облачной сетевой среды", "mixin.createSuccessful": "创建成功", "mixin.setLabel": "设置标签", "certificationRenewalDialog.renewalTitle": "数字证书续期", "certificationRenewalDialog.renewalTip": "您的证书已过期，为避免文件签署无法正常进行，请及时续期", "certificationRenewalDialog.previousIdentity": "持有证书的主体：", "certificationRenewalDialog.previousCA": "原证书颁发机构：", "certificationRenewalDialog.previousExpiryDate": "原证书有效期：", "certificationRenewalDialog.previousId": "原证书序列号：", "certificationRenewalDialog.renewal": "同意续期", "pdf.previewFail": "文件预览失败", "pdf.pager": "第{x}页，共{y}页", "pdf.parseFailed": "解析pdf文件失败，请点击“确定”重试", "pdf.confirm": "确定", "tagManage.title": "设置标签", "importOffLineDoc.importDoc": "导入合同", "importOffLineDoc.step0Title": "第一步：确认导入企业名称", "importOffLineDoc.step1Title": "第二步：上传Excel", "importOffLineDoc.step2Title": "第三步：上传合同文件", "importOffLineDoc.step1Info": "请先下载Excel模板，填写完成后再导入,合同数量不超过1000。", "importOffLineDoc.next": "下一步", "importOffLineDoc.entName": "企业名称", "importOffLineDoc.archiveFolder": "归档文件夹", "importOffLineDoc.downloadExcel": "下载Excel", "importOffLineDoc.uploadExcel": "上传Excel", "importOffLineDoc.reUploadExcel": "重新上传", "importOffLineDoc.step2Info.0": "1. 合同文件只能是PDF或图片；", "importOffLineDoc.step2Info.1": "2. 所有合同文件放置在一个文件夹后，将文件夹压缩为zip（不超过150M）；", "importOffLineDoc.step2Info.2": "3. 文件名称包含文件后缀名（如.pdf）需要与第二步中的Excel里填写的文件名称一一对应；", "importOffLineDoc.uploadZip": "点击上传Zip", "importOffLineDoc.reUploadZip": "重新上传Zip", "importOffLineDoc.done": "确定", "importOffLineDoc.back": "返回", "importOffLineDoc.contractTitle": "合同名称", "importOffLineDoc.singerAccount": "签署人账号", "importOffLineDoc.singerName": "签署人名称", "importOffLineDoc.uploadSucTip": "上传成功，点击\"确定\"按钮开始导入", "importOffLineDoc.outbox": "Исходящий", "importOffLineDoc.fileLessThan": "请上传小于{num}M的文件", "importOffLineDoc.fileTypeValid": "只能上传{type}格式的文件!", "approvalDetail.submitter": "提交人", "approvalDetail.signatory": "Под<PERSON>и<PERSON><PERSON><PERSON>т", "approvalDetail.reviewSchedule": "审批进度", "approvalDetail.downloadFile": "下载源文件", "approvalDetail.content": "内容", "approvalDetail.document": "文档", "approvalDialog.stepTip.0": "请为不同的合同类型，设置相应审批流程", "approvalDialog.stepTip.1": "请为各审批流，填写相应的内容", "approvalDialog.back": "返回", "approvalDialog.next.0": "下一步", "approvalDialog.next.1": "提交审批", "approvalDialog.chooseApprover": "选择审批人：", "approvalDialog.completeApprovalFlow": "您提交的审批流程不完整，请补全后重新提交", "approvalDialog.viewPrivateLetter": "查看私信", "approvalDialog.addPrivateLetter": "添加私信", "cancelContract.confirm": "Подтвердить", "cancelContract.selectRejectReason": "Пожалуйста, выберите причину отказа в подписании", "cancelContract.reasonWriteTip": "请填写拒签理由", "cancelContract.refuseReasonOther": "更多拒签理由（可不填） | 更多拒签理由（必填）", "cancelContract.inputRejectReason": "Укажите причину отказа, чтобы помочь другой стороне понять вашу проблему и ускорить процесс заключения контракта", "cancelContract.reject": "Отказ в подписи", "cancelContract.pwdWrong": "Неверный пароль подписи, вы можете ввести еще 2/1 раз", "cancelContract.forgetPwd": "Забыли пароль", "cancelContract.pwdLocked": "Пароль заблокирован, через 3 часа можно автоматически разблокировать , затем можете пройти", "cancelContract.retrievePwd": "Найти пароль", "cancelContract.unlock": "Разблокировать сейчас ", "cancelContract.inputReason": "Введите {type} причину (можно не заполнять)", "cancelContract.revokeTips": "撤回后合同接收方不能查看合同内容，但可以查看撤回原因。| 如果接收方尚未收到合同（如合同尚未通过发送前审批，或顺序签署但尚未轮到签署）则将完全看不到此合同。", "cancelContract.revokeReasons": "合同内容需做修改 | 接收人账号需做修改 | 签署人无需接收此合同", "cancelContract.revokeOtherReason": "其他", "cancelContract.rejectTips": "Этот договор не может быть подписан после отказа ", "cancelContract.signPwd": "Пароль для подписи", "cancelContract.inputPwd6": "Пожалуйста, введите 6-значный пароль для подписи", "cancelContract.inputPwd": "Пожалуйста, введите  пароль для подписи", "cancelContract.inputNumber6": "Пожалуйста, введите 6 цифр", "cancelContract.mail": "E-mail", "cancelContract.phone": "номер телефона", "cancelContract.verify": "Код подтверждения", "cancelContract.mailVerify": "验证码", "cancelContract.otherNotice.1": "Все время не получаете СМС сообщение? Пожалуйста, попробуйте еще раз ", "cancelContract.otherNotice.2": "Голосовой код подтверждения ", "cancelContract.otherNotice.3": "или ", "cancelContract.otherNotice.4": "СМС подтверждение ", "cancelContract.otherNotice.5": "код подтверждения почтового ящика ", "cancelContract.sendSucc": "Успешно отправлен", "cancelContract.sendInternalErr": "Интервал отправки слишком короткий ", "cancelContract.getVerifyCode": "Пожалуйста, получите сначала код подтверждения", "cancelContract.succ": "{type} успешно", "contractMove.newFolder": "Новая папка", "contractMove.plsInput": "请输入", "contractMove.archive": "归档", "contractMove.noFolder": "Файлов пока нет", "contractMove.confirm": "Подтвердить", "contractMove.cancel": "Отменить", "contractMove.folderPlaceholder": "Пожалуйста, введите наименование файла", "contractMove.fileSucc": "Успешно архивировано", "contractsTransferDialog.originOwner": "原持有人", "contractsTransferDialog.contractTransfer": "合同转交", "contractsTransferDialog.newOwner": "新持有人", "contractsTransferDialog.searchTip": "Поддержка ввода учетной записи / поиска имени", "contractsTransferDialog.confirm": "Подтвердить", "contractsTransferDialog.chooseNewOwner": "请选择新持有人", "contractsTransferDialog.notCliam": "合同未被认领", "contractsTransferDialog.bizTransfer": "跨业务线转交", "contractsTransferDialog.innerTransfer": "业务线内部转交", "contractsTransferDialog.chooseMultiLine": "选择业务线", "contractsTransferDialog.chooseAccount": "选择账号", "contractsTransferDialog.displayAccount": "仅展示前50个账号", "contractsTransferDialog.transferRoles": "您将转交以下角色的合同：", "contractsTransferDialog.tip": "提示", "contractsTransferDialog.continue": "继续", "contractsTransferDialog.know": "我知道了", "contractsTransferDialog.cancel": "取消", "linkContract.title": "关联合同", "linkContract.connectMore": "关联到", "linkContract.placeholder": "请输入主合同编号", "linkContract.contractNoLengthTip": "合同编号需为19位数字", "linkContract.revoke": "合同已撤回", "linkContract.overdue": "逾期未签", "linkContract.approvalNotPassed": "审批被驳回", "linkContract.reject": "合同已拒签", "linkContract.invalid": "合同已作废", "linkContract.signing": "签署中", "linkContract.complete": "已完成", "linkContract.approvaling": "审批中", "linkContract.disconnect": "解除关联", "linkContract.disconnectSuccess": "解除关联成功", "linkContract.connectLimit": "关联合同数量上限为100份", "linkContract.submit": "Подтвердить", "linkContract.cancel": "Отменить", "linkContract.entAccount": "Аккаунт компаний", "linkContract.personAccount": "Личный кабинет ", "linkContract.whetherMasterContract": "是否是主合同", "qrCodeTab.pleaseScanToHandleWrite": "请用微信或者手机浏览器扫码，在移动设备上手写签名", "qrCodeTab.save": "保存", "selectSignType.chooseSignType": "Выбрать модель подписи ", "selectSignType.useTemplate": "使用模板", "selectSignType.useLocalFile": "Загрузить локальные файлы", "signValidation.VerCodeVerify": "Проверить код подтверждение", "signValidation.QrCodeVerify": "Проверка QR-кода", "signValidation.verifyTip": "Bestsign в данный момент проверяет ваш безопасный цифровой сертификат, вы находитесь в защищенной среде электронной подписи, пожалуйста, будьте спокойны в подписывании!", "signValidation.verifyAllTip": "上上签正在调用企业数字证书和您的个人数字证书，您正在安全签约环境中，请放心签署！", "signValidation.appScanVerify": "上上签APP扫码校验", "signValidation.downloadBSApp": "下载上上签APP", "signValidation.scanned": "Scan code successfully", "signValidation.confirmInBSApp": "Please confirm your signature in BestSign APP", "signValidation.appKey": "APP安全校验", "signValidation.goToScan": "去扫码", "signValidation.signSuc": "Успешно подписано", "signValidation.if": "，是否", "signValidation.forgetPassword": "Забыли пароль", "signValidation.signPsw": "Пароль для подписи", "signValidation.signPwdType": "请输入6位数字", "signValidation.email": "邮箱", "signValidation.phoneNumber": "номер телефона", "signValidation.setNotificationInUserCenter": "请先到用户中心设置通知方式", "signValidation.mailVerificationCode": "验证码", "signValidation.verificationCode": "Код подтверждения", "signValidation.msgTip": "Все время не получаете СМС сообщение? Пожалуйста, попробуйте еще раз ", "signValidation.voiceVerCode": "Голосовой код подтверждения", "signValidation.or": "или ", "signValidation.SMSVerCode": "СМС подтверждение ", "signValidation.emailVerCode": "код подтверждения почтового ящика ", "signValidation.doNotWantUseVerCode": "不想用验证码", "signValidation.try": "试试", "signValidation.goToFaceVerify": "去刷脸", "signValidation.submit": "Подтвердить", "signValidation.SentSuccessfully": "Успешно отправлен", "signValidation.intervalTip": "Интервал отправки слишком короткий ", "signValidation.signVerification": "Подписать", "signValidation.appliedSeal": "用印申请已提交", "signValidation.operationCompleted": "Операция завершена", "switchSubject.chooseIdentity": "选择身份", "switchSubject.confirm": "Подтвердить", "switchSubject.cancel": "Отменить", "unverifyConfirmDialog.tip": "Напоминать", "unverifyConfirmDialog.isGroupProxyAuth": "本企业目前认证状态为集团代认证，合同收件人将无法识别您的身份，建议您先补充实名认证材料。", "unverifyConfirmDialog.unverify": "Вы не прошли подтверждение личности, получатель контракта не сможет вас идентифицировать. Рекомендуем сначала выполнить аутентификацию на основе настоящей имени.", "unverifyConfirmDialog.goAuthenticate": "Перейти к аутентификацию ", "unverifyConfirmDialog.theEnterprise": "该企业", "unverifyConfirmDialog.you": "您", "unverifyConfirmDialog.enterprise": "Компания ", "contractQrCodeDialog.selectVerifyCode": "选择查验码", "contractQrCodeDialog.viewVerifyCode": "查看样式", "contractQrCodeDialog.preview": "预览", "contractQrCodeDialog.msg.selectVerifyCode": "请先选择查验码", "contractQrCodeDialog.msg.success": "更换成功", "addAttachmentConfig.dialogTitle": "配置附件字段", "addAttachmentConfig.fieldInd": "序号", "addAttachmentConfig.fieldName": "字段名称", "addAttachmentConfig.fieldType": "字段内容", "addAttachmentConfig.fieldRequire": "是否必填", "addAttachmentConfig.lengthLimit": "不超过20个字", "addAttachmentConfig.type": "Pdf、Word、Excel及图片格式", "addAttachmentConfig.notRequire": "选填", "addAttachmentConfig.addField": "新增字段", "addAttachmentConfig.submit": "保存", "addAttachmentConfig.cancel": "取消", "addAttachmentConfig.requireName": "还有名称未填写", "addAttachmentConfig.saveSucc": "保存成功", "authInfoChange.title": "实名信息变更检测", "authInfoChange.confirm": "确认", "authInfoChange.changeAuth": "更新实名", "authInfoChange.notifyAdmin": "通知管理员", "authInfoChange.notifySuccess": "通知成功", "authInfoChange.operateSuccess": "操作成功", "authInfoChange.warningTip.tip1": "系统检测到您已在上上签平台实名，但使用的企业信息不是您最新的工商备案信息。", "authInfoChange.warningTip.tip2": "为保证您签署的电子合同合规性和效力，请使用最新的企业信息进行重新实名。", "authInfoChange.suggestTip.tip1": "如您企业为集团架构，请联系您的专属CSM、或者拨打上上签署客服热线400-993-6665办助您完成实名认证信息的更新。更新后，方可继续签署。", "authInfoChange.suggestTip.tip2": "点击【通知管理员{adminInfo}】，", "authInfoChange.suggestTip.tip3": "可以立即发送通知给管理员，引导管理员去重新实名。您也可线下通知，及时推动业务开展。", "excelScale.setExcelScale": "设置Excel缩放", "excelScale.info": "开启该功能，Excel所有列会被缩放至一页展示。", "excelScale.on": "开启缩放功能", "excelScale.line.tip1": "开启功能将对模板后续所有上传文档和附件生效；", "excelScale.line.tip2": "使用模板时会继承模板设置", "excelScale.saveSucc": "保存成功", "excelScale.saveFail": "保存失败", "internalSignConfig.configTitle.0": "仅用于对内文件签字场景设置", "internalSignConfig.configTitle.1": "已设置为“仅用于内部文件签字场景”", "internalSignConfig.title": "仅用于对内文件签字场景设置", "internalSignConfig.tip.0": "该功能不适用于正式合同（如劳动合同）开启该功能后，模板的使用方式将受到如下限制：", "internalSignConfig.tip.1": "· 只能添加企业内部的成员账号作为签署人", "internalSignConfig.tip.2": "· 签署人只能使用企业签字的方式", "internalSignConfig.checkboxInfo.0": "允许签字人不实名签署（在法律效力上比不上实名签署）。不勾选则必须实名。", "internalSignConfig.checkboxInfo.1": "允许签字人在签署合同时无需签署校验即可完成签署。不勾选则必须通过验证码（或签约密码、刷脸等等方式）校验才能完成签署。", "internalSignConfig.checkboxInfo.2": "允许签字人在签署合同时无需手写签名（可以使用默认签名）。不勾选则每次签署必须手写签。", "internalSignConfig.setAliasTip": "设置合同对外的“合同别名”", "internalSignConfig.setAliasInfo": "进入模板详情页—场景定制中，可以设置对外的“合同别名”。", "permissionHeader.hi": "你好", "permissionHeader.exit": "退出", "permissionHeader.help": "帮助", "permissionHeader.hotline": "服务热线", "paperSign.title": "使用纸质方式签署", "paperSign.stepText.0": "下一步", "paperSign.stepText.1": "确认纸质签", "paperSign.stepText.2": "确定", "paperSign.needUploadFile": "请先上传扫描件", "paperSign.uploadError": "上传失败", "paperSign.cancel": "取消", "paperSign.downloadPaperFile": "获取纸质签文件", "paperSign.step0.title": "您需要先下载打印合同，加盖物理章后，邮寄给发件方。", "paperSign.step0.address": "邮寄地址：", "paperSign.step0.contactName": "接收人姓名：", "paperSign.step0.contactPhone": "接收人联系方式：", "paperSign.step0.defaultValue": "请通过线下方式向发件方索取", "paperSign.step1.title0": "第一步：下载&打印纸质合同", "paperSign.step1.title0Desc.0": "下载打印的合同应包含已签署的电子章的图案。请", "paperSign.step1.title0Desc.1": "获取纸质签文件。", "paperSign.step1.title1": "第二步：加盖印章", "paperSign.step1.title1Desc": "在纸质合同上加盖合同有效的公司印章。", "paperSign.step1.title2.0": "第三步：", "paperSign.step1.title2.1": "上传扫描件，", "paperSign.step1.title2.2": "填写验证码，完成纸质签", "paperSign.step1.title2Desc.0": "将纸质合同扫描转换成合同扫描件（PDF格式文件）后上传，", "paperSign.step1.title2Desc.1": "再填写验证码后，即完成签署。电子合同中不展示您的印章图案，但会记录您此次操作过程。", "paperSign.step2.title.0": "将纸质合同扫描件（PDF格式文件）上传", "paperSign.step2.title.1": "请确认纸质合同已下载并签署后，再点击确定按钮，结束纸质签署流程。", "paperSign.step2.uploadFile": "上传扫描件", "paperSign.step2.getCodeVerify": "获取合同签署校验", "paperSign.step2.isUploading": "上传中...", "paperSign.operationCompleted": "操作完成", "changeSignDeadline.title": "修改合同状态", "changeSignDeadline.desc": "点击确定后，将有如下变化：", "changeSignDeadline.changeTime": "将合同状态“逾期未签”修改为“签署中”，并且合同签约截止时间更改为： {signDeadline}", "changeSignDeadline.changeTimeTip": "为签署人设置的“签署时效”将不再生效", "changeSignDeadline.choose": "您可选择：", "changeSignDeadline.checkSignNotice": "向未签署合同的账号推送签约提醒", "changeSignDeadline.confirmChange": "是否确认修改合同状态？ ", "changeSignDeadline.cancel": "取消", "changeSignDeadline.confirm": "确定", "changeSignDeadline.signDeadlineTip": "每份合同最多只能延期3次", "addEntFolder.title": "新建履约文件夹", "addEntFolder.ent": "所属企业", "addEntFolder.entHolder": "请选择", "addEntFolder.folderName": "履约文件夹名称", "addEntFolder.folderNameHolder": "不超过20个字，需唯一，必填", "addEntFolder.mark": "备注", "addEntFolder.markHolder": "不超过100字，选填", "addEntFolder.cancel": "取消", "addEntFolder.confirm": "确定", "addEntFolder.folderNameTip": "履约文件夹名称不能为空！", "entContractDownload.failTitle": "以下文档下载失败", "entContractDownload.documentId": "文档ID", "entContractDownload.documentTitle": "文档名称", "entContractDownload.failReason": "失败原因", "fileLimit.fileLessThan": "Загрузка файла меньше {num}м", "fileLimit.beExcel": "Загрузка файла Excel", "fileLimit.beAttachmentFile": "请上传Pdf、Word、Excel或图片", "fileLimit.usePdf": "при загрузке используйте файл PDF или рисунок", "fileLimit.beZip": "请上传Zip,7z压缩文件", "fileLimit.fileNameMoreThan": "имя файла с длиной более {num}, будет автоматически удалено", "regs.entNameMaxLength": "企业名称不能超过60个字符", "unPermissionRemind.title": "没有权限", "unPermissionRemind.content.noAuth": "签约企业：{receiverEntName}暂未通过系统实名认证，无法完成合同签署业务，需要进行企业认证。", "unPermissionRemind.content.noJoin": "该文件由{senderEntName}发给{receiverEntName}，需要使用企业电子印章进行签署。查询到您未加入企业系统，无签署权限，需要申请。", "unPermissionRemind.content.noView": "该文件由{senderEntName}发给{receiverEntName}，查询到您无权限查看，需要申请。", "unPermissionRemind.content.noSign": "该文件由{senderEntName}发给{receiverEntName}，需要使用企业电子印章进行签署。查询到您无签署权限，需要申请。", "unPermissionRemind.transferTip": "您也可以转交给具有印章权限的人员进行签署，转交签署成功后我们将会告知给您。", "unPermissionRemind.transferBtn": "去转交签署 >>", "unPermissionRemind.giveOtherAuthTip": "您也可以转交给其他管理人员进行认证。", "unPermissionRemind.giveOtherAuthBtn": "转他人认证 >>", "unPermissionRemind.noAuthPaperSign": "当您无法完成企业认证时，也可选择下载打印，进行纸质签署。", "unPermissionRemind.otherPaperSign": "您也可以选择下载合同文档并打印，交由企业印章负责人线下盖章签署。", "unPermissionRemind.paperSignBtn": "转纸质签署 >>", "unPermissionRemind.applyBtn": "去申请", "unPermissionRemind.authBtn": "去认证", "unPermissionRemind.returnBtn": "返回", "unPermissionRemind.transferContract": "移交", "unPermissionRemind.cantTransferTip": "收件方企业存在多业务线，暂不支持移交", "applyPermission.transferAdmin": "转交主管理员", "applyPermission.transferAdminTip": "转交主管理员之后，将为您保留以下权限，您也可以选择更多权限。", "applyPermission.giveOtherAuth": "转他人认证", "applyPermission.giveOtherAuthTip": "转交他人认证的过程中，同时为您申请以下权限，以便顺利完成合同签署。您也可以根据实际需要选择更多权限。", "applyPermission.rightApply": "权限申请", "applyPermission.adminName": "系统主管理员姓名", "applyPermission.account": "账号", "applyPermission.applyUserName": "申请人姓名", "applyPermission.senderNamePlaceholder": "请输入您的姓名", "applyPermission.senderAccountPlaceholder": "请输入您的账号", "applyPermission.specialSeal": "模板专用章", "applyPermission.electronicSeal": "电子公章", "applyPermission.phoneOrEmail": "手机号/邮箱", "applyPermission.applyRight": "申请权限", "applyPermission.ent": "企业", "applyPermission.receiverAccountPlaceholder": "请输入被转交人账号", "applyPermission.sender": "转交人", "applyPermission.yourName": "您的姓名", "applyPermission.applyName": "申请人姓名", "applyPermission.moreRight": "更多权限", "applyPermission.confirmAdminTip": "我是企业管理人员，我要", "applyPermission.confirmAdminBtn": "申请成为主管理员", "applyPermission.otherWayApply": "其他方式申请", "applyPermission.applyToAdmin": "向主管理员申请", "applyPermission.sendBtn": "发送通知", "applyPermission.applyRightTip": "当您不认识主管理员时，可选择其他方式申请，获取企业其它管理人员或者印章管理人员的授权，即可查看合同。", "applyPermissionResult.title": "通知成功，等待处理", "applyPermissionResult.content11": "主管理员（{name}，{account}）审批完成后，您将收到短信通知。", "applyPermissionResult.content12": "主管理员（{account}）审批完成后，您将收到短信通知。", "applyPermissionResult.content2": "被转交人{account}收到通知后，将会对您的转交请求进行处理。处理结果我们将以短信形式告知给您。", "applyPermissionResult.returnBtn": "返回", "morePermissionDialog.title": "更多权限", "morePermissionDialog.confirmBtn": "确定", "morePermissionDialog.cancel": "取消", "morePermissionDialog.viewContract": "查看企业合同全文", "morePermissionDialog.nowContract": "当前合同", "morePermissionDialog.nowSenderProxyContract": "当前发件方发送的无人处理的合同", "morePermissionDialog.allSenderProxyContract": "全部发件方发送的无人处理的合同", "morePermissionDialog.proxyContractTip": "无人处理是指发给企业后，无人去查看签署；获得该权限以后，即可通过通知链接直接查看和签署此类合同。", "morePermissionDialog.signContract": "签署企业合同（电子公章）", "morePermissionDialog.nowSenderContract": "当前发件方发送的合同", "morePermissionDialog.allSenderContract": "全部发件方发送的合同", "morePermissionDialog.noApplySignRight": "我不签署，不需要章", "morePermissionDialog.canSignTip": "在可查看范围内允许签署。", "morePermissionDialog.allTip": "说明：当前发件方包含该企业及其集团和子公司、业务线。", "applyToBeAdminDialog.title": "申诉成为主管理员", "applyToBeAdminDialog.content1": "您正在申诉成为{entName}企业的系统主管理员，主管理员主要职责与权限有：", "applyToBeAdminDialog.content2": "1、企业印章使用与分配 | 2、企业成员管理 | 3、企业合同管理", "applyToBeAdminDialog.tip": "更多主管理员功能可在申诉成功后，登录电脑端上上签平台 http://ent.bestsign.cn查看。", "applyToBeAdminDialog.footer": "系统主管理员通常由企业法定代表人、财务管理者、法务管理者、IT部门管理者或企业业务负责人等角色担任，以确保职责的有效履行。", "applyToBeAdminDialog.confirm": "去申诉", "applyToBeAdminDialog.cancel": "取消", "components.sendPointPosition.index-1c6dcb-1": "有盖章处/签字处没能匹配到关键字，暂时放置在合同第一页左下角，需要你一一手动调整。", "components.sendPointPosition.index-1c6dcb-2": "温馨提示", "components.sendPointPosition.index-1c6dcb-3": "确定", "components.sendPointPosition.index-1c6dcb-4": "所在文档：", "operate.certificateReport": "电子签约存证报告", "operate.notarization": "公证书", "operate.selectProject": "请选择出证申请项目", "operate.downloadAttacment": "下载签约存证页", "operate.downloadAttacmentTip": "上上签签约存证页为记录合同基本信息，以及合同从发送开始到签署完毕的过程中的签署者和签署信息。", "special-doc-dialog.tip1": "系统检测到文档存在异常，需要预处理后才能继续发起合同，整个过程由系统自动完成", "special-doc-dialog.tip2": "需要预处理的常见原因", "special-doc-dialog.tip3": "1、文档各页规格不统一", "special-doc-dialog.tip4": "2、文档是由多个小文档拼接而成，中间某几页做过旋转处理", "special-doc-dialog.tip5": "预处理后的效果", "special-doc-dialog.tip6": "1、原文档已添加了CA证书或使用其他加密策略的，在预处理后会失效", "special-doc-dialog.tip7": "2、发出的合同上的文字不能被选中、复制", "special-doc-dialog.tip8": "3、文档清晰度可能会变低", "special-doc-dialog.tip9": "请点击“继续”按钮，以完成合同发送流程", "SsoConfirm.index-c220bb-1": "", "SsoConfirm.index-c220bb-2": "", "SsoConfirm.index-c220bb-3": "", "SsoConfirm.index-c220bb-4": "", "SsoConfirm.index-c220bb-5": "", "SsoConfirm.index-c220bb-6": "", "SsoConfirm.index-c220bb-7": "", "SsoConfirm.index-c220bb-8": "", "SsoConfirm.index-c220bb-9": "", "SsoConfirm.index-c220bb-10": "", "SsoConfirm.index-c220bb-11": "", "SsoConfirm.index-c220bb-12": "", "SsoConfirm.index-c220bb-13": "", "templateReceiverConfig.err.needAddSender": "未设置本企业/本人作为签约方，合同发出后，你方将不会参与签署过程。是否需要将你方加入签署？", "templateReceiverConfig.err.addSender": "添加为签约方", "templateReceiverConfig.err.needSeal": "根据【盖章】的签署要求，需在下列文件中设置【盖章】签署位置", "templateReceiverConfig.err.needSignature": "根据【签字】的签署要求，需在下列文件中设置【签字】签署位置", "templateReceiverConfig.err.needSealSignature": "根据【盖章并签字】的签署要求，同一文件中的签署位置需同时设置【盖章】和【盖章人签字】", "templateReceiverConfig.err.needSealTip": "盖章的签署人在文档上存在其他字段（签署日期或签署人填写字段），唯独缺了印章。你可以删除其他字段，或者设置盖章处", "templateReceiverConfig.err.needSignatureTip": "签字的签署人在文档上存在其他字段（签署日期或签署人填写字段），唯独缺了签名。你可以删除其他字段，或者设置签字处", "labelLackTip.everyDocSign": "签署人需要在每份文件都设置签署位置才能查看、参与签署该文件", "labelEdit.importantNotice": "温馨提示", "labelEdit.tipInfo": "建议将签署人配置成多个签约角色，让每个角色对应一个盖章处。", "labelEdit.tipInfos.title1": "通常情况：", "labelEdit.tipInfos.con1": "同一“签约角色”在合同上的多个盖章处会默认使用同一章。虽然可以手动更改每个盖章处的章，但这可能导致疏忽。", "labelEdit.tipInfos.title2": "推荐做法：", "labelEdit.tipInfos.con2": "为确保准确性，建议将签署人配置为多个签约角色，每个角色对应一种章。这样，每次签署时，签署人需单独确认此次签署所使用的章，从而减少混淆。", "labelEdit.tipInfos.title3": "特殊情况：", "labelEdit.tipInfos.con3": "如果所有签署位置确实需要使用同一种章，那么维持一个签约角色对应多个盖章处是合适的。", "labelEdit.detailedInstructions": "详细说明", "labelEdit.operationDiagram": "操作示意图", "templatePermission.table.searchPlaceholder": "输入账号查询模板权限", "permissionRight.dialog.rightName": "权限名称", "permissionRight.dialog.recycleRight": "如何收回权限", "permissionRight.dialog.moreTip": "更多说明", "permissionRight.dialog.recycleByAccount": "按账号{account}取消权限", "permissionRight.dialog.recycleByRole": "从角色{role}中移除该账号（需进入控制台操作）", "permissionRight.dialog.sendTip1": "可为以下企业发送合同：", "permissionRight.dialog.sendTip2": "来自模板的直接授权：{ents}", "permissionRight.dialog.sendTip3": "来自集团管理权限的授权：{ents}", "permissionRight.dialog.grantManage": "当一个模板所有人都没有“权限分配”的权限时，系统会自动赋予主管理员该权限并且不可被收回（只有当有更多人持有“权限分配”时，才可以从主管理员收回“权限分配”权限）。", "permissionRight.dialog.roleAffect": "操作角色会影响{entName}中以下账号的模板权限", "permissionRight.dialog.noRight": "当前账号未获得此模板的任何权限", "permissionRight.dialog.more": "等{count}家企业", "agent.riskJudgement": "AI律师", "agent.uploadText": "请上传需要进行风险判断的文件", "agent.startTips": "现在我们可以开始判断风险了", "agent.feedback": "问卷反馈", "agent.satisfy": "对分析结果满意，继续下一项分析", "agent.dissatisfy": "对分析结果不满意，重新进行分析", "agent.custom": "请输入自定义审查规则", "agent.submit": "发送", "agent.others": "其他", "agent.autoExtract": "自动进行下一步提取直到提取结束", "agent.autoRisk": "自动进行下一步分析直到分析结束", "agent.aiGenerated": "以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。", "agent.extractTitle": "信息提取", "agent.riskTitle": "AI律师", "agent.deepInference": "AI律师（深度推理）", "agent.chooseRisk": "请选择需要进行分析的文件", "agent.chooseExtract": "请选择需要进行提取的文件", "agent.analyzing": "内容分析中", "agent.advice": "修改建议生成中", "agent.options": "选项生成中", "agent.selectFunc": "选择功能", "agent.inputTips": "请输入确切内容", "filter.yes": "是", "agent.deepThinking": "深度思考中", "agent.deepThoughtCompleted": "已深度思考", "agent.original": "原文", "agent.revision": "修改建议", "agent.diff": "对比", "charge.packagePurchaseTitle": "【{title}功能】套餐购买", "charge.payOnce": "特惠限购一次", "charge.payNow": "立即购买", "charge.amount": "数目", "charge.unitPrice": "单价", "charge.limitTime": "有效期", "charge.copy": "份", "charge.month": "月", "charge.compareInfo1": "使用说明：", "charge.compareInfo2": "{index}、购买的{type}可用{per}数，对应企业所有成员均可使用，如你仅需个人使用，可在右上角登录主体切换到个人账号；", "charge.compareInfo3": "{index}、按上传的合同{per}数统计用量", "charge.codePay": "请用扫码支付", "charge.aliPay": "支付宝支付", "charge.wxPay": "微信支付", "charge.paySuccess": "购买成功", "charge.payIno": "开通功能 | 购买对象 | 支付金额", "charge.contactUs": "开通功能 | 购买对象 | 支付金额"}