// 指定位置文档相关mixin
import { scrollToYSmooth } from 'pub-utils/dom.js';
import { mapGetters, mapMutations, mapState } from 'vuex';
import { updateLabel } from 'src/api/template/pointPosition.js';
import { upSameLabelMixin } from './upSameLabel.js';
import { textLabelInfo } from 'utils/labelStyle';
import { labelCoordinateReversal } from 'utils/labelCoordinates';
export const pointPositionMixin = {
    mixins: [upSameLabelMixin],
    data() {
        return {
            dragStatus: 'end',
            // flyingIcon
            float: {
                show: false,
                type: '',
                class: '',
                // ========
                name: '',
                receiverFill: false,
                fontSize: 14,
                necessary: true,
                items: null,
            },
            findSignLabelTimes: 0, // 已查找签名、印章的次数，用来计算下次跳转的时的字段
        };
    },
    computed: {
        ...mapState('template', ['docList', 'currentDocIndex', 'currentPageIndex', 'receivers', 'zoom', 'templateStatus']),
        ...mapGetters('template', ['currentDoc']),
    },
    methods: {
        ...mapMutations('template', [
            'setBlankDocument', 'setEditors', 'setReceivers', 'setDocList', 'setWatermarkList', 'setRidingSealList', 'setFocusLabelOpt',   'setCurrentDocIndex', 'setCurrentPageIndex', 'setTemplateStatus', 'resetTemplatePosition']),
        changeDocById(id) {
            const index = this.docList.findIndex(doc => doc.documentId === id);
            this.changeDoc(index);
        },
        // 左侧标签拖拽
        onDragStart(e, type, className, name, receiverFill = false, fontSize = 14, necessary = true, labelMetaId = '0', items = null, labelExtends = {}) {
            setTimeout(() => {
                this.dragStatus = 'started';
                this.followCursorWithIcon(e);
                this.float.show = true;
                this.float.type = type;
                this.float.class = className;
                this.float.name = name;
                this.float.receiverFill = receiverFill;
                this.float.fontSize = fontSize;
                this.float.necessary = necessary;
                this.float.items = items;
                this.float.refBizFieldId = labelMetaId;
                this.float.labelExtends = labelExtends;
            }, 150);
        },
        // 标签移动跟随的图标
        onDragMove(e) {
            if (this.dragStatus === 'started') {
                if (e.target.closest('.point-position-doc-page-con')) {
                    this.float.show = false;
                } else {
                    this.followCursorWithIcon(e);
                    this.float.show = true;
                }
            }
        },
        // 标签停止拖拽，隐藏跟随图标
        onDragEnd() {
            this.dragStatus = 'end';
            this.float.show = false;
            this.float.class = '';
        },
        // 跟随的标签图标
        followCursorWithIcon(e) {
            document.querySelector('#flying-icon').style.left = `${e.clientX + 1}px`;
            document.querySelector('#flying-icon').style.top = `${e.clientY + 1}px`;
        },
        getPagePosition(index) {
            const docPages = document.querySelectorAll('.point-position-doc-pages')[0].children;
            const initY = document.querySelector('.point-position-doc-wrapper').offsetTop;
            const y = docPages[index].offsetTop * this.zoom + initY;
            return y;
        },
        nextSignPosition() {
            const $scrollEl = document.querySelector('.point-position-doc-list');
            const signLabels = this.currentDoc.labels.filter(label => ['SEAL', 'SIGNATURE'].includes(label.labelType));
            if (this.findSignLabelTimes >= signLabels.length) { // 跳转到当前文档最后一个时，回到第一个
                this.findSignLabelTimes = 0;
            }
            const targetLabel = signLabels[this.findSignLabelTimes] || {};
            if (!targetLabel.labelId) {
                return;
            }
            const lableOffset = document.getElementsByClassName(targetLabel.labelId)[0].offsetTop;
            const pageY = this.getPagePosition(targetLabel.pageNumber - 1);
            const GAP_HEIGHT = document.getElementsByClassName('point-position-doc-page-footer')[0].clientHeight * this.zoom; // 页脚高度
            const ROLE_INFO_OFFSET_FIX = 30;
            // 减去页脚高度以保证滚动位置正确，30px为了保证章的签署人信息显示出来
            scrollToYSmooth($scrollEl, Math.max(pageY + lableOffset + GAP_HEIGHT - ROLE_INFO_OFFSET_FIX, 0), 400, 'ease-out');
            this.findSignLabelTimes = this.findSignLabelTimes + 1;
        },
        // 右侧修改页数
        changePage(index) {
            const $scrollEl = document.querySelector('.point-position-doc-list');
            const y = this.getPagePosition(index);
            scrollToYSmooth($scrollEl, y, 400, 'ease-out');
            setTimeout(() => {
                this.setCurrentPageIndex(index);
            }, 400);
        },
        // 切换文档
        changeDoc(docIndex) {
            this.setCurrentDocIndex(docIndex);
            this.findSignLabelTimes = 0;
            this.$nextTick(() => {
                setTimeout(() => {
                    document.querySelector('.point-position-doc-list').scrollTo(0, 0);
                }, 400);
            });
        },
        toBack() {
            this.$router.back(-1);
        },
        // 存在不同类型的同名字段
        hasDiffType(newItem) {
            let index = -1;
            for (let i = 0; i < this.docList.length; i++) {
                index =  this.docList[i].labels.findIndex(label => label.name === newItem.name && label.type !== newItem.type && newItem.labelId !== label.labelId);
                if (index > -1) {
                    break;
                }
            }
            return index > -1;
        },
        // 编辑区保存标签修改
        async onMetaSave(label, type, nextK, metaData) {
            // 修改签署位置同步不return掉，实时保存
            if (nextK === 'ap') { // 修改签署位置同步时，前端临时存储notSignPosSync标记
                // 保存后由于labelid变了，取消聚焦
                this.setFocusLabelOpt({
                    labelId: '',
                    labelButtonInd: -1,
                });
                // const labelList = this.currentDoc.labels;
                // const oldLabelInd = labelList.findIndex(a => a.labelId === label.labelId);
                // this.$set(labelList, oldLabelInd, {
                //     ...label,
                //     ...metaData,
                // });
                // return;
            }
            // 如果是签署位置不同步，则增加index参数-合同索引，前者为前端临时标记，后者为后端数据标记
            if (label.notSignPosSync || label.labelExtends?.ap === 1) {
                metaData.index = this.idxInBatch;
            }
            // 不同类型的业务字段是不允许同名
            if (this.hasDiffType({
                ...label,
                ...metaData,
            })) {
                return this.$MessageToast.error(this.$t('pointPosition.hasSameLabelTip')); // '已存在不同类型的同名字段'
            }
            // 是否位复选框，单选框
            const isCheckBox = ['MULTIPLE_BOX', 'SINGLE_BOX', 'CONFIRMATION_REQUEST_REMARK'].includes(label.labelType);
            if (isCheckBox && type === 'labelName') { // 复选框，单选框修改名称不能同名
                const maxNum = label.labelName === type ? 1 : 0;
                if (this.getSameNameMarksNum(metaData, label) > maxNum) {
                    this.$MessageToast.error(this.$t('pointPosition.needChangeNameTip')); // '已存在同名字段，请修改名称' 复选框，单选框不能重名
                    return;
                }
            }
            // 日期样式修改之后需要同步更新字段宽度 SAAS-14898
            if (nextK === 'dateFieldFormat') {
                label.labelPosition.width = this.updateDateLabelWidth(label, metaData);
                label.labelPosition.x = Math.min(label.labelPosition.x, 1 - label.labelPosition.width);
            }
            const params = {
                ...label,
                documentId: this.currentDoc.documentId,
                ...metaData,
            };
            updateLabel(params).then(({ data }) => {
                // 重新聚焦，保证focus的拖拽marquee位置正确
                if (nextK === 'dateFieldFormat') {
                    this.setFocusLabelOpt({
                        labelId: label.labelId,
                        labelButtonInd: this.currentDoc.labels.findIndex(a => a.labelId === label.labelId),
                    });
                }
                if (data.length === 1) { // 只有当前标签需要更新时
                    const newLabel = data[0];
                    const labelList = this.currentDoc.labels;
                    const oldLabelInd = labelList.findIndex(a => a.labelId === label.labelId);
                    this.$set(labelList, oldLabelInd, newLabel);
                } else if (data.length > 1) {
                    this.upSameLabel(data); // 需要更新同名字段
                }
            }).catch(() => {});
        },
        // 切换日期格式时，修正日期字段宽度
        updateDateLabelWidth(label, metaData) {
            const { width: pageWidth } = this.currentDoc.documentPages[this.currentPageIndex];
            const labelData = {
                type: label.labelType,
                fontSize: label.labelExtends.pxFontSize,
                name: label.labelName,
                ...metaData,
            };
            const { width } = textLabelInfo(labelData);
            return Math.round(width) / pageWidth;
        },
        // 同类型的不能同名（单复选框）
        getSameNameMarksNum(metaData, label) {
            let len = -1;
            let list;
            for (let i = 0; i < this.docList.length; i++) {
                list =  this.docList[i].labels.filter((a) => a.type === label.labelType && a.labelName === metaData[Object.keys(metaData)[1]]);
                len = len + list.length;
            }
            return len;
        },
        initDoc(resData) {
            resData.forEach((doc) => {
                const dateStr = `&_t=${new Date().getTime()}`; // CFD-9324 避免图片展示缓存
                doc.documentPages.forEach((page) => {
                    page.documentName = doc.documentName;
                    if (doc.blankDocument) {
                        this.setBlankDocument(true);
                    }
                    page.imagePreviewUrl = this.$hybrid.getContractImg(page.imagePreviewUrl) + dateStr;
                    page.highQualityPreviewUrl = this.$hybrid.getContractImg(page.highQualityPreviewUrl) + dateStr;
                });
                doc.image = doc.documentPages[0].imagePreviewUrl; // 缩略图只需 第一页
                doc.labels.forEach(label => {
                    // 老的坐标为数值，转换为百分比
                    const labelPosition  = labelCoordinateReversal(
                        label,
                        doc.documentPages[label.pageNumber - 1].width,
                        doc.documentPages[label.pageNumber - 1].height,
                    );
                    label.labelPosition = labelPosition;
                    label.documentId = doc.documentId;
                });
            });
            return resData;
        },
    },
    beforeMount() {
        document.addEventListener('mousemove', this.onDragMove);
        document.addEventListener('mouseup', this.onDragEnd);
    },
    beforeDestroy() {
        document.removeEventListener('mousemove', this.onDragMove);
        document.removeEventListener('mouseup', this.onDragEnd);
        this.resetTemplatePosition(); // 重置指定签署位置相关数据
    },
};
