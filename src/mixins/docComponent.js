import SignOrder from 'components/signOrder';
import SlidePop from 'pub-components/slidePop/index.vue';
import SlideModelContent from 'src/views/docManage/Detail/SlideModelContent';
import Gallery from 'components/gallery/index.vue';
import { download } from 'src/utils/download.js';
import DownloadAttachmentDialog from 'components/downloadAttachmentDialog/index.vue';
export const docComponentMixin = {
    components: {
        SignOrder,
        SlidePop,
        SlideModelContent,
        Gallery,
        DownloadAttachmentDialog,
    },
    data() {
        return {
            signOrderDialog: {
                visible: false,
            },
            popShow: {
                show: false,
            },
            previewImgInfo: {
                visible: false,
                fileName: '',
                previewUrl: '',
            },
            downloadAttachmentDialogVisible: false,
            fileType: '',
            downloadUrl: '',
        };
    },
    methods: {
        // 显示签署顺序
        handleShowSignOrder() {
            this.signOrderDialog.visible = true;
        },
        popShowChange(options) {
            this.popShow = options;
        },
        // 查看附件图片
        openPrivateLetterImg(item) {
            this.previewImgInfo = {
                visible: true,
                fileName: item.fileName,
                previewUrl: item.previewUrls,
            };
        },
        // 更新签署方的签约须知信息
        updatePrivateMessage(communicateInfo, receiverId) {
            const index = this.contractBaseInfo.singers.findIndex(item => item.receiverId === receiverId);
            this.$set(this.contractBaseInfo.singers.communicateInfo, index, communicateInfo);
        },
        // 查看合同附属资料图片
        previewAttachment(item) {
            this.previewImgInfo = {
                visible: true,
                fileName: item.name,
                previewUrl: item.fileLists.map(e => `${e.previewUrl}`),
            };
        },
        handleDownloadAttachment(type, downloadUrl) {
            this.fileType = type;
            this.downloadUrl = downloadUrl;
            this.downloadAttachmentDialogVisible = true;
        },
        downloadAttachment() {
            download(this.downloadUrl);
        },
    },
};
