import Vue from 'vue';

/**
 * @desc 上传空白文档
 * @param {String} templateId 模板id
 * @param {String} contractTitle 空白文档标题
 * */
export function postBlanckDoc(templateId, contractTitle) {
    return Vue.$http.post(`/template-api/v2/templates/${templateId}/document/blank-document`, {
        contractTitle,
    });
}

/**
 * @desc 查询模板配置：场景定制等
 * @param {String} queryStr 拼接了draftId/templateId的query
 * */
export function getTemplateConfig(queryStr) {
    return Vue.$http.get(`/template-api/edit-permission/detail?${queryStr}`);
}

/**
 * @desc 查询模板签约角色数据
 * @param {String} templateId
 * */
export function getTemplateReceiverRoles(templateId) {
    return Vue.$http.get(`/template-api/v2/custom-scene/roles-for-following?templateId=${templateId}`);
}
/**
 * @desc 生成发送码
 * @param {String} templateId
 * */
export function generateSendCode(templateId) {
    return Vue.$http.post(`/template-api/v2/template/${templateId}/generate-send-code`, {});
}
/**
 * @desc 关闭发送码
 * @param {String} templateId
 * */
export function closeSendCode(templateId) {
    return Vue.$http.post(`/template-api/v2/template/
    ${templateId}/send-code/close`, {});
}

/**
 * @desc 查询模板的'权限管理'试用数据
 * @param {String} templateId
 * */
export function getTemplateAdvancedFunctionData(templateId) {
    return Vue.$http.get(`/template-api/advanced-function/${templateId}/template-licensing`);
}

/**
 * @desc 设置模板有效日期
 * @param {String} templateId 模板id
 * @param {Number} expireDays 有效期时常天数，-1代表永久有效
 * */
export function postTemplateExpire(templateId, expireDays) {
    return Vue.$http.put(`/template-api/v2/templates/${templateId}/expire`, { expireDays });
}

/**
 * @desc 查询集团角色的'业务管理'权限
 * */
export function getGroupSignManagePrivillage() {
    return Vue.$http.get(`/ents/currentManagementRoles`);
}

/**
 * @desc 开启/关闭印章推荐开关
 * @param {String} templateId 模板id
 * @param {Boolean} useStampRecommendation 是否启用印章推荐
 * */
export function toggleStampRecommendationSwitch(templateId, useStampRecommendation) {
    return Vue.$http.post(`/template-api/templates/${templateId}/stamp-recommendation/switch?useStampRecommendation=${useStampRecommendation}`);
}

/**
 * @desc 查询拖章规则
 * @param {String} templateId 模板id
 * */
export function getStampRecommendationRule(templateId) {
    return Vue.$http.get(`/template-api/templates/${templateId}/stamp-recommendation/rule`);
}

/**
 * @desc 保存拖章规则
 * @param {String} templateId 模板id
 * @param {Object} ruleConfig 规则配置对象
 * @param {Array} ruleConfig.stampStrategies 印章策略数组
 * @param {Boolean} ruleConfig.useStampRecommendation 是否启用印章推荐
 * */
export function saveStampRecommendationRule(templateId, ruleConfig) {
    return Vue.$http.post(`/template-api/templates/${templateId}/stamp-recommendation/rule/save`, ruleConfig);
}

/**
 * @desc 提交拖章任务
 * @param {String} draftId 草稿id
 * */
export function startStampRecommendationTask(draftId) {
    return Vue.$http.post(`/template-api/draft/${draftId}/start-stamp-recommendation-task`);
}

/**
 * @desc 查询拖章任务状态
 * @param {String} draftId 草稿id
 * @param {String} taskId 任务id
 * */
export function getStampRecommendationTaskStatus(draftId, taskId) {
    return Vue.$http.get(`/template-api/draft/${draftId}/task/${taskId}/stamp-recommendation-task-status`);
}
