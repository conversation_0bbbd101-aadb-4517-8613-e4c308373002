/*
 * @Author: fan_liu
 * @Date: 2020-04-21 17:36:08
 * @LastEditors: Fan_<PERSON>
 * @LastEditTime: 2022-10-28 14:37:41
 * @Description: Do not edit
 */
const resRules = {
    /**
     * 密码的校验规则: 6-18位的数字，字母，或者数字字母组合, 支持特殊字符
     * 支持的特殊字符有 中英文符号
     */
    pass: /^[a-zA-Z0-9 _`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？～「」\-·¥《》"\\]{8,18}$/,
    jaPass: /^[a-zA-Z0-9]{6,18}$/, // 6-18位的数字，字母，或者数字字母组合, 密码的校验规则 日文版密码
    onlyNumber: /^\d{1,}$/, // 纯数字判断，至少1位数字，上面的会放过空字符串 日文版正则
    signpass: /^[0-9]{6}$/, // 6位的数字
    companyName: /^.{1,60}$/,
    userName: /^(([a-zA-Z\s\·]{2,35})|([\u0400-\u04FF\s\·]{2,35})|([\u4e00-\u9fa5\u4dae\uE863\s\·]{2,35})|(((?![\u3000-\u303F])[\u2E80-\uFE4F]{2,35}|\·)*(?![\u3000-\u303F])[\u2E80-\uFE4F]{2,35}(\·)*))$/, // 不能是英文和中文(俄文)的组合，不能有标点，可以有空格和原点，不区分大小写，最长不超过32个字符
    foreignerUserName: /^(([a-zA-Z\s\·\-\,\，]{2,64})|([\u0400-\u04FF\s\·\-\,\，]{2,64})|([\u4e00-\u9fa5\u4dae\uE863\s\·\-\,\，]{2,64})|(((?![\u3000-\u303F])[\u2E80-\uFE4F\-]{2,64}|\·\-\,\，)*(?![\u3000-\u303F])[\u2E80-\uFE4F\-\,\，]{2,64}(\·)*))$/, // 和userName相比,多了：外国人实名允许填写逗号，（包括中英文）和短横杠- SAAS-12562
    foreignerUserNameIgnore: /^(([a-zA-Z\s\·\-\,\，\*]{2,64})|([\u0400-\u04FF\s\·\-\,\，\*]{2,64})|([\u4e00-\u9fa5\u4dae\uE863\s\·\-\,\，\*]{2,64})|(((?![\u3000-\u303F])[\u2E80-\uFE4F\-]{2,64}|\·\-\,\，\*)*(?![\u3000-\u303F])[\u2E80-\uFE4F\-\,\，\*]{2,64}(\·)*))$/, // 模板使用时中校验外国人名称规则
    userNameIgnore: /^(([a-zA-Z\s\·\*]{2,35})|([\u0400-\u04FF\s\·\*]{2,35})|([\u4e00-\u9fa5\u4dae\uE863\s\·\*]{2,35})|(((?![\u3000-\u303F])[\u2E80-\uFE4F\*]{2,35}|\·)*(?![\u3000-\u303F])[\u2E80-\uFE4F\*]{2,35}(\·)*))$/, // 允许*的用户名
    userNameJa: /^(([a-zA-Z\s\·\*]{2,35})|([\u0400-\u04FF\s\·\*]{2,35})|([\u0800-\u4e00\s\·\*]{2,35})|([\u4e00-\u9fa5\u4dae\uE863\s\·\*]{2,35})|(((?![\u3000-\u303F])[\u2E80-\uFE4F\*]{2,35}|\·)*(?![\u3000-\u303F])[\u2E80-\uFE4F\*]{2,35}(\·)*))$/, // 适配日文
    userAccount: /(^1[0-9]{10}$)|(^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-_]+(\.[a-zA-Z0-9-_]+)*\.[a-zA-Z0-9-_]{2,20}$)/,
    userPhone: /^1[0-9]{10}$/,
    userPhoneJa: /^[0-9]{10,11}$/,
    userEmail: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-_]+(\.[a-zA-Z0-9-_]+)*\.[a-zA-Z0-9-_]{2,20}$/,
    userEmailDomain: /^@[a-zA-Z0-9-_]+(\.[a-zA-Z0-9-_]+)*\.[a-zA-Z0-9-_]{2,20}$/, // 邮箱域名
    contractId: /^[0-9]{19}$/, // 合同Id、模板Id等19位数字
    phoneVerifyCode: /^\d{6}$/,
    imageVerifyCode: /^\d{4}$/,
    loginCode: /^[a-zA-Z0-9 _`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？～「」\-·¥《》"\\]{6,18}$/, // 6-18位的数字，字母，或者数字字母组合，支持特殊字符
    signCode: /^\d{6}$/, // 6位数字
    IDCardReg: /(^([1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2})$)/,
    weakIdCardReg: /^(\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x))$/, // 身份证号改为弱校验（身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X）
    numberLabelCode: /^[-\.\d]*[-\.\d]*[-\.\d]*$/, // 数字标签
    roleNameStr: /(^[\u4E00-\u9FA5A-Za-z0-9]+$)/, // 业务角色校验：中文、英文、数字
    fieldValueReg: /[()#\/\\]/, // 单复选框备选项不能输入\/()#特殊符号：CFD-6264、SAAS-12570
    complexPass: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9 _`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？～「」\-·¥《》"\\]{6,18}$/, // 和后端保持一致
    emojiReg: /\ud83c[\udf00-\udfff]|\ud83d[\udc00-\ude4f]|\ud83d[\ude80-\udeff]/g, // 匹配emoji
    maskIDCardReg: /^(.{3})(?:\w+)(.{3})$/, // 身份证号脱敏
    maskPhone: /(\d{3})\d*(\d{3})/, // 手机号脱敏
    numberDefault: (n) => n === 0 ? /^-?\d+$/ : new RegExp(`^-?\\d+\(\.\\d{1,${n}})?$`),
    corporateNumber: /^[0-9]{13}$/, // 13位的数字 法人番号
    jaTelePhoneOrPhone: /(^0[1-9]\d{8}$)|(^0[5,7,8,9]\d{9}$)/, // 日本固话或手机号码，详见 WWW-666；（日本固定电话，0开头，第2位不为0的10位数字）（日本手机号码，0开头，第2位为5,7,8,9的11位数字）
    pathAbsolute: /(https?|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/, // 绝对路径
};

export default resRules;
